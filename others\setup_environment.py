#!/usr/bin/env python3
"""
Environment Setup Script
Interactive setup for trading system environment configuration

Features:
🔧 1. Environment File Creation
- Create .env file from template
- Interactive credential input
- Validation and testing

📊 2. Configuration Validation
- Check all agent configurations
- Validate SmartAPI credentials
- Test notification services

🔒 3. Security Setup
- Generate secure keys
- Validate credential formats
- Set appropriate file permissions

🚀 4. Quick Start Guide
- Step-by-step setup instructions
- Common troubleshooting tips
- Agent initialization testing
"""

import os
import sys
import getpass
import secrets
import string
from pathlib import Path
from typing import Dict, Any, Optional

# Add utils to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_loader import ConfigurationLoader, check_environment_setup

class EnvironmentSetup:
    """Interactive environment setup for trading system"""
    
    def __init__(self):
        self.config_loader = ConfigurationLoader()
        self.env_vars = {}
        
    def welcome_message(self):
        """Display welcome message"""
        print("=" * 70)
        print("🚀 TRADING SYSTEM ENVIRONMENT SETUP")
        print("=" * 70)
        print()
        print("This script will help you set up your trading system environment.")
        print("You'll need:")
        print("  📊 Angel One SmartAPI credentials")
        print("  📱 Telegram bot token (optional)")
        print("  📧 Email settings (optional)")
        print()
        
    def check_existing_setup(self) -> bool:
        """Check if environment is already set up"""
        if os.path.exists(".env"):
            print("⚠️  Found existing .env file")
            response = input("Do you want to overwrite it? (y/n): ").lower()
            if response != 'y':
                print("Setup cancelled. Use existing .env file.")
                return False
        
        return True
    
    def create_env_from_template(self) -> bool:
        """Create .env file from template"""
        try:
            if not os.path.exists(".env.template"):
                print("❌ Template file .env.template not found")
                return False
            
            # Copy template
            with open(".env.template", 'r') as template:
                content = template.read()
            
            with open(".env", 'w') as env_file:
                env_file.write(content)
            
            print("✅ Created .env file from template")
            return True
            
        except Exception as e:
            print(f"❌ Error creating .env file: {e}")
            return False
    
    def collect_smartapi_credentials(self) -> Dict[str, str]:
        """Collect SmartAPI credentials interactively"""
        print("\n" + "=" * 50)
        print("📊 ANGEL ONE SMARTAPI CREDENTIALS")
        print("=" * 50)
        print("Get these from: https://smartapi.angelbroking.com/")
        print()
        
        credentials = {}
        
        # API Key
        credentials['SMARTAPI_API_KEY'] = input("Enter your API Key: ").strip()
        
        # Username (Client Code)
        credentials['SMARTAPI_USERNAME'] = input("Enter your Client Code: ").strip()
        
        # Password (PIN)
        credentials['SMARTAPI_PASSWORD'] = getpass.getpass("Enter your PIN: ").strip()
        
        # TOTP Token
        print("\nFor TOTP Token:")
        print("1. Scan QR code from Angel One app")
        print("2. Enter the token from your authenticator app")
        credentials['SMARTAPI_TOTP_TOKEN'] = input("Enter your TOTP Token: ").strip()
        
        return credentials
    
    def collect_telegram_credentials(self) -> Dict[str, str]:
        """Collect Telegram credentials interactively"""
        print("\n" + "=" * 50)
        print("📱 TELEGRAM NOTIFICATIONS (Optional)")
        print("=" * 50)
        
        use_telegram = input("Do you want to set up Telegram notifications? (y/n): ").lower()
        
        if use_telegram != 'y':
            return {}
        
        print("\nTo set up Telegram:")
        print("1. Message @BotFather on Telegram")
        print("2. Create a new bot with /newbot")
        print("3. Get your bot token")
        print("4. Start a chat with your bot")
        print("5. Get your chat ID from @userinfobot")
        print()
        
        credentials = {}
        credentials['TELEGRAM_BOT_TOKEN'] = input("Enter your Bot Token: ").strip()
        credentials['TELEGRAM_CHAT_ID'] = input("Enter your Chat ID: ").strip()
        
        return credentials
    
    def collect_email_credentials(self) -> Dict[str, str]:
        """Collect email credentials interactively"""
        print("\n" + "=" * 50)
        print("📧 EMAIL NOTIFICATIONS (Optional)")
        print("=" * 50)
        
        use_email = input("Do you want to set up email notifications? (y/n): ").lower()
        
        if use_email != 'y':
            return {}
        
        print("\nFor Gmail, use App Password instead of regular password")
        print("Enable 2FA and generate App Password from Google Account settings")
        print()
        
        credentials = {}
        credentials['EMAIL_SMTP_SERVER'] = input("SMTP Server (default: smtp.gmail.com): ").strip() or "smtp.gmail.com"
        credentials['EMAIL_SMTP_PORT'] = input("SMTP Port (default: 587): ").strip() or "587"
        credentials['EMAIL_USERNAME'] = input("Email Username: ").strip()
        credentials['EMAIL_PASSWORD'] = getpass.getpass("Email Password/App Password: ").strip()
        credentials['EMAIL_FROM'] = input("From Email: ").strip()
        credentials['EMAIL_TO'] = input("To Email (for alerts): ").strip()
        
        return credentials
    
    def generate_security_keys(self) -> Dict[str, str]:
        """Generate security keys"""
        print("\n" + "=" * 50)
        print("🔒 GENERATING SECURITY KEYS")
        print("=" * 50)
        
        # Generate encryption key
        encryption_key = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
        
        # Generate JWT secret
        jwt_secret = ''.join(secrets.choice(string.ascii_letters + string.digits + string.punctuation) for _ in range(64))
        
        # Generate webhook secret
        webhook_secret = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
        
        print("✅ Generated security keys")
        
        return {
            'ENCRYPTION_KEY': encryption_key,
            'JWT_SECRET': jwt_secret,
            'WEBHOOK_SECRET': webhook_secret
        }
    
    def set_environment_defaults(self) -> Dict[str, str]:
        """Set default environment values"""
        return {
            'TRADING_ENVIRONMENT': 'development',
            'DEBUG_MODE': 'true',
            'LOG_LEVEL': 'INFO',
            'TEST_MODE': 'false',
            'PAPER_TRADING_ENABLED': 'true',
            'MOCK_DATA_ENABLED': 'false',
            'WEBHOOK_PORT': '8080'
        }
    
    def update_env_file(self, credentials: Dict[str, str]):
        """Update .env file with collected credentials"""
        try:
            # Read current .env file
            with open(".env", 'r') as file:
                lines = file.readlines()
            
            # Update lines with new values
            updated_lines = []
            for line in lines:
                if '=' in line and not line.strip().startswith('#'):
                    key = line.split('=')[0].strip()
                    if key in credentials:
                        updated_lines.append(f"{key}={credentials[key]}\n")
                    else:
                        updated_lines.append(line)
                else:
                    updated_lines.append(line)
            
            # Write updated content
            with open(".env", 'w') as file:
                file.writelines(updated_lines)
            
            print("✅ Updated .env file with your credentials")
            
        except Exception as e:
            print(f"❌ Error updating .env file: {e}")
    
    def validate_setup(self) -> bool:
        """Validate the setup"""
        print("\n" + "=" * 50)
        print("🔍 VALIDATING SETUP")
        print("=" * 50)
        
        # Reload configuration loader to pick up new environment
        self.config_loader = ConfigurationLoader()
        
        # Check environment setup
        if not check_environment_setup():
            print("❌ Environment validation failed")
            return False
        
        # Test agent configurations
        agents = ['market_monitoring', 'signal_generation', 'risk_management', 'execution']
        
        for agent in agents:
            try:
                config_path = f"config/{agent}_config.yaml"
                if os.path.exists(config_path):
                    config = self.config_loader.load_agent_config(config_path, agent)
                    validation_result = self.config_loader.validate_agent_config(config, agent)
                    
                    if validation_result.is_valid:
                        print(f"✅ {agent.replace('_', ' ').title()} Agent configuration valid")
                    else:
                        print(f"⚠️  {agent.replace('_', ' ').title()} Agent has warnings:")
                        for warning in validation_result.warnings:
                            print(f"   - {warning}")
                else:
                    print(f"⚠️  {agent.replace('_', ' ').title()} Agent config not found")
                    
            except Exception as e:
                print(f"❌ Error validating {agent} agent: {e}")
        
        print("\n✅ Setup validation completed")
        return True
    
    def display_next_steps(self):
        """Display next steps after setup"""
        print("\n" + "=" * 70)
        print("🎉 SETUP COMPLETED!")
        print("=" * 70)
        print()
        print("Next steps:")
        print("1. 🧪 Test your setup:")
        print("   python -c \"from utils.config_loader import check_environment_setup; check_environment_setup()\"")
        print()
        print("2. 🚀 Run individual agents:")
        print("   python agents/run_market_monitoring.py")
        print("   python agents/run_execution_agent.py --demo")
        print()
        print("3. 📊 Run backtesting:")
        print("   python agents/run_enhanced_backtesting.py")
        print()
        print("4. 🔧 Customize configurations:")
        print("   Edit files in config/ directory as needed")
        print()
        print("5. 📚 Read documentation:")
        print("   Check docs/ directory for detailed guides")
        print()
        print("⚠️  IMPORTANT:")
        print("- Start with paper trading (PAPER_TRADING_ENABLED=true)")
        print("- Test thoroughly before live trading")
        print("- Keep your .env file secure and never commit it")
        print()
    
    def run_setup(self):
        """Run the complete setup process"""
        try:
            self.welcome_message()
            
            if not self.check_existing_setup():
                return
            
            # Create .env from template
            if not self.create_env_from_template():
                return
            
            # Collect all credentials
            all_credentials = {}
            
            # SmartAPI credentials (required)
            smartapi_creds = self.collect_smartapi_credentials()
            all_credentials.update(smartapi_creds)
            
            # Telegram credentials (optional)
            telegram_creds = self.collect_telegram_credentials()
            all_credentials.update(telegram_creds)
            
            # Email credentials (optional)
            email_creds = self.collect_email_credentials()
            all_credentials.update(email_creds)
            
            # Security keys
            security_keys = self.generate_security_keys()
            all_credentials.update(security_keys)
            
            # Environment defaults
            env_defaults = self.set_environment_defaults()
            all_credentials.update(env_defaults)
            
            # Update .env file
            self.update_env_file(all_credentials)
            
            # Validate setup
            self.validate_setup()
            
            # Display next steps
            self.display_next_steps()
            
        except KeyboardInterrupt:
            print("\n\n❌ Setup cancelled by user")
        except Exception as e:
            print(f"\n❌ Setup failed: {e}")

def main():
    """Main function"""
    setup = EnvironmentSetup()
    setup.run_setup()

if __name__ == "__main__":
    main()
