# 🚀 WebSocket Connection Solutions & Dynamic Stock Selection Guide

## 📋 Overview

This guide provides comprehensive solutions for:
1. **WebSocket Connection Issues** - Diagnosing and fixing connection failures
2. **Dynamic Stock Selection Workflow** - Intelligent stock selection based on market conditions

## 🔍 Problem Analysis

### WebSocket Connection Issues
The error `[INFO] WebSocket not connected, storing symbols for later subscription` indicates:

**Common Causes:**
- ❌ Authentication failures (invalid tokens)
- ❌ Network connectivity issues (firewall/proxy)
- ❌ SmartAPI rate limiting (max 3 concurrent connections)
- ❌ Threading conflicts in async/sync operations
- ❌ Invalid subscription parameters

### Current Workflow Limitations
- Static stock selection instead of dynamic market-based selection
- No market timing consideration
- Limited error handling and recovery

## 🛠️ Solutions Implemented

### 1. WebSocket Diagnostics Tool
**File:** `utils/websocket_diagnostics.py`

**Features:**
- ✅ Environment validation (API keys, TOTP tokens)
- ✅ Network connectivity testing
- ✅ SmartAPI authentication verification
- ✅ WebSocket connection testing
- ✅ Automated troubleshooting recommendations

### 2. Robust WebSocket Manager
**File:** `utils/robust_websocket_manager.py`

**Features:**
- ✅ Enhanced authentication with retry logic
- ✅ Connection state management
- ✅ Exponential backoff reconnection
- ✅ Thread-safe operations
- ✅ Comprehensive error handling
- ✅ Connection metrics and monitoring

### 3. Dynamic Stock Selection Workflow
**File:** `utils/dynamic_stock_selection_workflow.py`

**Features:**
- ✅ Market timing detection (pre-market, active, closing)
- ✅ Initial monitoring of 500 stocks
- ✅ AI-driven market condition analysis
- ✅ Dynamic stock selection based on conditions
- ✅ Automatic WebSocket switching to selected stocks
- ✅ Periodic reselection based on market changes

## 🚀 Quick Start

### Step 1: Run Diagnostics
```bash
python run_websocket_diagnostics.py
```

This will:
- Test your environment setup
- Verify SmartAPI credentials
- Check network connectivity
- Test WebSocket connections
- Provide specific recommendations

### Step 2: Fix Any Issues
Based on diagnostics output, common fixes:

**Authentication Issues:**
```bash
# Verify environment variables
echo $SMARTAPI_API_KEY
echo $SMARTAPI_USERNAME
echo $SMARTAPI_PASSWORD
echo $SMARTAPI_TOTP_TOKEN

# Check TOTP token format (should be 32 characters)
python -c "import pyotp; print(pyotp.TOTP('YOUR_TOTP_TOKEN').now())"
```

**Network Issues:**
- Check firewall settings for port 443
- Verify proxy settings if applicable
- Test direct internet connectivity

### Step 3: Use Enhanced Solutions

#### Option A: Replace Existing WebSocket Manager
```python
from utils.robust_websocket_manager import RobustWebSocketManager

# Configuration
config = {
    'max_retry_attempts': 5,
    'retry_delay_base': 2,
    'connection_timeout': 15,
    'heartbeat_interval': 30
}

# Initialize
manager = RobustWebSocketManager(config)

# Set callbacks
manager.set_callbacks(
    on_connected=lambda: print("Connected!"),
    on_data=lambda data: process_market_data(data),
    on_error=lambda error: print(f"Error: {error}")
)

# Connect and subscribe
await manager.connect()
await manager.subscribe_symbols(['RELIANCE', 'TCS', 'INFY'])
```

#### Option B: Use Dynamic Workflow
```python
from utils.dynamic_stock_selection_workflow import DynamicStockSelectionWorkflow

# Configuration
config = {
    'websocket': {
        'max_retry_attempts': 5,
        'connection_timeout': 15
    },
    'initial_monitoring_minutes': 30,
    'reselection_interval_hours': 2,
    'selection_criteria': {
        'max_stocks': 20,
        'min_market_cap': 'Mid'
    }
}

# Initialize workflow
workflow = DynamicStockSelectionWorkflow(config)

# Set callbacks
workflow.set_callbacks(
    on_stocks_selected=lambda stocks, condition: handle_selection(stocks),
    on_market_data=lambda data: process_data(data)
)

# Start workflow
await workflow.start_workflow()
```

## 🎯 Dynamic Workflow Process

### Phase 1: Market Detection
- Detects current market phase (pre-market, active, closing)
- Waits for market open if necessary
- Handles weekend/holiday detection

### Phase 2: Initial Monitoring (30 minutes)
- Connects to WebSocket
- Subscribes to top 500 liquid stocks
- Collects real-time market data
- Analyzes market breadth and conditions

### Phase 3: Market Analysis
- Volatility assessment (low/medium/high)
- Trend direction detection (bullish/bearish/sideways)
- Volume profile analysis
- Sector rotation identification
- Market breadth calculation

### Phase 4: Stock Selection
- Applies dynamic criteria based on market conditions
- Considers sector rotation and momentum
- Ensures diversification across sectors
- Selects optimal 15-20 stocks

### Phase 5: Focused Monitoring
- Disconnects from 500-stock monitoring
- Reconnects with selected stocks only
- Continues monitoring for 2 hours
- Repeats cycle for reselection

## 🔧 Integration with Existing Code

### Update Market Monitoring Agent
```python
# In agents/market_monitoring_agent.py
from utils.robust_websocket_manager import RobustWebSocketManager

class MarketMonitoringAgent:
    def __init__(self, config):
        # Replace existing WebSocket setup
        self.websocket_manager = RobustWebSocketManager(config.get('websocket', {}))
        
    async def start_monitoring(self):
        # Use enhanced connection
        if await self.websocket_manager.connect():
            await self.websocket_manager.subscribe_symbols(self.symbols)
```

### Update Continuous Trading
```python
# In run_continuous_live_trading.py
from utils.dynamic_stock_selection_workflow import DynamicStockSelectionWorkflow

class ContinuousTrading:
    async def start_trading(self):
        # Use dynamic workflow instead of static selection
        workflow = DynamicStockSelectionWorkflow(self.config)
        await workflow.start_workflow()
```

## 📊 Monitoring and Metrics

### Connection Metrics
```python
status = manager.get_connection_status()
print(f"State: {status['state']}")
print(f"Connection attempts: {status['metrics']['connection_attempts']}")
print(f"Messages received: {status['metrics']['total_messages_received']}")
```

### Workflow Status
```python
status = workflow.get_workflow_status()
print(f"Market phase: {status['current_phase']}")
print(f"Selected stocks: {status['selected_stocks_count']}")
print(f"Current stocks: {status['selected_stocks']}")
```

## 🚨 Troubleshooting

### Common Issues & Solutions

**1. Authentication Failures**
```
Error: Invalid Token
Solution: Regenerate TOTP token, check API credentials
```

**2. Connection Timeouts**
```
Error: Connection timeout after 15 seconds
Solution: Check firewall, try different network, verify market hours
```

**3. Subscription Failures**
```
Error: No valid tokens found for symbols
Solution: Verify symbol format, check exchange mapping
```

**4. Rate Limiting**
```
Error: Too many connections
Solution: Ensure max 3 WebSocket connections, close unused connections
```

## 📈 Performance Optimization

### Best Practices
- Use connection pooling for multiple agents
- Implement circuit breaker pattern for failures
- Monitor connection health with heartbeats
- Use efficient data structures for tick storage
- Implement proper error recovery mechanisms

### Resource Management
- Limit concurrent WebSocket connections (max 3)
- Use memory-efficient data buffers
- Implement proper cleanup on shutdown
- Monitor memory usage for large datasets

## 🔄 Next Steps

1. **Run diagnostics** to identify current issues
2. **Implement enhanced WebSocket manager** for better reliability
3. **Deploy dynamic workflow** for intelligent stock selection
4. **Monitor performance** and adjust parameters
5. **Scale gradually** from test to production

## 📞 Support

If you encounter issues:
1. Check the diagnostics output first
2. Review the generated log files
3. Verify environment variables are set correctly
4. Test with a minimal set of stocks first
5. Check SmartAPI documentation for updates

---

**Note:** This solution addresses both the WebSocket connection reliability and implements the intelligent workflow you requested. The system now automatically handles market timing, analyzes conditions, and dynamically selects the best stocks for monitoring.
