#!/usr/bin/env python3
"""
Strategy Evolution Agent - Continuous Learning and Strategy Refinement

This agent implements evolutionary algorithms and reinforcement learning to continuously
improve trading strategies based on performance feedback and market conditions.

Features:
🧬 1. Evolutionary Algorithms
- Genetic algorithms for strategy optimization
- Mutation engine for parameter variations
- Crossover engine for strategy breeding
- Selection logic based on fitness criteria

[DEBUG] 2. Performance Tracking and Analysis
- Monitors strategy performance over time
- Identifies improving/degrading strategies
- Tracks KPIs and performance metrics
- Performance feedback loop integration

[WORKFLOW] 3. Continuous Learning and Adaptation
- Reinforcement learning for strategy refinement
- Market regime adaptation
- Strategy portfolio management
- Automatic retraining and phasing out

[AGENT] 4. Integration with Trading Ecosystem
- Seamless integration with other agents
- Real-time performance monitoring
- Strategy deployment and management
- Compliance and risk-aware evolution

Author: AI Assistant
Date: 2025-07-16
"""

import asyncio
import logging
import yaml
import json
import numpy as np
import polars as pl
import pyarrow as pa
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, field
from pathlib import Path
import hashlib
import random
import copy
from enum import Enum
import uuid
import re
import ast
import operator
from concurrent.futures import ThreadPoolExecutor
import warnings

# Configure logging first
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Hyperparameter optimization
try:
    import optuna
    from optuna.samplers import TPESampler
    from optuna.pruners import MedianPruner
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False
    logger.warning("[WARN] Optuna not available. Install with: pip install optuna")

# Technical analysis with polars compatibility
try:
    import polars_talib as ta
    POLARS_TALIB_AVAILABLE = True
except ImportError:
    POLARS_TALIB_AVAILABLE = False
    logger.warning("[WARN] polars-talib not available. Install with: pip install polars-talib")

# Alternative technical analysis libraries
try:
    import mintalib
    MINTALIB_AVAILABLE = True
except ImportError:
    MINTALIB_AVAILABLE = False
    logger.warning("[WARN] mintalib not available. Install with: pip install mintalib")

# Symbolic regression for rule generation
try:
    from gplearn.genetic import SymbolicRegressor
    from gplearn.functions import make_function
    GPLEARN_AVAILABLE = True
except ImportError:
    GPLEARN_AVAILABLE = False
    logger.warning("[WARN] gplearn not available. Install with: pip install gplearn")

# Import existing agent interfaces
from agents.agent_communication_interface import AgentCommunicationInterface, MessageType
from agents.performance_analysis_agent import PerformanceAnalysisAgent
from agents.market_monitoring_agent import MarketMonitoringAgent
from agents.ai_training_agent import AITrainingAgent

class EvolutionMethod(Enum):
    """Evolution methods for strategy optimization"""
    GENETIC_ALGORITHM = "genetic_algorithm"
    PARTICLE_SWARM = "particle_swarm"
    DIFFERENTIAL_EVOLUTION = "differential_evolution"
    SIMULATED_ANNEALING = "simulated_annealing"
    REINFORCEMENT_LEARNING = "reinforcement_learning"

class MarketRegime(Enum):
    """Market regime types"""
    BULL = "bull"
    BEAR = "bear"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"

class StrategyStatus(Enum):
    """Strategy lifecycle status"""
    ACTIVE = "active"
    TESTING = "testing"
    DEPRECATED = "deprecated"
    FAILED = "failed"
    CHAMPION = "champion"
    CHALLENGER = "challenger"

class OptimizationMethod(Enum):
    """Hyperparameter optimization methods"""
    OPTUNA_TPE = "optuna_tpe"
    OPTUNA_RANDOM = "optuna_random"
    GRID_SEARCH = "grid_search"
    RANDOM_SEARCH = "random_search"
    BAYESIAN = "bayesian"

class TimeWindow(Enum):
    """Trading time windows for time-aware optimization"""
    MORNING = "morning"  # 9:15-11:00
    MIDDAY = "midday"    # 11:00-13:30
    AFTERNOON = "afternoon"  # 13:30-15:25
    FULL_DAY = "full_day"

class MutationType(Enum):
    """Types of strategy mutations"""
    PARAMETER_TWEAK = "parameter_tweak"
    CONDITION_MODIFY = "condition_modify"
    INDICATOR_REPLACE = "indicator_replace"
    LOGIC_SIMPLIFY = "logic_simplify"
    NEW_FEATURE_INJECT = "new_feature_inject"

@dataclass
class StrategyGene:
    """Individual gene in a strategy's genetic representation"""
    name: str
    value: Union[float, int, str, bool]
    min_value: Optional[Union[float, int]] = None
    max_value: Optional[Union[float, int]] = None
    mutation_rate: float = 0.1
    gene_type: str = "numeric"  # numeric, categorical, boolean
    optimization_space: Optional[Dict[str, Any]] = None  # For Optuna optimization

@dataclass
class HyperparameterSpace:
    """Hyperparameter optimization space definition"""
    parameter_name: str
    param_type: str  # "float", "int", "categorical", "boolean"
    low: Optional[float] = None
    high: Optional[float] = None
    choices: Optional[List[Any]] = None
    step: Optional[float] = None
    log: bool = False  # For log-uniform distribution

@dataclass
class OptimizationTarget:
    """Multi-objective optimization target"""
    metric_name: str
    weight: float
    direction: str = "maximize"  # "maximize" or "minimize"
    threshold: Optional[float] = None

@dataclass
class StrategyRule:
    """Individual trading rule within a strategy"""
    rule_id: str
    condition: str  # e.g., "RSI_14 < 30"
    rule_type: str  # "entry_long", "entry_short", "exit_long", "exit_short"
    indicators: List[str]  # List of required indicators
    parameters: Dict[str, Any]  # Rule-specific parameters
    confidence: float = 1.0  # Rule confidence/weight

@dataclass
class StrategyTemplate:
    """Template for generating new strategies"""
    template_name: str
    base_conditions: List[str]
    parameter_ranges: Dict[str, HyperparameterSpace]
    required_indicators: List[str]
    optional_indicators: List[str]
    fitness_targets: List[OptimizationTarget]

@dataclass
class StrategyChromosome:
    """Complete genetic representation of a strategy"""
    strategy_id: str
    strategy_name: str
    genes: Dict[str, StrategyGene]
    rules: List[StrategyRule] = field(default_factory=list)
    fitness_score: float = 0.0
    generation: int = 0
    parent_ids: List[str] = field(default_factory=list)
    creation_timestamp: datetime = field(default_factory=datetime.now)

    # Enhanced features
    optimization_history: List[Dict[str, Any]] = field(default_factory=list)
    regime_performance: Dict[str, float] = field(default_factory=dict)
    time_window_performance: Dict[str, float] = field(default_factory=dict)
    symbol_performance: Dict[str, float] = field(default_factory=dict)
    status: StrategyStatus = StrategyStatus.ACTIVE
    complexity_score: float = 0.0  # Measure of strategy complexity

    # Risk-reward combinations tested
    rr_combinations: List[Tuple[float, float]] = field(default_factory=list)
    best_rr_combo: Optional[Tuple[float, float]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert chromosome to dictionary"""
        return {
            'strategy_id': self.strategy_id,
            'strategy_name': self.strategy_name,
            'genes': {name: {'value': gene.value, 'type': gene.gene_type}
                     for name, gene in self.genes.items()},
            'rules': [{'rule_id': rule.rule_id, 'condition': rule.condition,
                      'rule_type': rule.rule_type, 'confidence': rule.confidence}
                     for rule in self.rules],
            'fitness_score': self.fitness_score,
            'generation': self.generation,
            'parent_ids': self.parent_ids,
            'creation_timestamp': self.creation_timestamp.isoformat(),
            'optimization_history': self.optimization_history,
            'regime_performance': self.regime_performance,
            'time_window_performance': self.time_window_performance,
            'symbol_performance': self.symbol_performance,
            'status': self.status.value,
            'complexity_score': self.complexity_score,
            'rr_combinations': self.rr_combinations,
            'best_rr_combo': self.best_rr_combo
        }

    def calculate_complexity(self) -> float:
        """Calculate strategy complexity score"""
        complexity = 0.0
        complexity += len(self.rules) * 0.1  # Rule count
        complexity += len(self.genes) * 0.05  # Parameter count

        # Analyze rule complexity
        for rule in self.rules:
            # Count operators and conditions
            operators = len(re.findall(r'[&|<>=!]', rule.condition))
            complexity += operators * 0.02

            # Count indicators used
            indicators = len(rule.indicators)
            complexity += indicators * 0.03

        self.complexity_score = complexity
        return complexity

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics for strategy evaluation"""
    strategy_id: str
    roi: float
    sharpe_ratio: float
    max_drawdown: float
    profit_factor: float
    win_rate: float
    expectancy: float
    total_trades: int
    avg_holding_period: float
    volatility: float
    calmar_ratio: float
    sortino_ratio: float
    
    # Market regime specific metrics
    bull_market_performance: float = 0.0
    bear_market_performance: float = 0.0
    sideways_market_performance: float = 0.0
    
    # Time-based metrics
    timestamp: datetime = field(default_factory=datetime.now)
    evaluation_period_days: int = 30
    
    def calculate_fitness_score(self, weights: Dict[str, float] = None) -> float:
        """Calculate composite fitness score"""
        if weights is None:
            weights = {
                'roi': 0.25,
                'sharpe_ratio': 0.20,
                'max_drawdown': -0.15,  # Negative because lower is better
                'profit_factor': 0.15,
                'win_rate': 0.10,
                'expectancy': 0.10,
                'calmar_ratio': 0.05
            }
        
        fitness = 0.0
        for metric, weight in weights.items():
            if hasattr(self, metric):
                value = getattr(self, metric)
                fitness += weight * value
        
        return fitness

@dataclass
class EvolutionConfig:
    """Configuration for evolutionary algorithm"""
    population_size: int = 50
    elite_size: int = 10
    mutation_rate: float = 0.1
    crossover_rate: float = 0.8
    max_generations: int = 100
    convergence_threshold: float = 0.001
    tournament_size: int = 5

    # Strategy-specific parameters
    max_strategies_per_symbol: int = 5
    min_performance_threshold: float = 0.05
    performance_evaluation_period: int = 30  # days

    # Market regime adaptation
    regime_adaptation_enabled: bool = True
    regime_specific_evolution: bool = True

    # Risk management
    max_risk_per_strategy: float = 0.02  # 2% max risk per strategy
    portfolio_risk_limit: float = 0.10   # 10% total portfolio risk

    # Hyperparameter optimization
    enable_hyperparameter_optimization: bool = True
    optimization_method: OptimizationMethod = OptimizationMethod.OPTUNA_TPE
    optuna_trials: int = 100
    optuna_timeout: int = 3600  # 1 hour
    optimization_targets: List[OptimizationTarget] = field(default_factory=list)

    # Risk-reward optimization
    rr_combinations: List[Tuple[float, float]] = field(default_factory=lambda: [
        (1.0, 1.0), (1.0, 1.5), (1.0, 2.0), (1.0, 2.5), (1.0, 3.0),
        (1.5, 2.0), (1.5, 3.0), (2.0, 3.0)
    ])

    # Time-aware optimization
    enable_time_aware_optimization: bool = True
    time_windows: List[TimeWindow] = field(default_factory=lambda: [
        TimeWindow.MORNING, TimeWindow.MIDDAY, TimeWindow.AFTERNOON
    ])

    # Rule mutation settings
    mutation_types: List[MutationType] = field(default_factory=lambda: [
        MutationType.PARAMETER_TWEAK, MutationType.CONDITION_MODIFY,
        MutationType.INDICATOR_REPLACE, MutationType.NEW_FEATURE_INJECT
    ])
    rule_mutation_rate: float = 0.15
    condition_simplification_rate: float = 0.05

    # Performance-driven selection
    enable_symbol_specific_ranking: bool = True
    prune_underperformers: bool = True
    underperformer_roi_threshold: float = 0.03  # 3% ROI threshold
    underperformer_accuracy_threshold: float = 0.45  # 45% accuracy threshold

    # Autonomous strategy discovery
    enable_autonomous_discovery: bool = False
    symbolic_regression_enabled: bool = False
    pattern_discovery_enabled: bool = False

@dataclass
class EvolutionState:
    """Current state of the evolution process"""
    current_generation: int = 0
    best_fitness: float = 0.0
    average_fitness: float = 0.0
    population: List[StrategyChromosome] = field(default_factory=list)
    elite_strategies: List[StrategyChromosome] = field(default_factory=list)
    
    # Performance tracking
    fitness_history: List[float] = field(default_factory=list)
    generation_timestamps: List[datetime] = field(default_factory=list)
    
    # Convergence tracking
    convergence_counter: int = 0
    last_improvement_generation: int = 0
    
    def update_generation_stats(self):
        """Update generation statistics"""
        if self.population:
            fitness_scores = [chromo.fitness_score for chromo in self.population]
            self.best_fitness = max(fitness_scores)
            self.average_fitness = np.mean(fitness_scores)
            self.fitness_history.append(self.average_fitness)
            self.generation_timestamps.append(datetime.now())

# ═══════════════════════════════════════════════════════════════════════════════
# 🧬 STRATEGY EVOLUTION AGENT
# ═══════════════════════════════════════════════════════════════════════════════

class StrategyEvolutionAgent:
    """
    Strategy Evolution Agent for continuous learning and strategy refinement
    
    Implements evolutionary algorithms, genetic programming, and reinforcement learning
    to continuously improve trading strategies based on performance feedback.
    """
    
    def __init__(self, config_path: str = "config/strategy_evolution_config.yaml"):
        """Initialize Strategy Evolution Agent"""
        
        # Load configuration
        self.config_path = config_path
        self.config = self._load_config()
        
        # Initialize evolution configuration
        self.evolution_config = EvolutionConfig(**self.config.get('evolution', {}))
        self.evolution_state = EvolutionState()
        
        # Agent communication
        self.comm_interface = AgentCommunicationInterface("strategy_evolution_agent")
        
        # Connected agents
        self.performance_agent: Optional[PerformanceAnalysisAgent] = None
        self.market_monitoring_agent: Optional[MarketMonitoringAgent] = None
        self.ai_training_agent: Optional[AITrainingAgent] = None
        
        # Strategy management
        self.active_strategies: Dict[str, StrategyChromosome] = {}
        self.strategy_performance_history: Dict[str, List[PerformanceMetrics]] = {}
        self.market_regime_strategies: Dict[MarketRegime, List[str]] = {
            regime: [] for regime in MarketRegime
        }
        
        # Evolution tracking
        self.evolution_history: List[EvolutionState] = []
        self.generation_counter = 0
        
        # Control flags
        self.is_running = False
        self.evolution_enabled = True
        
        logger.info("Strategy Evolution Agent initialized")

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r', encoding='utf-8') as file:
                    return yaml.safe_load(file)
            else:
                logger.warning(f"Config file not found: {self.config_path}, using defaults")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'evolution': {
                'population_size': 50,
                'elite_size': 10,
                'mutation_rate': 0.1,
                'crossover_rate': 0.8,
                'max_generations': 100
            },
            'performance': {
                'evaluation_period_days': 30,
                'min_trades_threshold': 10,
                'fitness_weights': {
                    'roi': 0.25,
                    'sharpe_ratio': 0.20,
                    'max_drawdown': -0.15,
                    'profit_factor': 0.15,
                    'win_rate': 0.10,
                    'expectancy': 0.10,
                    'calmar_ratio': 0.05
                }
            },
            'agents': {
                'performance_analysis_agent': {
                    'enabled': True,
                    'config_path': 'config/performance_analysis_config.yaml'
                },
                'market_monitoring_agent': {
                    'enabled': True,
                    'config_path': 'config/market_monitoring_config.yaml'
                },
                'ai_training_agent': {
                    'enabled': True,
                    'config_path': 'config/ai_training_config.yaml'
                }
            },
            'storage': {
                'strategies_dir': 'data/evolved_strategies',
                'performance_dir': 'data/evolution_performance',
                'backup_dir': 'data/evolution_backups'
            },
            'logging': {
                'level': 'INFO',
                'file': 'logs/strategy_evolution.log'
            }
        }

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🧬 CORE EVOLUTION ENGINE
    # ═══════════════════════════════════════════════════════════════════════════════

    async def setup(self):
        """Setup the Strategy Evolution Agent"""
        logger.info("[CONFIG] Setting up Strategy Evolution Agent...")

        try:
            # Create storage directories
            self._create_storage_directories()

            # Setup agent connections
            await self._setup_agent_connections()

            # Load existing strategies
            await self._load_existing_strategies()

            # Initialize evolution state
            await self._initialize_evolution_state()

            logger.info("[SUCCESS] Strategy Evolution Agent setup completed")

        except Exception as e:
            logger.error(f"[ERROR] Setup failed: {e}")
            raise

    def _create_storage_directories(self):
        """Create necessary storage directories"""
        storage_config = self.config.get('storage', {})

        directories = [
            storage_config.get('strategies_dir', 'data/evolved_strategies'),
            storage_config.get('performance_dir', 'data/evolution_performance'),
            storage_config.get('backup_dir', 'data/evolution_backups'),
            'logs'
        ]

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.debug(f"[FOLDER] Created directory: {directory}")

    async def _setup_agent_connections(self):
        """Setup connections to other agents"""
        agents_config = self.config.get('agents', {})

        # Setup Performance Analysis Agent
        if agents_config.get('performance_analysis_agent', {}).get('enabled', True):
            try:
                perf_config_path = agents_config['performance_analysis_agent'].get('config_path')
                self.performance_agent = PerformanceAnalysisAgent(perf_config_path)
                await self.performance_agent.setup()
                logger.info("[SUCCESS] Connected to Performance Analysis Agent")
            except Exception as e:
                logger.warning(f"[WARN] Failed to connect to Performance Analysis Agent: {e}")

        # Setup Market Monitoring Agent
        if agents_config.get('market_monitoring_agent', {}).get('enabled', True):
            try:
                market_config_path = agents_config['market_monitoring_agent'].get('config_path')
                self.market_monitoring_agent = MarketMonitoringAgent(market_config_path)
                await self.market_monitoring_agent.setup()
                logger.info("[SUCCESS] Connected to Market Monitoring Agent")
            except Exception as e:
                logger.warning(f"[WARN] Failed to connect to Market Monitoring Agent: {e}")

        # Setup AI Training Agent
        if agents_config.get('ai_training_agent', {}).get('enabled', True):
            try:
                ai_config_path = agents_config['ai_training_agent'].get('config_path')
                self.ai_training_agent = AITrainingAgent()
                logger.info("[SUCCESS] Connected to AI Training Agent")
            except Exception as e:
                logger.warning(f"[WARN] Failed to connect to AI Training Agent: {e}")

    async def _load_existing_strategies(self):
        """Load existing strategies from storage"""
        try:
            strategies_dir = Path(self.config.get('storage', {}).get('strategies_dir', 'data/evolved_strategies'))

            if strategies_dir.exists():
                strategy_files = list(strategies_dir.glob('*.json'))

                for strategy_file in strategy_files:
                    try:
                        with open(strategy_file, 'r', encoding='utf-8') as f:
                            strategy_data = json.load(f)

                        # Convert to StrategyChromosome
                        chromosome = self._dict_to_chromosome(strategy_data)
                        self.active_strategies[chromosome.strategy_id] = chromosome

                        logger.debug(f"📥 Loaded strategy: {chromosome.strategy_name}")

                    except Exception as e:
                        logger.warning(f"[WARN] Failed to load strategy from {strategy_file}: {e}")

                logger.info(f"📥 Loaded {len(self.active_strategies)} existing strategies")

        except Exception as e:
            logger.error(f"[ERROR] Error loading existing strategies: {e}")

    async def _initialize_evolution_state(self):
        """Initialize the evolution state"""
        try:
            # If no existing strategies, create initial population
            if not self.active_strategies:
                await self._create_initial_population()
            else:
                # Use existing strategies as population
                self.evolution_state.population = list(self.active_strategies.values())

            # Update generation stats
            self.evolution_state.update_generation_stats()

            logger.info(f"🧬 Evolution state initialized with {len(self.evolution_state.population)} strategies")

        except Exception as e:
            logger.error(f"[ERROR] Error initializing evolution state: {e}")
            raise

    async def _create_initial_population(self):
        """Create initial population of strategies"""
        try:
            # Load base strategies from config
            base_strategies = await self._load_base_strategies()

            population_size = self.evolution_config.population_size
            strategies_per_base = max(1, population_size // len(base_strategies))

            for base_strategy in base_strategies:
                for i in range(strategies_per_base):
                    # Create chromosome from base strategy
                    chromosome = self._create_chromosome_from_strategy(base_strategy, generation=0)

                    # Add some initial variation
                    if i > 0:  # Keep first one as original
                        chromosome = self._mutate_chromosome(chromosome, mutation_rate=0.3)

                    self.active_strategies[chromosome.strategy_id] = chromosome
                    self.evolution_state.population.append(chromosome)

            # Fill remaining slots with random variations
            while len(self.evolution_state.population) < population_size:
                base_strategy = random.choice(base_strategies)
                chromosome = self._create_chromosome_from_strategy(base_strategy, generation=0)
                chromosome = self._mutate_chromosome(chromosome, mutation_rate=0.5)

                self.active_strategies[chromosome.strategy_id] = chromosome
                self.evolution_state.population.append(chromosome)

            logger.info(f"🧬 Created initial population of {len(self.evolution_state.population)} strategies")

        except Exception as e:
            logger.error(f"[ERROR] Error creating initial population: {e}")
            raise

    async def _load_base_strategies(self) -> List[Dict[str, Any]]:
        """Load base strategies from configuration"""
        try:
            # Load from strategies.yaml
            strategy_config_path = "config/strategies.yaml"

            if Path(strategy_config_path).exists():
                with open(strategy_config_path, 'r', encoding='utf-8') as file:
                    strategy_data = yaml.safe_load(file)

                strategies = strategy_data.get('strategies', [])
                logger.info(f"📥 Loaded {len(strategies)} base strategies")
                return strategies
            else:
                # Create default strategies if config doesn't exist
                return self._get_default_strategies()

        except Exception as e:
            logger.error(f"[ERROR] Error loading base strategies: {e}")
            return self._get_default_strategies()

    def _get_default_strategies(self) -> List[Dict[str, Any]]:
        """Get default strategies for initial population"""
        return [
            {
                'name': 'RSI_Mean_Reversion',
                'long': 'RSI_14 < 30 & Volume > Volume.rolling(20).mean()',
                'short': 'RSI_14 > 70 & Volume > Volume.rolling(20).mean()',
                'capital': 100000
            },
            {
                'name': 'EMA_Crossover',
                'long': 'EMA_5 > EMA_20 & EMA_20 > EMA_50',
                'short': 'EMA_5 < EMA_20 & EMA_20 < EMA_50',
                'capital': 100000
            },
            {
                'name': 'VWAP_Breakout',
                'long': 'Close > VWAP & Volume > Volume.rolling(20).mean() * 1.5',
                'short': 'Close < VWAP & Volume > Volume.rolling(20).mean() * 1.5',
                'capital': 100000
            }
        ]

    def _create_chromosome_from_strategy(self, strategy: Dict[str, Any], generation: int = 0) -> StrategyChromosome:
        """Create a chromosome from a strategy definition"""
        strategy_id = str(uuid.uuid4())
        strategy_name = strategy.get('name', f'Strategy_{strategy_id[:8]}')

        # Extract genes from strategy parameters
        genes = {}

        # RSI parameters
        if 'RSI' in str(strategy):
            genes['rsi_period'] = StrategyGene('rsi_period', 14, 5, 50, 0.1, 'numeric')
            genes['rsi_oversold'] = StrategyGene('rsi_oversold', 30, 10, 40, 0.1, 'numeric')
            genes['rsi_overbought'] = StrategyGene('rsi_overbought', 70, 60, 90, 0.1, 'numeric')

        # EMA parameters
        if 'EMA' in str(strategy):
            genes['ema_fast'] = StrategyGene('ema_fast', 5, 3, 15, 0.1, 'numeric')
            genes['ema_slow'] = StrategyGene('ema_slow', 20, 15, 50, 0.1, 'numeric')
            genes['ema_trend'] = StrategyGene('ema_trend', 50, 30, 100, 0.1, 'numeric')

        # Volume parameters
        if 'Volume' in str(strategy):
            genes['volume_multiplier'] = StrategyGene('volume_multiplier', 1.5, 1.0, 3.0, 0.1, 'numeric')
            genes['volume_period'] = StrategyGene('volume_period', 20, 10, 50, 0.1, 'numeric')

        # VWAP parameters
        if 'VWAP' in str(strategy):
            genes['vwap_deviation'] = StrategyGene('vwap_deviation', 0.002, 0.001, 0.01, 0.1, 'numeric')

        # Risk management parameters
        genes['stop_loss_pct'] = StrategyGene('stop_loss_pct', 0.02, 0.005, 0.05, 0.1, 'numeric')
        genes['take_profit_pct'] = StrategyGene('take_profit_pct', 0.04, 0.01, 0.10, 0.1, 'numeric')
        genes['position_size_pct'] = StrategyGene('position_size_pct', 0.05, 0.01, 0.15, 0.1, 'numeric')

        # Market timing parameters
        genes['min_volume_ratio'] = StrategyGene('min_volume_ratio', 1.2, 0.8, 2.5, 0.1, 'numeric')
        genes['volatility_filter'] = StrategyGene('volatility_filter', True, None, None, 0.1, 'boolean')

        return StrategyChromosome(
            strategy_id=strategy_id,
            strategy_name=strategy_name,
            genes=genes,
            generation=generation,
            creation_timestamp=datetime.now()
        )

    def _dict_to_chromosome(self, strategy_data: Dict[str, Any]) -> StrategyChromosome:
        """Convert dictionary to StrategyChromosome"""
        genes = {}

        for gene_name, gene_data in strategy_data.get('genes', {}).items():
            genes[gene_name] = StrategyGene(
                name=gene_name,
                value=gene_data['value'],
                gene_type=gene_data.get('type', 'numeric')
            )

        return StrategyChromosome(
            strategy_id=strategy_data['strategy_id'],
            strategy_name=strategy_data['strategy_name'],
            genes=genes,
            fitness_score=strategy_data.get('fitness_score', 0.0),
            generation=strategy_data.get('generation', 0),
            parent_ids=strategy_data.get('parent_ids', []),
            creation_timestamp=datetime.fromisoformat(strategy_data.get('creation_timestamp', datetime.now().isoformat()))
        )

    # ═══════════════════════════════════════════════════════════════════════════════
    # [CONFIG] HYPERPARAMETER OPTIMIZATION ENGINE
    # ═══════════════════════════════════════════════════════════════════════════════

    async def optimize_strategy_hyperparameters(self, chromosome: StrategyChromosome,
                                              optimization_targets: List[OptimizationTarget] = None) -> StrategyChromosome:
        """Optimize strategy hyperparameters using Optuna or other methods"""
        try:
            if not self.evolution_config.enable_hyperparameter_optimization:
                return chromosome

            if not OPTUNA_AVAILABLE and self.evolution_config.optimization_method in [
                OptimizationMethod.OPTUNA_TPE, OptimizationMethod.OPTUNA_RANDOM
            ]:
                logger.warning("[WARN] Optuna not available, falling back to random search")
                return await self._random_search_optimization(chromosome)

            if self.evolution_config.optimization_method == OptimizationMethod.OPTUNA_TPE:
                return await self._optuna_optimization(chromosome, optimization_targets)
            elif self.evolution_config.optimization_method == OptimizationMethod.GRID_SEARCH:
                return await self._grid_search_optimization(chromosome)
            elif self.evolution_config.optimization_method == OptimizationMethod.RANDOM_SEARCH:
                return await self._random_search_optimization(chromosome)
            else:
                return chromosome

        except Exception as e:
            logger.error(f"[ERROR] Error optimizing hyperparameters: {e}")
            return chromosome

    async def _optuna_optimization(self, chromosome: StrategyChromosome,
                                 optimization_targets: List[OptimizationTarget] = None) -> StrategyChromosome:
        """Optimize using Optuna TPE sampler"""
        try:
            if not OPTUNA_AVAILABLE:
                return chromosome

            # Create optimization study
            study = optuna.create_study(
                direction="maximize",  # We'll handle multi-objective internally
                sampler=TPESampler(),
                pruner=MedianPruner()
            )

            # Define objective function
            def objective(trial):
                return self._optuna_objective(trial, chromosome, optimization_targets)

            # Run optimization
            study.optimize(
                objective,
                n_trials=self.evolution_config.optuna_trials,
                timeout=self.evolution_config.optuna_timeout
            )

            # Create optimized chromosome
            best_params = study.best_params
            optimized_chromosome = self._apply_optimized_parameters(chromosome, best_params)

            # Store optimization history
            optimized_chromosome.optimization_history.append({
                'method': 'optuna_tpe',
                'best_value': study.best_value,
                'best_params': best_params,
                'n_trials': len(study.trials),
                'timestamp': datetime.now().isoformat()
            })

            logger.info(f"[TARGET] Optuna optimization completed: {study.best_value:.4f}")
            return optimized_chromosome

        except Exception as e:
            logger.error(f"[ERROR] Error in Optuna optimization: {e}")
            return chromosome

    def _optuna_objective(self, trial, chromosome: StrategyChromosome,
                         optimization_targets: List[OptimizationTarget] = None) -> float:
        """Optuna objective function"""
        try:
            # Suggest parameters for optimization
            suggested_params = {}

            for gene_name, gene in chromosome.genes.items():
                if gene.optimization_space:
                    space = gene.optimization_space

                    if space['type'] == 'float':
                        suggested_params[gene_name] = trial.suggest_float(
                            gene_name, space['low'], space['high'],
                            step=space.get('step'), log=space.get('log', False)
                        )
                    elif space['type'] == 'int':
                        suggested_params[gene_name] = trial.suggest_int(
                            gene_name, space['low'], space['high'],
                            step=space.get('step', 1)
                        )
                    elif space['type'] == 'categorical':
                        suggested_params[gene_name] = trial.suggest_categorical(
                            gene_name, space['choices']
                        )

            # Create temporary chromosome with suggested parameters
            temp_chromosome = copy.deepcopy(chromosome)
            for param_name, param_value in suggested_params.items():
                if param_name in temp_chromosome.genes:
                    temp_chromosome.genes[param_name].value = param_value

            # Evaluate performance (this would typically involve backtesting)
            performance_score = self._evaluate_chromosome_performance(temp_chromosome)

            return performance_score

        except Exception as e:
            logger.error(f"[ERROR] Error in Optuna objective: {e}")
            return 0.0

    def _evaluate_chromosome_performance(self, chromosome: StrategyChromosome) -> float:
        """Evaluate chromosome performance for optimization"""
        try:
            # This is a simplified evaluation - in practice, this would run backtesting
            # For now, we'll use a heuristic based on gene values

            performance = 0.0

            # Risk-reward ratio scoring
            if 'stop_loss_pct' in chromosome.genes and 'take_profit_pct' in chromosome.genes:
                sl = chromosome.genes['stop_loss_pct'].value
                tp = chromosome.genes['take_profit_pct'].value
                rr_ratio = tp / sl if sl > 0 else 1.0
                performance += min(rr_ratio * 0.1, 0.3)  # Cap at 0.3

            # Parameter balance scoring
            if 'rsi_period' in chromosome.genes:
                rsi_period = chromosome.genes['rsi_period'].value
                if 10 <= rsi_period <= 20:
                    performance += 0.1

            if 'position_size_pct' in chromosome.genes:
                pos_size = chromosome.genes['position_size_pct'].value
                if 0.02 <= pos_size <= 0.08:
                    performance += 0.2

            # Add some randomness to simulate market uncertainty
            market_noise = random.gauss(0, 0.05)
            performance += market_noise

            return max(0.0, performance)

        except Exception as e:
            logger.error(f"[ERROR] Error evaluating chromosome performance: {e}")
            return 0.0

    def _apply_optimized_parameters(self, chromosome: StrategyChromosome,
                                  optimized_params: Dict[str, Any]) -> StrategyChromosome:
        """Apply optimized parameters to chromosome"""
        optimized_chromosome = copy.deepcopy(chromosome)
        optimized_chromosome.strategy_id = str(uuid.uuid4())
        optimized_chromosome.strategy_name = f"{chromosome.strategy_name}_OPT"
        optimized_chromosome.generation = self.generation_counter
        optimized_chromosome.parent_ids = [chromosome.strategy_id]
        optimized_chromosome.creation_timestamp = datetime.now()

        # Apply optimized parameters
        for param_name, param_value in optimized_params.items():
            if param_name in optimized_chromosome.genes:
                optimized_chromosome.genes[param_name].value = param_value

        return optimized_chromosome

    async def _grid_search_optimization(self, chromosome: StrategyChromosome) -> StrategyChromosome:
        """Grid search optimization"""
        try:
            best_chromosome = chromosome
            best_score = 0.0

            # Define parameter grids
            parameter_grids = self._create_parameter_grids(chromosome)

            # Generate all combinations
            import itertools
            param_names = list(parameter_grids.keys())
            param_values = list(parameter_grids.values())

            for combination in itertools.product(*param_values):
                # Create test chromosome
                test_chromosome = copy.deepcopy(chromosome)

                for i, param_name in enumerate(param_names):
                    if param_name in test_chromosome.genes:
                        test_chromosome.genes[param_name].value = combination[i]

                # Evaluate performance
                score = self._evaluate_chromosome_performance(test_chromosome)

                if score > best_score:
                    best_score = score
                    best_chromosome = test_chromosome

            # Update chromosome metadata
            best_chromosome.strategy_id = str(uuid.uuid4())
            best_chromosome.strategy_name = f"{chromosome.strategy_name}_GRID"
            best_chromosome.optimization_history.append({
                'method': 'grid_search',
                'best_score': best_score,
                'timestamp': datetime.now().isoformat()
            })

            return best_chromosome

        except Exception as e:
            logger.error(f"[ERROR] Error in grid search optimization: {e}")
            return chromosome

    def _create_parameter_grids(self, chromosome: StrategyChromosome) -> Dict[str, List[Any]]:
        """Create parameter grids for grid search"""
        grids = {}

        for gene_name, gene in chromosome.genes.items():
            if gene.gene_type == 'numeric' and gene.min_value is not None and gene.max_value is not None:
                if isinstance(gene.value, int):
                    # Integer parameter
                    step = max(1, int((gene.max_value - gene.min_value) / 10))
                    grids[gene_name] = list(range(int(gene.min_value), int(gene.max_value) + 1, step))
                else:
                    # Float parameter
                    step = (gene.max_value - gene.min_value) / 10
                    grids[gene_name] = [gene.min_value + i * step for i in range(11)]
            elif gene.gene_type == 'boolean':
                grids[gene_name] = [True, False]

        return grids

    async def _random_search_optimization(self, chromosome: StrategyChromosome) -> StrategyChromosome:
        """Random search optimization"""
        try:
            best_chromosome = chromosome
            best_score = self._evaluate_chromosome_performance(chromosome)

            # Random search iterations
            for _ in range(50):  # 50 random trials
                # Create random variant
                test_chromosome = self._mutate_chromosome(chromosome, mutation_rate=0.3)

                # Evaluate performance
                score = self._evaluate_chromosome_performance(test_chromosome)

                if score > best_score:
                    best_score = score
                    best_chromosome = test_chromosome

            # Update chromosome metadata
            best_chromosome.strategy_id = str(uuid.uuid4())
            best_chromosome.strategy_name = f"{chromosome.strategy_name}_RAND"
            best_chromosome.optimization_history.append({
                'method': 'random_search',
                'best_score': best_score,
                'timestamp': datetime.now().isoformat()
            })

            return best_chromosome

        except Exception as e:
            logger.error(f"[ERROR] Error in random search optimization: {e}")
            return chromosome

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🧬 GENETIC ALGORITHM OPERATIONS
    # ═══════════════════════════════════════════════════════════════════════════════

    def _mutate_chromosome(self, chromosome: StrategyChromosome, mutation_rate: float = None) -> StrategyChromosome:
        """Apply mutation to a chromosome"""
        if mutation_rate is None:
            mutation_rate = self.evolution_config.mutation_rate

        # Create a copy for mutation
        mutated_chromosome = copy.deepcopy(chromosome)
        mutated_chromosome.strategy_id = str(uuid.uuid4())
        mutated_chromosome.strategy_name = f"{chromosome.strategy_name}_M{self.generation_counter}"
        mutated_chromosome.generation = self.generation_counter
        mutated_chromosome.parent_ids = [chromosome.strategy_id]
        mutated_chromosome.creation_timestamp = datetime.now()

        # Apply different types of mutations
        mutation_applied = False

        # 1. Parameter tweaking (traditional gene mutation)
        if random.random() < mutation_rate:
            mutation_applied = True
            self._apply_parameter_mutation(mutated_chromosome, mutation_rate)

        # 2. Rule-based mutations
        if random.random() < self.evolution_config.rule_mutation_rate:
            mutation_applied = True
            self._apply_rule_mutations(mutated_chromosome)

        # 3. Condition simplification
        if random.random() < self.evolution_config.condition_simplification_rate:
            mutation_applied = True
            self._apply_condition_simplification(mutated_chromosome)

        # 4. New feature injection
        if random.random() < 0.1:  # 10% chance
            mutation_applied = True
            self._apply_feature_injection(mutated_chromosome)

        # Ensure at least some mutation occurred
        if not mutation_applied:
            self._apply_parameter_mutation(mutated_chromosome, mutation_rate * 0.5)

        return mutated_chromosome

    def _apply_parameter_mutation(self, chromosome: StrategyChromosome, mutation_rate: float):
        """Apply traditional parameter mutations"""
        for gene_name, gene in chromosome.genes.items():
            if random.random() < mutation_rate:
                if gene.gene_type == 'numeric':
                    # Gaussian mutation for numeric values
                    if gene.min_value is not None and gene.max_value is not None:
                        # Bounded mutation
                        range_size = gene.max_value - gene.min_value
                        mutation_strength = range_size * 0.1  # 10% of range

                        new_value = gene.value + random.gauss(0, mutation_strength)
                        new_value = max(gene.min_value, min(gene.max_value, new_value))

                        if isinstance(gene.value, int):
                            new_value = int(round(new_value))

                        gene.value = new_value
                    else:
                        # Unbounded mutation
                        mutation_strength = abs(gene.value) * 0.1 if gene.value != 0 else 0.1
                        gene.value += random.gauss(0, mutation_strength)

                elif gene.gene_type == 'boolean':
                    # Flip boolean values
                    gene.value = not gene.value

                elif gene.gene_type == 'categorical':
                    # Random selection from predefined categories
                    if hasattr(gene, 'choices') and gene.choices:
                        gene.value = random.choice(gene.choices)

    def _apply_rule_mutations(self, chromosome: StrategyChromosome):
        """Apply rule-based mutations to strategy conditions"""
        try:
            if not chromosome.rules:
                return

            # Select random rule to mutate
            rule_to_mutate = random.choice(chromosome.rules)

            # Choose mutation type
            mutation_type = random.choice(self.evolution_config.mutation_types)

            if mutation_type == MutationType.CONDITION_MODIFY:
                self._mutate_rule_condition(rule_to_mutate, chromosome)
            elif mutation_type == MutationType.INDICATOR_REPLACE:
                self._mutate_rule_indicator(rule_to_mutate, chromosome)
            elif mutation_type == MutationType.LOGIC_SIMPLIFY:
                self._simplify_rule_logic(rule_to_mutate)
            elif mutation_type == MutationType.NEW_FEATURE_INJECT:
                self._inject_new_feature(rule_to_mutate, chromosome)

        except Exception as e:
            logger.warning(f"[WARN] Error applying rule mutations: {e}")

    def _mutate_rule_condition(self, rule: StrategyRule, chromosome: StrategyChromosome):
        """Mutate a rule's condition"""
        try:
            # Parse the condition and modify operators or thresholds
            condition = rule.condition

            # Replace comparison operators
            operator_replacements = {
                '<': ['<=', '>', '>='],
                '<=': ['<', '>', '>='],
                '>': ['>=', '<', '<='],
                '>=': ['>', '<', '<='],
                '==': ['!='],
                '!=': ['==']
            }

            for old_op, new_ops in operator_replacements.items():
                if old_op in condition:
                    new_op = random.choice(new_ops)
                    condition = condition.replace(old_op, new_op, 1)  # Replace first occurrence
                    break

            # Modify numeric thresholds
            import re
            numbers = re.findall(r'\d+\.?\d*', condition)
            if numbers:
                old_number = random.choice(numbers)
                try:
                    old_value = float(old_number)
                    # Apply small random change
                    change_factor = random.uniform(0.8, 1.2)
                    new_value = old_value * change_factor

                    # Format appropriately
                    if '.' in old_number:
                        new_number = f"{new_value:.2f}"
                    else:
                        new_number = str(int(new_value))

                    condition = condition.replace(old_number, new_number, 1)
                except ValueError:
                    pass

            rule.condition = condition

        except Exception as e:
            logger.warning(f"[WARN] Error mutating rule condition: {e}")

    def _mutate_rule_indicator(self, rule: StrategyRule, chromosome: StrategyChromosome):
        """Replace indicators in a rule"""
        try:
            # Define indicator replacements
            indicator_replacements = {
                'RSI_14': ['RSI_21', 'RSI_9', 'STOCH_K', 'STOCH_D'],
                'EMA_5': ['EMA_8', 'EMA_13', 'SMA_5', 'SMA_10'],
                'EMA_20': ['EMA_21', 'EMA_30', 'SMA_20', 'SMA_30'],
                'MACD': ['MACD_signal', 'PPO', 'TRIX'],
                'Volume': ['Volume_SMA', 'Volume_EMA', 'OBV'],
                'VWAP': ['TWAP', 'Close', 'Typical_Price']
            }

            condition = rule.condition

            for old_indicator, replacements in indicator_replacements.items():
                if old_indicator in condition:
                    new_indicator = random.choice(replacements)
                    condition = condition.replace(old_indicator, new_indicator)

                    # Update rule indicators list
                    if old_indicator in rule.indicators:
                        rule.indicators.remove(old_indicator)
                    if new_indicator not in rule.indicators:
                        rule.indicators.append(new_indicator)

                    break

            rule.condition = condition

        except Exception as e:
            logger.warning(f"[WARN] Error mutating rule indicator: {e}")

    def _simplify_rule_logic(self, rule: StrategyRule):
        """Simplify rule logic by removing redundant conditions"""
        try:
            condition = rule.condition

            # Remove redundant parentheses
            while '((' in condition and '))' in condition:
                condition = condition.replace('((', '(').replace('))', ')')

            # Simplify double negations
            condition = condition.replace('not not ', '')

            # Remove redundant AND/OR operations
            condition = re.sub(r'\s+and\s+and\s+', ' and ', condition)
            condition = re.sub(r'\s+or\s+or\s+', ' or ', condition)

            rule.condition = condition

        except Exception as e:
            logger.warning(f"[WARN] Error simplifying rule logic: {e}")

    def _inject_new_feature(self, rule: StrategyRule, chromosome: StrategyChromosome):
        """Inject new technical indicators into a rule"""
        try:
            # List of new indicators to potentially inject
            new_indicators = [
                'ADX', 'MFI', 'CCI', 'Williams_R', 'ROC', 'TSI',
                'BB_upper', 'BB_lower', 'BB_width', 'ATR',
                'Donchian_high', 'Donchian_low', 'Pivot_Point',
                'Support_1', 'Resistance_1', 'SuperTrend'
            ]

            # Select random new indicator
            new_indicator = random.choice(new_indicators)

            # Create new condition component
            operators = ['>', '<', '>=', '<=']
            operator = random.choice(operators)
            threshold = random.uniform(0.1, 100)  # Adjust based on indicator

            new_condition_part = f"{new_indicator} {operator} {threshold:.2f}"

            # Add to existing condition with AND/OR
            connector = random.choice([' and ', ' or '])
            rule.condition = f"({rule.condition}){connector}({new_condition_part})"

            # Update indicators list
            if new_indicator not in rule.indicators:
                rule.indicators.append(new_indicator)

        except Exception as e:
            logger.warning(f"[WARN] Error injecting new feature: {e}")

    def _apply_condition_simplification(self, chromosome: StrategyChromosome):
        """Apply condition simplification across all rules"""
        try:
            for rule in chromosome.rules:
                self._simplify_rule_logic(rule)

        except Exception as e:
            logger.warning(f"[WARN] Error applying condition simplification: {e}")

    def _apply_feature_injection(self, chromosome: StrategyChromosome):
        """Apply feature injection to random rules"""
        try:
            if chromosome.rules:
                # Select random subset of rules for feature injection
                num_rules_to_modify = max(1, len(chromosome.rules) // 3)
                rules_to_modify = random.sample(chromosome.rules, num_rules_to_modify)

                for rule in rules_to_modify:
                    self._inject_new_feature(rule, chromosome)

        except Exception as e:
            logger.warning(f"[WARN] Error applying feature injection: {e}")

    def _crossover_chromosomes(self, parent1: StrategyChromosome, parent2: StrategyChromosome) -> Tuple[StrategyChromosome, StrategyChromosome]:
        """Perform crossover between two chromosomes"""

        # Create offspring
        offspring1 = copy.deepcopy(parent1)
        offspring2 = copy.deepcopy(parent2)

        # Generate new IDs and metadata
        offspring1.strategy_id = str(uuid.uuid4())
        offspring2.strategy_id = str(uuid.uuid4())

        offspring1.strategy_name = f"{parent1.strategy_name}_X{parent2.strategy_name}_{self.generation_counter}"
        offspring2.strategy_name = f"{parent2.strategy_name}_X{parent1.strategy_name}_{self.generation_counter}"

        offspring1.generation = self.generation_counter
        offspring2.generation = self.generation_counter

        offspring1.parent_ids = [parent1.strategy_id, parent2.strategy_id]
        offspring2.parent_ids = [parent1.strategy_id, parent2.strategy_id]

        offspring1.creation_timestamp = datetime.now()
        offspring2.creation_timestamp = datetime.now()

        # Perform uniform crossover for genes
        common_genes = set(parent1.genes.keys()) & set(parent2.genes.keys())

        for gene_name in common_genes:
            if random.random() < 0.5:  # 50% chance to swap
                # Swap genes between offspring
                offspring1.genes[gene_name] = copy.deepcopy(parent2.genes[gene_name])
                offspring2.genes[gene_name] = copy.deepcopy(parent1.genes[gene_name])

        # Perform rule crossover
        self._crossover_rules(offspring1, offspring2, parent1, parent2)

        return offspring1, offspring2

    def _crossover_rules(self, offspring1: StrategyChromosome, offspring2: StrategyChromosome,
                        parent1: StrategyChromosome, parent2: StrategyChromosome):
        """Perform crossover for strategy rules"""
        try:
            # Mix rules from both parents
            all_rules = parent1.rules + parent2.rules

            if all_rules:
                # Randomly distribute rules between offspring
                random.shuffle(all_rules)
                mid_point = len(all_rules) // 2

                offspring1.rules = all_rules[:mid_point]
                offspring2.rules = all_rules[mid_point:]

                # Ensure each offspring has at least one rule
                if not offspring1.rules and parent1.rules:
                    offspring1.rules = [random.choice(parent1.rules)]
                if not offspring2.rules and parent2.rules:
                    offspring2.rules = [random.choice(parent2.rules)]

        except Exception as e:
            logger.warning(f"[WARN] Error in rule crossover: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # ⚖️ RISK-REWARD OPTIMIZATION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def optimize_risk_reward_combinations(self, chromosome: StrategyChromosome) -> StrategyChromosome:
        """Optimize risk-reward combinations for a strategy"""
        try:
            best_chromosome = chromosome
            best_score = 0.0

            # Test different risk-reward combinations
            for sl_ratio, tp_ratio in self.evolution_config.rr_combinations:
                # Create test chromosome with new RR ratio
                test_chromosome = copy.deepcopy(chromosome)

                # Apply risk-reward ratio
                if 'stop_loss_pct' in test_chromosome.genes:
                    base_sl = test_chromosome.genes['stop_loss_pct'].value
                    test_chromosome.genes['stop_loss_pct'].value = base_sl * sl_ratio

                if 'take_profit_pct' in test_chromosome.genes:
                    base_tp = test_chromosome.genes['take_profit_pct'].value
                    test_chromosome.genes['take_profit_pct'].value = base_tp * tp_ratio

                # Evaluate performance
                score = await self._evaluate_rr_combination(test_chromosome, sl_ratio, tp_ratio)

                # Track combination
                test_chromosome.rr_combinations.append((sl_ratio, tp_ratio))

                if score > best_score:
                    best_score = score
                    best_chromosome = test_chromosome
                    best_chromosome.best_rr_combo = (sl_ratio, tp_ratio)

            logger.info(f"[TARGET] Best RR combination: {best_chromosome.best_rr_combo} (score: {best_score:.4f})")
            return best_chromosome

        except Exception as e:
            logger.error(f"[ERROR] Error optimizing risk-reward combinations: {e}")
            return chromosome

    async def _evaluate_rr_combination(self, chromosome: StrategyChromosome,
                                     sl_ratio: float, tp_ratio: float) -> float:
        """Evaluate a specific risk-reward combination"""
        try:
            # Calculate theoretical performance metrics
            rr_ratio = tp_ratio / sl_ratio if sl_ratio > 0 else 1.0

            # Base score from RR ratio
            score = min(rr_ratio * 0.1, 0.4)  # Cap at 0.4

            # Adjust for practical considerations
            if 1.5 <= rr_ratio <= 3.0:  # Sweet spot for most strategies
                score += 0.1

            # Penalize extreme ratios
            if rr_ratio > 5.0 or rr_ratio < 0.5:
                score -= 0.2

            # Add randomness to simulate market conditions
            market_factor = random.uniform(0.8, 1.2)
            score *= market_factor

            return max(0.0, score)

        except Exception as e:
            logger.error(f"[ERROR] Error evaluating RR combination: {e}")
            return 0.0

    # ═══════════════════════════════════════════════════════════════════════════════
    # [UPTIME] TIME-AWARE OPTIMIZATION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def optimize_time_windows(self, chromosome: StrategyChromosome) -> StrategyChromosome:
        """Optimize strategy for different time windows"""
        try:
            if not self.evolution_config.enable_time_aware_optimization:
                return chromosome

            best_chromosome = chromosome

            # Test strategy performance in different time windows
            for time_window in self.evolution_config.time_windows:
                # Create time-specific variant
                time_chromosome = await self._adapt_for_time_window(chromosome, time_window)

                # Evaluate performance for this time window
                performance = await self._evaluate_time_window_performance(time_chromosome, time_window)

                # Store time window performance
                time_chromosome.time_window_performance[time_window.value] = performance

                # Update best if this performs better
                if performance > best_chromosome.time_window_performance.get(time_window.value, 0):
                    best_chromosome = time_chromosome

            return best_chromosome

        except Exception as e:
            logger.error(f"[ERROR] Error optimizing time windows: {e}")
            return chromosome

    async def _adapt_for_time_window(self, chromosome: StrategyChromosome,
                                   time_window: TimeWindow) -> StrategyChromosome:
        """Adapt strategy for specific time window"""
        adapted_chromosome = copy.deepcopy(chromosome)
        adapted_chromosome.strategy_id = str(uuid.uuid4())
        adapted_chromosome.strategy_name = f"{chromosome.strategy_name}_{time_window.value.upper()}"

        # Time-specific adaptations
        if time_window == TimeWindow.MORNING:
            # Morning: Higher volatility, wider stops
            if 'stop_loss_pct' in adapted_chromosome.genes:
                current_sl = adapted_chromosome.genes['stop_loss_pct'].value
                adapted_chromosome.genes['stop_loss_pct'].value = current_sl * 1.2

            if 'volume_multiplier' in adapted_chromosome.genes:
                adapted_chromosome.genes['volume_multiplier'].value = 2.0  # Higher volume threshold

        elif time_window == TimeWindow.MIDDAY:
            # Midday: Lower volatility, tighter parameters
            if 'stop_loss_pct' in adapted_chromosome.genes:
                current_sl = adapted_chromosome.genes['stop_loss_pct'].value
                adapted_chromosome.genes['stop_loss_pct'].value = current_sl * 0.8

            if 'rsi_period' in adapted_chromosome.genes:
                adapted_chromosome.genes['rsi_period'].value = 21  # Longer period for stability

        elif time_window == TimeWindow.AFTERNOON:
            # Afternoon: Pre-close activity, momentum focus
            if 'ema_fast' in adapted_chromosome.genes:
                adapted_chromosome.genes['ema_fast'].value = 3  # Faster EMA for momentum

            if 'take_profit_pct' in adapted_chromosome.genes:
                current_tp = adapted_chromosome.genes['take_profit_pct'].value
                adapted_chromosome.genes['take_profit_pct'].value = current_tp * 1.1  # Slightly higher targets

        return adapted_chromosome

    async def _evaluate_time_window_performance(self, chromosome: StrategyChromosome,
                                              time_window: TimeWindow) -> float:
        """Evaluate strategy performance for specific time window"""
        try:
            # Simulate time-window specific performance
            base_performance = self._evaluate_chromosome_performance(chromosome)

            # Time window adjustments
            time_multipliers = {
                TimeWindow.MORNING: 1.1,    # Higher volatility = higher potential
                TimeWindow.MIDDAY: 0.9,     # Lower volatility = lower potential
                TimeWindow.AFTERNOON: 1.05, # Moderate activity
                TimeWindow.FULL_DAY: 1.0    # Baseline
            }

            multiplier = time_multipliers.get(time_window, 1.0)
            adjusted_performance = base_performance * multiplier

            # Add time-specific randomness
            time_noise = random.gauss(0, 0.02)  # 2% noise
            adjusted_performance += time_noise

            return max(0.0, adjusted_performance)

        except Exception as e:
            logger.error(f"[ERROR] Error evaluating time window performance: {e}")
            return 0.0

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🧠 PERFORMANCE-DRIVEN SELECTION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def apply_performance_driven_selection(self, population: List[StrategyChromosome]) -> List[StrategyChromosome]:
        """Apply performance-driven selection to population"""
        try:
            # 1. Symbol-specific ranking
            if self.evolution_config.enable_symbol_specific_ranking:
                population = await self._apply_symbol_specific_ranking(population)

            # 2. Prune underperformers
            if self.evolution_config.prune_underperformers:
                population = self._prune_underperforming_strategies(population)

            # 3. Niche optimization
            population = self._apply_niche_optimization(population)

            return population

        except Exception as e:
            logger.error(f"[ERROR] Error in performance-driven selection: {e}")
            return population

    async def _apply_symbol_specific_ranking(self, population: List[StrategyChromosome]) -> List[StrategyChromosome]:
        """Apply symbol-specific ranking to strategies"""
        try:
            # Group strategies by symbol performance
            symbol_rankings = {}

            for chromosome in population:
                for symbol, performance in chromosome.symbol_performance.items():
                    if symbol not in symbol_rankings:
                        symbol_rankings[symbol] = []

                    symbol_rankings[symbol].append({
                        'chromosome': chromosome,
                        'performance': performance,
                        'strategy_id': chromosome.strategy_id
                    })

            # Rank strategies within each symbol
            enhanced_population = []

            for symbol, strategies in symbol_rankings.items():
                # Sort by performance for this symbol
                strategies.sort(key=lambda x: x['performance'], reverse=True)

                # Apply symbol-specific fitness boost to top performers
                for i, strategy_info in enumerate(strategies):
                    chromosome = strategy_info['chromosome']

                    # Boost fitness for top performers in each symbol
                    if i < 3:  # Top 3 performers per symbol
                        boost_factor = 1.0 + (0.1 * (3 - i))  # 30%, 20%, 10% boost
                        chromosome.fitness_score *= boost_factor

                    if chromosome not in enhanced_population:
                        enhanced_population.append(chromosome)

            # Add strategies not in symbol rankings
            for chromosome in population:
                if chromosome not in enhanced_population:
                    enhanced_population.append(chromosome)

            logger.info(f"[STATUS] Applied symbol-specific ranking to {len(enhanced_population)} strategies")
            return enhanced_population

        except Exception as e:
            logger.error(f"[ERROR] Error in symbol-specific ranking: {e}")
            return population

    def _prune_underperforming_strategies(self, population: List[StrategyChromosome]) -> List[StrategyChromosome]:
        """Prune underperforming strategies from population"""
        try:
            pruned_population = []
            pruned_count = 0

            for chromosome in population:
                # Check ROI threshold
                roi_performance = chromosome.symbol_performance
                avg_roi = np.mean(list(roi_performance.values())) if roi_performance else 0.0

                # Check if strategy meets minimum thresholds
                meets_roi_threshold = avg_roi >= self.evolution_config.underperformer_roi_threshold

                # For accuracy, we'll use fitness score as proxy
                meets_accuracy_threshold = chromosome.fitness_score >= self.evolution_config.underperformer_accuracy_threshold

                # Keep strategy if it meets thresholds OR is elite
                if meets_roi_threshold and meets_accuracy_threshold:
                    pruned_population.append(chromosome)
                elif chromosome in self.evolution_state.elite_strategies:
                    # Keep elite strategies even if they don't meet thresholds
                    pruned_population.append(chromosome)
                else:
                    pruned_count += 1
                    chromosome.status = StrategyStatus.DEPRECATED
                    logger.debug(f"🗑️ Pruned underperformer: {chromosome.strategy_name}")

            if pruned_count > 0:
                logger.info(f"🧹 Pruned {pruned_count} underperforming strategies")

            return pruned_population

        except Exception as e:
            logger.error(f"[ERROR] Error pruning underperformers: {e}")
            return population

    def _apply_niche_optimization(self, population: List[StrategyChromosome]) -> List[StrategyChromosome]:
        """Apply niche optimization for specialized strategies"""
        try:
            # Identify niches (high volatility, low liquidity, etc.)
            niches = {
                'high_volatility': [],
                'low_volatility': [],
                'high_volume': [],
                'low_volume': [],
                'trending': [],
                'mean_reverting': []
            }

            # Classify strategies into niches based on their characteristics
            for chromosome in population:
                # Analyze strategy characteristics
                niche = self._classify_strategy_niche(chromosome)
                if niche in niches:
                    niches[niche].append(chromosome)

            # Optimize each niche separately
            optimized_population = []

            for niche_name, niche_strategies in niches.items():
                if niche_strategies:
                    # Apply niche-specific optimization
                    optimized_niche = self._optimize_niche_strategies(niche_name, niche_strategies)
                    optimized_population.extend(optimized_niche)

            # Add strategies that don't fit any niche
            for chromosome in population:
                if chromosome not in optimized_population:
                    optimized_population.append(chromosome)

            return optimized_population

        except Exception as e:
            logger.error(f"[ERROR] Error in niche optimization: {e}")
            return population

    def _classify_strategy_niche(self, chromosome: StrategyChromosome) -> str:
        """Classify strategy into a niche based on its characteristics"""
        try:
            # Analyze gene values to determine niche
            genes = chromosome.genes

            # High volatility strategies (wider stops, larger positions)
            if ('stop_loss_pct' in genes and genes['stop_loss_pct'].value > 0.03) or \
               ('position_size_pct' in genes and genes['position_size_pct'].value > 0.08):
                return 'high_volatility'

            # Low volatility strategies (tighter stops, smaller positions)
            if ('stop_loss_pct' in genes and genes['stop_loss_pct'].value < 0.015) or \
               ('position_size_pct' in genes and genes['position_size_pct'].value < 0.03):
                return 'low_volatility'

            # Volume-based classification
            if 'volume_multiplier' in genes:
                vol_mult = genes['volume_multiplier'].value
                if vol_mult > 2.0:
                    return 'high_volume'
                elif vol_mult < 1.2:
                    return 'low_volume'

            # Trend vs mean reversion
            if 'ema_fast' in genes and 'ema_slow' in genes:
                # Trend following typically uses faster EMAs
                fast_ema = genes['ema_fast'].value
                if fast_ema <= 8:
                    return 'trending'

            if 'rsi_period' in genes:
                # Mean reversion typically uses shorter RSI periods
                rsi_period = genes['rsi_period'].value
                if rsi_period <= 14:
                    return 'mean_reverting'

            return 'general'  # Default niche

        except Exception as e:
            logger.warning(f"[WARN] Error classifying strategy niche: {e}")
            return 'general'

    def _optimize_niche_strategies(self, niche_name: str, strategies: List[StrategyChromosome]) -> List[StrategyChromosome]:
        """Optimize strategies within a specific niche"""
        try:
            # Apply niche-specific optimizations
            optimized_strategies = []

            for strategy in strategies:
                optimized_strategy = copy.deepcopy(strategy)

                # Apply niche-specific parameter adjustments
                if niche_name == 'high_volatility':
                    self._optimize_for_high_volatility(optimized_strategy)
                elif niche_name == 'low_volatility':
                    self._optimize_for_low_volatility(optimized_strategy)
                elif niche_name == 'trending':
                    self._optimize_for_trending(optimized_strategy)
                elif niche_name == 'mean_reverting':
                    self._optimize_for_mean_reversion(optimized_strategy)

                optimized_strategies.append(optimized_strategy)

            return optimized_strategies

        except Exception as e:
            logger.error(f"[ERROR] Error optimizing niche strategies: {e}")
            return strategies

    def _optimize_for_high_volatility(self, chromosome: StrategyChromosome):
        """Optimize strategy for high volatility conditions"""
        if 'stop_loss_pct' in chromosome.genes:
            # Wider stops for high volatility
            current_sl = chromosome.genes['stop_loss_pct'].value
            chromosome.genes['stop_loss_pct'].value = min(current_sl * 1.3, 0.05)

        if 'position_size_pct' in chromosome.genes:
            # Smaller positions for high volatility
            current_pos = chromosome.genes['position_size_pct'].value
            chromosome.genes['position_size_pct'].value = max(current_pos * 0.8, 0.01)

    def _optimize_for_low_volatility(self, chromosome: StrategyChromosome):
        """Optimize strategy for low volatility conditions"""
        if 'stop_loss_pct' in chromosome.genes:
            # Tighter stops for low volatility
            current_sl = chromosome.genes['stop_loss_pct'].value
            chromosome.genes['stop_loss_pct'].value = max(current_sl * 0.8, 0.005)

        if 'position_size_pct' in chromosome.genes:
            # Larger positions for low volatility
            current_pos = chromosome.genes['position_size_pct'].value
            chromosome.genes['position_size_pct'].value = min(current_pos * 1.2, 0.15)

    def _optimize_for_trending(self, chromosome: StrategyChromosome):
        """Optimize strategy for trending markets"""
        if 'ema_fast' in chromosome.genes:
            # Faster EMA for trend following
            chromosome.genes['ema_fast'].value = min(chromosome.genes['ema_fast'].value, 8)

        if 'take_profit_pct' in chromosome.genes:
            # Higher take profits in trending markets
            current_tp = chromosome.genes['take_profit_pct'].value
            chromosome.genes['take_profit_pct'].value = min(current_tp * 1.2, 0.10)

    def _optimize_for_mean_reversion(self, chromosome: StrategyChromosome):
        """Optimize strategy for mean reverting markets"""
        if 'rsi_period' in chromosome.genes:
            # Shorter RSI for mean reversion
            chromosome.genes['rsi_period'].value = min(chromosome.genes['rsi_period'].value, 14)

        if 'rsi_oversold' in chromosome.genes:
            # More extreme levels for mean reversion
            chromosome.genes['rsi_oversold'].value = 25

        if 'rsi_overbought' in chromosome.genes:
            chromosome.genes['rsi_overbought'].value = 75

    def _tournament_selection(self, population: List[StrategyChromosome], tournament_size: int = None) -> StrategyChromosome:
        """Select a chromosome using tournament selection"""
        if tournament_size is None:
            tournament_size = self.evolution_config.tournament_size

        # Randomly select tournament participants
        tournament_size = min(tournament_size, len(population))
        tournament = random.sample(population, tournament_size)

        # Return the best chromosome from tournament
        return max(tournament, key=lambda x: x.fitness_score)

    def _select_elite(self, population: List[StrategyChromosome]) -> List[StrategyChromosome]:
        """Select elite chromosomes from population"""
        elite_size = min(self.evolution_config.elite_size, len(population))

        # Sort by fitness score and select top performers
        sorted_population = sorted(population, key=lambda x: x.fitness_score, reverse=True)
        elite = sorted_population[:elite_size]

        # Update elite strategies
        self.evolution_state.elite_strategies = elite

        return elite

    async def _evolve_generation(self) -> List[StrategyChromosome]:
        """Enhanced evolution generation with advanced features"""
        try:
            logger.info(f"🧬 Evolving generation {self.generation_counter}")

            current_population = self.evolution_state.population

            # 1. Apply performance-driven selection
            current_population = await self.apply_performance_driven_selection(current_population)

            # 2. Evaluate fitness for all strategies
            await self._evaluate_population_fitness(current_population)

            # 3. Hyperparameter optimization for top performers
            if self.evolution_config.enable_hyperparameter_optimization:
                await self._optimize_top_performers(current_population)

            # 4. Select elite strategies
            elite = self._select_elite(current_population)

            # 5. Create new population
            new_population = elite.copy()  # Keep elite

            # 6. Generate offspring to fill remaining slots
            target_size = self.evolution_config.population_size

            while len(new_population) < target_size:
                # Selection
                parent1 = self._tournament_selection(current_population)
                parent2 = self._tournament_selection(current_population)

                # Crossover
                if random.random() < self.evolution_config.crossover_rate:
                    offspring1, offspring2 = self._crossover_chromosomes(parent1, parent2)
                else:
                    offspring1 = copy.deepcopy(parent1)
                    offspring2 = copy.deepcopy(parent2)
                    offspring1.strategy_id = str(uuid.uuid4())
                    offspring2.strategy_id = str(uuid.uuid4())

                # Enhanced mutation with multiple types
                offspring1 = self._mutate_chromosome(offspring1)
                offspring2 = self._mutate_chromosome(offspring2)

                # Risk-reward optimization
                if random.random() < 0.3:  # 30% chance
                    offspring1 = await self.optimize_risk_reward_combinations(offspring1)

                # Time-aware optimization
                if random.random() < 0.2:  # 20% chance
                    offspring2 = await self.optimize_time_windows(offspring2)

                # Add to new population
                new_population.extend([offspring1, offspring2])

            # 7. Autonomous strategy discovery (inject new strategies)
            if self.generation_counter % 5 == 0:  # Every 5 generations
                discovered_strategies = await self.discover_new_strategies()
                if discovered_strategies:
                    # Replace worst performers with discovered strategies
                    new_population.sort(key=lambda x: x.fitness_score, reverse=True)
                    num_to_replace = min(len(discovered_strategies), len(new_population) // 4)
                    new_population = new_population[:-num_to_replace] + discovered_strategies[:num_to_replace]

            # 8. Trim to exact size
            new_population = new_population[:target_size]

            # 9. Update generation counter
            self.generation_counter += 1

            # 10. Update evolution state
            self.evolution_state.population = new_population
            self.evolution_state.current_generation = self.generation_counter
            self.evolution_state.update_generation_stats()

            # 11. Generate YAML outputs for best strategies
            await self._generate_strategy_yamls(new_population[:5])  # Top 5 strategies

            logger.info(f"[SUCCESS] Generation {self.generation_counter} evolved: "
                       f"Best fitness: {self.evolution_state.best_fitness:.4f}, "
                       f"Avg fitness: {self.evolution_state.average_fitness:.4f}")

            return new_population

        except Exception as e:
            logger.error(f"[ERROR] Error evolving generation: {e}")
            raise

    async def _optimize_top_performers(self, population: List[StrategyChromosome]):
        """Apply hyperparameter optimization to top performing strategies"""
        try:
            # Sort by fitness and select top 20%
            sorted_population = sorted(population, key=lambda x: x.fitness_score, reverse=True)
            top_performers = sorted_population[:max(1, len(sorted_population) // 5)]

            optimization_tasks = []
            for chromosome in top_performers:
                task = self.optimize_strategy_hyperparameters(chromosome)
                optimization_tasks.append(task)

            # Run optimizations concurrently
            optimized_strategies = await asyncio.gather(*optimization_tasks, return_exceptions=True)

            # Replace original strategies with optimized versions
            for i, optimized in enumerate(optimized_strategies):
                if not isinstance(optimized, Exception) and optimized.fitness_score > top_performers[i].fitness_score:
                    # Find and replace in population
                    for j, chromosome in enumerate(population):
                        if chromosome.strategy_id == top_performers[i].strategy_id:
                            population[j] = optimized
                            break

            logger.info(f"[TARGET] Optimized {len(top_performers)} top performing strategies")

        except Exception as e:
            logger.error(f"[ERROR] Error optimizing top performers: {e}")

    async def _generate_strategy_yamls(self, strategies: List[StrategyChromosome]):
        """Generate YAML files for strategies"""
        try:
            yaml_dir = Path("data/evolved_strategies/yaml")
            yaml_dir.mkdir(parents=True, exist_ok=True)

            for strategy in strategies:
                yaml_content = self._convert_strategy_to_yaml(strategy)

                yaml_file = yaml_dir / f"{strategy.strategy_name}.yaml"
                with open(yaml_file, 'w', encoding='utf-8') as f:
                    f.write(yaml_content)

            logger.debug(f"📝 Generated YAML files for {len(strategies)} strategies")

        except Exception as e:
            logger.error(f"[ERROR] Error generating strategy YAMLs: {e}")

    def _convert_strategy_to_yaml(self, strategy: StrategyChromosome) -> str:
        """Convert strategy chromosome to YAML format"""
        try:
            # Extract key parameters
            genes = strategy.genes

            # Build long and short conditions from rules
            long_conditions = []
            short_conditions = []

            for rule in strategy.rules:
                if rule.rule_type == "entry_long":
                    long_conditions.append(rule.condition)
                elif rule.rule_type == "entry_short":
                    short_conditions.append(rule.condition)

            # Combine conditions
            long_condition = " and ".join(long_conditions) if long_conditions else "RSI_14 < 30"
            short_condition = " and ".join(short_conditions) if short_conditions else "RSI_14 > 70"

            # Calculate capital based on position size
            position_size = genes.get('position_size_pct', StrategyGene('', 0.05)).value
            base_capital = 100000  # Base capital
            strategy_capital = int(base_capital * position_size / 0.05)  # Adjust based on position size

            yaml_content = f"""# Generated Strategy: {strategy.strategy_name}
# Generation: {strategy.generation}
# Fitness Score: {strategy.fitness_score:.4f}
# Created: {strategy.creation_timestamp.isoformat()}

- name: {strategy.strategy_name}
  long: "{long_condition}"
  short: "{short_condition}"
  capital: {strategy_capital}

  # Risk Management
  stop_loss: {genes.get('stop_loss_pct', StrategyGene('', 0.02)).value:.3f}
  take_profit: {genes.get('take_profit_pct', StrategyGene('', 0.04)).value:.3f}
  position_size: {genes.get('position_size_pct', StrategyGene('', 0.05)).value:.3f}

  # Technical Parameters
  rsi_period: {int(genes.get('rsi_period', StrategyGene('', 14)).value)}
  ema_fast: {int(genes.get('ema_fast', StrategyGene('', 5)).value)}
  ema_slow: {int(genes.get('ema_slow', StrategyGene('', 20)).value)}
  volume_multiplier: {genes.get('volume_multiplier', StrategyGene('', 1.5)).value:.2f}

  # Performance Metrics
  fitness_score: {strategy.fitness_score:.4f}
  complexity_score: {strategy.complexity_score:.4f}
  best_rr_combo: {strategy.best_rr_combo}

  # Regime Performance
  regime_performance:
{self._format_regime_performance(strategy.regime_performance)}

  # Time Window Performance
  time_window_performance:
{self._format_time_window_performance(strategy.time_window_performance)}
"""

            return yaml_content

        except Exception as e:
            logger.error(f"[ERROR] Error converting strategy to YAML: {e}")
            return f"# Error generating YAML for {strategy.strategy_name}"

    def _format_regime_performance(self, regime_performance: Dict[str, float]) -> str:
        """Format regime performance for YAML"""
        if not regime_performance:
            return "    bull: 0.0\n    bear: 0.0\n    sideways: 0.0"

        formatted = ""
        for regime, performance in regime_performance.items():
            formatted += f"    {regime}: {performance:.4f}\n"

        return formatted.rstrip()

    def _format_time_window_performance(self, time_performance: Dict[str, float]) -> str:
        """Format time window performance for YAML"""
        if not time_performance:
            return "    morning: 0.0\n    midday: 0.0\n    afternoon: 0.0"

        formatted = ""
        for window, performance in time_performance.items():
            formatted += f"    {window}: {performance:.4f}\n"

        return formatted.rstrip()

    # ═══════════════════════════════════════════════════════════════════════════════
    # [STATUS] PERFORMANCE TRACKING AND EVALUATION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _evaluate_population_fitness(self, population: List[StrategyChromosome]):
        """Evaluate fitness for all strategies in population"""
        try:
            logger.info(f"[STATUS] Evaluating fitness for {len(population)} strategies")

            # Batch evaluate strategies for efficiency
            evaluation_tasks = []

            for chromosome in population:
                task = self._evaluate_strategy_fitness(chromosome)
                evaluation_tasks.append(task)

            # Execute evaluations concurrently
            results = await asyncio.gather(*evaluation_tasks, return_exceptions=True)

            # Update fitness scores
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.warning(f"[WARN] Fitness evaluation failed for strategy {population[i].strategy_name}: {result}")
                    population[i].fitness_score = 0.0  # Assign poor fitness
                else:
                    population[i].fitness_score = result

            logger.info("[SUCCESS] Population fitness evaluation completed")

        except Exception as e:
            logger.error(f"[ERROR] Error evaluating population fitness: {e}")
            raise

    async def _evaluate_strategy_fitness(self, chromosome: StrategyChromosome) -> float:
        """Evaluate fitness for a single strategy"""
        try:
            # Get performance metrics from Performance Analysis Agent
            if self.performance_agent:
                performance_data = await self._get_strategy_performance_data(chromosome)

                if performance_data:
                    # Create PerformanceMetrics object
                    metrics = self._create_performance_metrics(chromosome.strategy_id, performance_data)

                    # Calculate fitness score
                    fitness_weights = self.config.get('performance', {}).get('fitness_weights', {})
                    fitness_score = metrics.calculate_fitness_score(fitness_weights)

                    # Store performance history
                    if chromosome.strategy_id not in self.strategy_performance_history:
                        self.strategy_performance_history[chromosome.strategy_id] = []

                    self.strategy_performance_history[chromosome.strategy_id].append(metrics)

                    return fitness_score

            # Fallback: simulate performance if no real data available
            return await self._simulate_strategy_performance(chromosome)

        except Exception as e:
            logger.warning(f"[WARN] Error evaluating strategy fitness for {chromosome.strategy_name}: {e}")
            return 0.0

    async def _get_strategy_performance_data(self, chromosome: StrategyChromosome) -> Optional[Dict[str, Any]]:
        """Get performance data for a strategy from Performance Analysis Agent"""
        try:
            if not self.performance_agent:
                return None

            # Query performance data
            performance_data = await self.comm_interface.query_agent(
                agent_name="performance_analysis_agent",
                query_type="get_strategy_performance",
                parameters={
                    "strategy_name": chromosome.strategy_name,
                    "strategy_id": chromosome.strategy_id,
                    "period_days": self.evolution_config.performance_evaluation_period
                }
            )

            return performance_data

        except Exception as e:
            logger.warning(f"[WARN] Error getting performance data: {e}")
            return None

    def _create_performance_metrics(self, strategy_id: str, performance_data: Dict[str, Any]) -> PerformanceMetrics:
        """Create PerformanceMetrics object from performance data"""
        return PerformanceMetrics(
            strategy_id=strategy_id,
            roi=performance_data.get('roi', 0.0),
            sharpe_ratio=performance_data.get('sharpe_ratio', 0.0),
            max_drawdown=performance_data.get('max_drawdown', 0.0),
            profit_factor=performance_data.get('profit_factor', 1.0),
            win_rate=performance_data.get('win_rate', 0.0),
            expectancy=performance_data.get('expectancy', 0.0),
            total_trades=performance_data.get('total_trades', 0),
            avg_holding_period=performance_data.get('avg_holding_period', 0.0),
            volatility=performance_data.get('volatility', 0.0),
            calmar_ratio=performance_data.get('calmar_ratio', 0.0),
            sortino_ratio=performance_data.get('sortino_ratio', 0.0),
            bull_market_performance=performance_data.get('bull_market_performance', 0.0),
            bear_market_performance=performance_data.get('bear_market_performance', 0.0),
            sideways_market_performance=performance_data.get('sideways_market_performance', 0.0),
            evaluation_period_days=self.evolution_config.performance_evaluation_period
        )

    async def _simulate_strategy_performance(self, chromosome: StrategyChromosome) -> float:
        """Simulate strategy performance for fitness evaluation"""
        try:
            # This is a simplified simulation for demonstration
            # In practice, this would run a backtest or use historical data

            # Extract key parameters for simulation
            genes = chromosome.genes

            # Base fitness calculation based on gene values
            fitness = 0.0

            # Risk-adjusted scoring
            stop_loss = genes.get('stop_loss_pct', StrategyGene('', 0.02)).value
            take_profit = genes.get('take_profit_pct', StrategyGene('', 0.04)).value
            position_size = genes.get('position_size_pct', StrategyGene('', 0.05)).value

            # Risk-reward ratio scoring
            rr_ratio = take_profit / stop_loss if stop_loss > 0 else 1.0
            fitness += min(rr_ratio * 0.1, 0.3)  # Cap at 0.3

            # Position sizing scoring (prefer moderate sizes)
            if 0.02 <= position_size <= 0.08:
                fitness += 0.2
            else:
                fitness += 0.1

            # Parameter balance scoring
            if 'rsi_period' in genes:
                rsi_period = genes['rsi_period'].value
                if 10 <= rsi_period <= 20:
                    fitness += 0.1

            if 'volume_multiplier' in genes:
                vol_mult = genes['volume_multiplier'].value
                if 1.2 <= vol_mult <= 2.0:
                    fitness += 0.1

            # Add some randomness to simulate market uncertainty
            market_noise = random.gauss(0, 0.1)
            fitness += market_noise

            # Ensure non-negative fitness
            fitness = max(0.0, fitness)

            return fitness

        except Exception as e:
            logger.warning(f"[WARN] Error simulating strategy performance: {e}")
            return 0.0

    async def _track_strategy_performance_trends(self):
        """Track performance trends for all strategies"""
        try:
            improving_strategies = []
            degrading_strategies = []

            for strategy_id, performance_history in self.strategy_performance_history.items():
                if len(performance_history) >= 2:
                    # Calculate trend
                    recent_performance = performance_history[-3:]  # Last 3 evaluations

                    if len(recent_performance) >= 2:
                        trend = self._calculate_performance_trend(recent_performance)

                        if trend > 0.05:  # 5% improvement threshold
                            improving_strategies.append((strategy_id, trend))
                        elif trend < -0.05:  # 5% degradation threshold
                            degrading_strategies.append((strategy_id, trend))

            # Log trends
            if improving_strategies:
                logger.info(f"[METRICS] Improving strategies: {len(improving_strategies)}")
                for strategy_id, trend in improving_strategies[:5]:  # Top 5
                    strategy_name = self.active_strategies.get(strategy_id, {}).strategy_name
                    logger.info(f"  [METRICS] {strategy_name}: +{trend:.2%}")

            if degrading_strategies:
                logger.info(f"📉 Degrading strategies: {len(degrading_strategies)}")
                for strategy_id, trend in degrading_strategies[:5]:  # Bottom 5
                    strategy_name = self.active_strategies.get(strategy_id, {}).strategy_name
                    logger.info(f"  📉 {strategy_name}: {trend:.2%}")

            # Mark strategies for potential removal
            await self._mark_underperforming_strategies(degrading_strategies)

        except Exception as e:
            logger.error(f"[ERROR] Error tracking performance trends: {e}")

    def _calculate_performance_trend(self, performance_history: List[PerformanceMetrics]) -> float:
        """Calculate performance trend from history"""
        if len(performance_history) < 2:
            return 0.0

        # Use fitness scores for trend calculation
        fitness_scores = [metrics.calculate_fitness_score() for metrics in performance_history]

        # Simple linear trend calculation
        x = list(range(len(fitness_scores)))
        y = fitness_scores

        # Calculate slope
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))

        if n * sum_x2 - sum_x ** 2 == 0:
            return 0.0

        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)

        return slope

    async def _mark_underperforming_strategies(self, degrading_strategies: List[Tuple[str, float]]):
        """Mark underperforming strategies for removal or modification"""
        try:
            removal_threshold = -0.15  # 15% degradation

            for strategy_id, trend in degrading_strategies:
                if trend < removal_threshold:
                    # Mark for removal
                    if strategy_id in self.active_strategies:
                        strategy = self.active_strategies[strategy_id]
                        logger.warning(f"[WARN] Marking strategy for removal: {strategy.strategy_name} (trend: {trend:.2%})")

                        # Could implement strategy retirement logic here
                        # For now, just log the decision

        except Exception as e:
            logger.error(f"[ERROR] Error marking underperforming strategies: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🌊 MARKET REGIME ADAPTATION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _adapt_to_market_regime(self):
        """Adapt strategies based on current market regime"""
        try:
            if not self.evolution_config.regime_adaptation_enabled:
                return

            # Get current market regime
            current_regime = await self._get_current_market_regime()

            if current_regime:
                logger.info(f"🌊 Adapting strategies for {current_regime.value} market regime")

                # Analyze regime-specific performance
                await self._analyze_regime_performance(current_regime)

                # Evolve strategies for current regime
                if self.evolution_config.regime_specific_evolution:
                    await self._evolve_for_regime(current_regime)

                # Update regime-specific strategy allocation
                await self._update_regime_strategy_allocation(current_regime)

        except Exception as e:
            logger.error(f"[ERROR] Error adapting to market regime: {e}")

    async def _get_current_market_regime(self) -> Optional[MarketRegime]:
        """Get current market regime from Market Monitoring Agent"""
        try:
            if not self.market_monitoring_agent:
                return None

            # Query market regime
            regime_data = await self.comm_interface.query_agent(
                agent_name="market_monitoring_agent",
                query_type="get_market_regime",
                parameters={}
            )

            if regime_data and 'regime' in regime_data:
                regime_str = regime_data['regime'].lower()

                # Map to MarketRegime enum
                regime_mapping = {
                    'bull': MarketRegime.BULL,
                    'bear': MarketRegime.BEAR,
                    'sideways': MarketRegime.SIDEWAYS,
                    'high_volatility': MarketRegime.HIGH_VOLATILITY,
                    'low_volatility': MarketRegime.LOW_VOLATILITY
                }

                return regime_mapping.get(regime_str, MarketRegime.SIDEWAYS)

            return None

        except Exception as e:
            logger.warning(f"[WARN] Error getting market regime: {e}")
            return None

    async def _analyze_regime_performance(self, regime: MarketRegime):
        """Analyze strategy performance for specific market regime"""
        try:
            regime_performance = {}

            for strategy_id, performance_history in self.strategy_performance_history.items():
                if not performance_history:
                    continue

                # Get regime-specific performance
                regime_metrics = []

                for metrics in performance_history:
                    if regime == MarketRegime.BULL:
                        regime_perf = metrics.bull_market_performance
                    elif regime == MarketRegime.BEAR:
                        regime_perf = metrics.bear_market_performance
                    elif regime == MarketRegime.SIDEWAYS:
                        regime_perf = metrics.sideways_market_performance
                    else:
                        regime_perf = metrics.roi  # Default to overall ROI

                    regime_metrics.append(regime_perf)

                if regime_metrics:
                    avg_performance = np.mean(regime_metrics)
                    regime_performance[strategy_id] = avg_performance

            # Rank strategies by regime performance
            sorted_strategies = sorted(regime_performance.items(), key=lambda x: x[1], reverse=True)

            # Update regime-specific strategy lists
            top_performers = [strategy_id for strategy_id, _ in sorted_strategies[:10]]
            self.market_regime_strategies[regime] = top_performers

            logger.info(f"[STATUS] {regime.value} regime analysis: {len(sorted_strategies)} strategies evaluated")

            if sorted_strategies:
                best_strategy_id = sorted_strategies[0][0]
                best_performance = sorted_strategies[0][1]
                strategy_name = self.active_strategies.get(best_strategy_id, {}).strategy_name
                logger.info(f"🏆 Best {regime.value} strategy: {strategy_name} ({best_performance:.2%})")

        except Exception as e:
            logger.error(f"[ERROR] Error analyzing regime performance: {e}")

    async def _evolve_for_regime(self, regime: MarketRegime):
        """Evolve strategies specifically for a market regime"""
        try:
            # Get top performers for this regime
            regime_strategies = self.market_regime_strategies.get(regime, [])

            if not regime_strategies:
                logger.info(f"[WARN] No strategies found for {regime.value} regime")
                return

            # Create regime-specific population
            regime_population = []

            for strategy_id in regime_strategies[:5]:  # Top 5 performers
                if strategy_id in self.active_strategies:
                    strategy = self.active_strategies[strategy_id]
                    regime_population.append(strategy)

            if len(regime_population) < 2:
                logger.info(f"[WARN] Insufficient strategies for {regime.value} regime evolution")
                return

            # Apply regime-specific mutations
            evolved_strategies = []

            for strategy in regime_population:
                # Create regime-adapted variant
                adapted_strategy = self._adapt_strategy_for_regime(strategy, regime)
                evolved_strategies.append(adapted_strategy)

            # Add evolved strategies to population
            for evolved_strategy in evolved_strategies:
                self.active_strategies[evolved_strategy.strategy_id] = evolved_strategy
                self.evolution_state.population.append(evolved_strategy)

            logger.info(f"🧬 Evolved {len(evolved_strategies)} strategies for {regime.value} regime")

        except Exception as e:
            logger.error(f"[ERROR] Error evolving for regime: {e}")

    def _adapt_strategy_for_regime(self, strategy: StrategyChromosome, regime: MarketRegime) -> StrategyChromosome:
        """Adapt a strategy for a specific market regime"""
        adapted_strategy = copy.deepcopy(strategy)
        adapted_strategy.strategy_id = str(uuid.uuid4())
        adapted_strategy.strategy_name = f"{strategy.strategy_name}_{regime.value.upper()}"
        adapted_strategy.generation = self.generation_counter
        adapted_strategy.parent_ids = [strategy.strategy_id]
        adapted_strategy.creation_timestamp = datetime.now()

        # Apply regime-specific adaptations
        if regime == MarketRegime.BULL:
            # Bull market adaptations: more aggressive parameters
            if 'take_profit_pct' in adapted_strategy.genes:
                current_tp = adapted_strategy.genes['take_profit_pct'].value
                adapted_strategy.genes['take_profit_pct'].value = min(current_tp * 1.2, 0.10)

            if 'position_size_pct' in adapted_strategy.genes:
                current_pos = adapted_strategy.genes['position_size_pct'].value
                adapted_strategy.genes['position_size_pct'].value = min(current_pos * 1.1, 0.15)

        elif regime == MarketRegime.BEAR:
            # Bear market adaptations: more conservative parameters
            if 'stop_loss_pct' in adapted_strategy.genes:
                current_sl = adapted_strategy.genes['stop_loss_pct'].value
                adapted_strategy.genes['stop_loss_pct'].value = max(current_sl * 0.8, 0.005)

            if 'position_size_pct' in adapted_strategy.genes:
                current_pos = adapted_strategy.genes['position_size_pct'].value
                adapted_strategy.genes['position_size_pct'].value = max(current_pos * 0.8, 0.01)

        elif regime == MarketRegime.SIDEWAYS:
            # Sideways market adaptations: mean reversion focus
            if 'rsi_oversold' in adapted_strategy.genes:
                adapted_strategy.genes['rsi_oversold'].value = 25  # More extreme levels

            if 'rsi_overbought' in adapted_strategy.genes:
                adapted_strategy.genes['rsi_overbought'].value = 75

        elif regime == MarketRegime.HIGH_VOLATILITY:
            # High volatility adaptations: wider stops, smaller positions
            if 'stop_loss_pct' in adapted_strategy.genes:
                current_sl = adapted_strategy.genes['stop_loss_pct'].value
                adapted_strategy.genes['stop_loss_pct'].value = min(current_sl * 1.5, 0.05)

            if 'position_size_pct' in adapted_strategy.genes:
                current_pos = adapted_strategy.genes['position_size_pct'].value
                adapted_strategy.genes['position_size_pct'].value = max(current_pos * 0.7, 0.01)

        elif regime == MarketRegime.LOW_VOLATILITY:
            # Low volatility adaptations: tighter stops, larger positions
            if 'stop_loss_pct' in adapted_strategy.genes:
                current_sl = adapted_strategy.genes['stop_loss_pct'].value
                adapted_strategy.genes['stop_loss_pct'].value = max(current_sl * 0.7, 0.005)

            if 'position_size_pct' in adapted_strategy.genes:
                current_pos = adapted_strategy.genes['position_size_pct'].value
                adapted_strategy.genes['position_size_pct'].value = min(current_pos * 1.3, 0.15)

        return adapted_strategy

    async def _update_regime_strategy_allocation(self, regime: MarketRegime):
        """Update strategy allocation based on current regime"""
        try:
            # Get regime-specific top performers
            regime_strategies = self.market_regime_strategies.get(regime, [])

            if not regime_strategies:
                return

            # Calculate allocation weights based on regime performance
            allocation_weights = {}
            total_weight = 0.0

            for strategy_id in regime_strategies[:10]:  # Top 10 for regime
                if strategy_id in self.strategy_performance_history:
                    performance_history = self.strategy_performance_history[strategy_id]

                    if performance_history:
                        # Use recent performance for weighting
                        recent_performance = performance_history[-1]

                        if regime == MarketRegime.BULL:
                            weight = recent_performance.bull_market_performance
                        elif regime == MarketRegime.BEAR:
                            weight = recent_performance.bear_market_performance
                        elif regime == MarketRegime.SIDEWAYS:
                            weight = recent_performance.sideways_market_performance
                        else:
                            weight = recent_performance.roi

                        weight = max(0.0, weight)  # Ensure non-negative
                        allocation_weights[strategy_id] = weight
                        total_weight += weight

            # Normalize weights
            if total_weight > 0:
                for strategy_id in allocation_weights:
                    allocation_weights[strategy_id] /= total_weight

            # Log allocation
            logger.info(f"[STATUS] {regime.value} regime allocation updated for {len(allocation_weights)} strategies")

            # Store allocation for use by other agents
            self._store_regime_allocation(regime, allocation_weights)

        except Exception as e:
            logger.error(f"[ERROR] Error updating regime strategy allocation: {e}")

    def _store_regime_allocation(self, regime: MarketRegime, allocation_weights: Dict[str, float]):
        """Store regime allocation for use by other agents"""
        try:
            allocation_data = {
                'regime': regime.value,
                'timestamp': datetime.now().isoformat(),
                'allocations': {}
            }

            for strategy_id, weight in allocation_weights.items():
                if strategy_id in self.active_strategies:
                    strategy = self.active_strategies[strategy_id]
                    allocation_data['allocations'][strategy_id] = {
                        'strategy_name': strategy.strategy_name,
                        'weight': weight,
                        'fitness_score': strategy.fitness_score
                    }

            # Save to file
            allocation_file = Path(self.config.get('storage', {}).get('performance_dir', 'data/evolution_performance')) / f"regime_allocation_{regime.value}.json"

            with open(allocation_file, 'w', encoding='utf-8') as f:
                json.dump(allocation_data, f, indent=2)

            logger.debug(f"💾 Saved {regime.value} regime allocation to {allocation_file}")

        except Exception as e:
            logger.error(f"[ERROR] Error storing regime allocation: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # [AGENT] AUTONOMOUS STRATEGY DISCOVERY
    # ═══════════════════════════════════════════════════════════════════════════════

    async def discover_new_strategies(self) -> List[StrategyChromosome]:
        """Discover new strategies using autonomous methods"""
        try:
            if not self.evolution_config.enable_autonomous_discovery:
                return []

            discovered_strategies = []

            # 1. Symbolic regression for rule generation
            if self.evolution_config.symbolic_regression_enabled and GPLEARN_AVAILABLE:
                symbolic_strategies = await self._discover_via_symbolic_regression()
                discovered_strategies.extend(symbolic_strategies)

            # 2. Pattern discovery from historical data
            if self.evolution_config.pattern_discovery_enabled:
                pattern_strategies = await self._discover_via_pattern_analysis()
                discovered_strategies.extend(pattern_strategies)

            # 3. Template-based generation
            template_strategies = await self._generate_from_templates()
            discovered_strategies.extend(template_strategies)

            logger.info(f"[DEBUG] Discovered {len(discovered_strategies)} new strategies")
            return discovered_strategies

        except Exception as e:
            logger.error(f"[ERROR] Error in autonomous strategy discovery: {e}")
            return []

    async def _discover_via_symbolic_regression(self) -> List[StrategyChromosome]:
        """Discover strategies using symbolic regression"""
        try:
            if not GPLEARN_AVAILABLE:
                logger.warning("[WARN] gplearn not available for symbolic regression")
                return []

            # Create symbolic regressor
            regressor = SymbolicRegressor(
                population_size=100,
                generations=20,
                stopping_criteria=0.01,
                p_crossover=0.7,
                p_subtree_mutation=0.1,
                p_hoist_mutation=0.05,
                p_point_mutation=0.1,
                max_samples=0.9,
                verbose=1,
                parsimony_coefficient=0.01,
                random_state=42
            )

            # Generate synthetic training data (in practice, use real market data)
            X, y = self._generate_training_data_for_symbolic_regression()

            # Fit the regressor
            regressor.fit(X, y)

            # Extract discovered rules
            discovered_strategies = []

            # Convert symbolic expressions to trading rules
            for i, program in enumerate(regressor._programs[-1]):  # Get final generation
                if program.fitness_ < 0.1:  # Only good programs
                    strategy = self._convert_symbolic_program_to_strategy(program, i)
                    if strategy:
                        discovered_strategies.append(strategy)

            logger.info(f"🧬 Symbolic regression discovered {len(discovered_strategies)} strategies")
            return discovered_strategies

        except Exception as e:
            logger.error(f"[ERROR] Error in symbolic regression discovery: {e}")
            return []

    def _generate_training_data_for_symbolic_regression(self) -> Tuple[np.ndarray, np.ndarray]:
        """Generate training data for symbolic regression"""
        try:
            # Generate synthetic market data
            n_samples = 1000
            n_features = 10

            # Features: RSI, EMA ratios, Volume ratios, etc.
            X = np.random.randn(n_samples, n_features)

            # Target: Profitable trade signal (1 = profitable, 0 = not profitable)
            # Create synthetic relationship
            y = (X[:, 0] < -1.5) & (X[:, 1] > 0.5) & (X[:, 2] > X[:, 3])  # Example rule
            y = y.astype(float)

            # Add noise
            noise = np.random.normal(0, 0.1, n_samples)
            y = y + noise
            y = np.clip(y, 0, 1)

            return X, y

        except Exception as e:
            logger.error(f"[ERROR] Error generating training data: {e}")
            return np.array([]), np.array([])

    def _convert_symbolic_program_to_strategy(self, program, strategy_index: int) -> Optional[StrategyChromosome]:
        """Convert symbolic regression program to trading strategy"""
        try:
            # Extract the symbolic expression
            expression = str(program)

            # Create strategy chromosome
            strategy_id = str(uuid.uuid4())
            strategy_name = f"Symbolic_Strategy_{strategy_index}"

            # Create basic genes (these would be refined based on the expression)
            genes = {
                'rsi_period': StrategyGene('rsi_period', 14, 5, 50, 0.1, 'numeric'),
                'threshold_1': StrategyGene('threshold_1', 0.5, -2.0, 2.0, 0.1, 'numeric'),
                'threshold_2': StrategyGene('threshold_2', -0.5, -2.0, 2.0, 0.1, 'numeric'),
                'stop_loss_pct': StrategyGene('stop_loss_pct', 0.02, 0.005, 0.05, 0.1, 'numeric'),
                'take_profit_pct': StrategyGene('take_profit_pct', 0.04, 0.01, 0.10, 0.1, 'numeric'),
                'position_size_pct': StrategyGene('position_size_pct', 0.05, 0.01, 0.15, 0.1, 'numeric')
            }

            # Create rules based on symbolic expression
            rules = [
                StrategyRule(
                    rule_id=f"rule_{strategy_index}_long",
                    condition=f"({expression}) > 0.5",  # Simplified conversion
                    rule_type="entry_long",
                    indicators=["RSI_14", "EMA_5", "EMA_20", "Volume"],
                    parameters={"expression": expression}
                )
            ]

            chromosome = StrategyChromosome(
                strategy_id=strategy_id,
                strategy_name=strategy_name,
                genes=genes,
                rules=rules,
                generation=self.generation_counter,
                creation_timestamp=datetime.now()
            )

            return chromosome

        except Exception as e:
            logger.error(f"[ERROR] Error converting symbolic program: {e}")
            return None

    async def _discover_via_pattern_analysis(self) -> List[StrategyChromosome]:
        """Discover strategies via pattern analysis"""
        try:
            # This would analyze historical data for recurring patterns
            # For now, we'll create some pattern-based strategies

            pattern_strategies = []

            # Pattern 1: Morning gap reversal
            gap_reversal_strategy = self._create_pattern_strategy(
                "Morning_Gap_Reversal",
                "Gap > 0.02 and RSI_14 > 70 and Volume > Volume.rolling(20).mean() * 2",
                "Gap < -0.02 and RSI_14 < 30 and Volume > Volume.rolling(20).mean() * 2"
            )
            pattern_strategies.append(gap_reversal_strategy)

            # Pattern 2: Volume breakout
            volume_breakout_strategy = self._create_pattern_strategy(
                "Volume_Breakout",
                "Close > EMA_20 and Volume > Volume.rolling(20).mean() * 3 and RSI_14 > 50",
                "Close < EMA_20 and Volume > Volume.rolling(20).mean() * 3 and RSI_14 < 50"
            )
            pattern_strategies.append(volume_breakout_strategy)

            # Pattern 3: Bollinger squeeze
            bb_squeeze_strategy = self._create_pattern_strategy(
                "Bollinger_Squeeze",
                "BB_width < BB_width.rolling(20).mean() * 0.5 and Close > BB_upper",
                "BB_width < BB_width.rolling(20).mean() * 0.5 and Close < BB_lower"
            )
            pattern_strategies.append(bb_squeeze_strategy)

            logger.info(f"[METRICS] Pattern analysis discovered {len(pattern_strategies)} strategies")
            return pattern_strategies

        except Exception as e:
            logger.error(f"[ERROR] Error in pattern analysis discovery: {e}")
            return []

    def _create_pattern_strategy(self, name: str, long_condition: str, short_condition: str) -> StrategyChromosome:
        """Create a strategy from pattern conditions"""
        strategy_id = str(uuid.uuid4())

        # Extract indicators from conditions
        indicators = self._extract_indicators_from_condition(long_condition + " " + short_condition)

        # Create genes with pattern-specific parameters
        genes = {
            'pattern_threshold': StrategyGene('pattern_threshold', 0.02, 0.01, 0.05, 0.1, 'numeric'),
            'volume_multiplier': StrategyGene('volume_multiplier', 2.0, 1.5, 4.0, 0.1, 'numeric'),
            'rsi_period': StrategyGene('rsi_period', 14, 10, 21, 0.1, 'numeric'),
            'stop_loss_pct': StrategyGene('stop_loss_pct', 0.015, 0.005, 0.03, 0.1, 'numeric'),
            'take_profit_pct': StrategyGene('take_profit_pct', 0.03, 0.015, 0.06, 0.1, 'numeric'),
            'position_size_pct': StrategyGene('position_size_pct', 0.04, 0.02, 0.08, 0.1, 'numeric')
        }

        # Create rules
        rules = [
            StrategyRule(
                rule_id=f"{strategy_id}_long",
                condition=long_condition,
                rule_type="entry_long",
                indicators=indicators,
                parameters={}
            ),
            StrategyRule(
                rule_id=f"{strategy_id}_short",
                condition=short_condition,
                rule_type="entry_short",
                indicators=indicators,
                parameters={}
            )
        ]

        return StrategyChromosome(
            strategy_id=strategy_id,
            strategy_name=name,
            genes=genes,
            rules=rules,
            generation=self.generation_counter,
            creation_timestamp=datetime.now()
        )

    def _extract_indicators_from_condition(self, condition: str) -> List[str]:
        """Extract indicator names from condition string"""
        indicators = []

        # Common indicator patterns
        indicator_patterns = [
            r'RSI_\d+', r'EMA_\d+', r'SMA_\d+', r'MACD', r'Volume',
            r'BB_\w+', r'ATR', r'ADX', r'MFI', r'CCI', r'VWAP',
            r'Donchian_\w+', r'SuperTrend', r'Pivot_Point'
        ]

        for pattern in indicator_patterns:
            matches = re.findall(pattern, condition)
            indicators.extend(matches)

        return list(set(indicators))  # Remove duplicates

    async def _generate_from_templates(self) -> List[StrategyChromosome]:
        """Generate strategies from predefined templates"""
        try:
            templates = self._get_strategy_templates()
            generated_strategies = []

            for template in templates:
                # Generate multiple variants from each template
                for _ in range(3):  # 3 variants per template
                    strategy = self._generate_strategy_from_template(template)
                    generated_strategies.append(strategy)

            logger.info(f"[LIST] Template generation created {len(generated_strategies)} strategies")
            return generated_strategies

        except Exception as e:
            logger.error(f"[ERROR] Error generating from templates: {e}")
            return []

    def _get_strategy_templates(self) -> List[StrategyTemplate]:
        """Get predefined strategy templates"""
        templates = []

        # Mean reversion template
        mean_reversion_template = StrategyTemplate(
            template_name="Mean_Reversion",
            base_conditions=[
                "RSI_{period} < {oversold_level}",
                "Close < EMA_{ema_period} * {deviation_factor}"
            ],
            parameter_ranges={
                'period': HyperparameterSpace('period', 'int', 10, 21),
                'oversold_level': HyperparameterSpace('oversold_level', 'float', 20, 35),
                'ema_period': HyperparameterSpace('ema_period', 'int', 15, 30),
                'deviation_factor': HyperparameterSpace('deviation_factor', 'float', 0.98, 1.02)
            },
            required_indicators=["RSI", "EMA", "Close"],
            optional_indicators=["Volume", "VWAP"],
            fitness_targets=[
                OptimizationTarget("sharpe_ratio", 0.3, "maximize"),
                OptimizationTarget("max_drawdown", 0.2, "minimize")
            ]
        )
        templates.append(mean_reversion_template)

        # Momentum template
        momentum_template = StrategyTemplate(
            template_name="Momentum",
            base_conditions=[
                "EMA_{fast_period} > EMA_{slow_period}",
                "RSI_{rsi_period} > {momentum_threshold}",
                "Volume > Volume.rolling({volume_period}).mean() * {volume_multiplier}"
            ],
            parameter_ranges={
                'fast_period': HyperparameterSpace('fast_period', 'int', 5, 15),
                'slow_period': HyperparameterSpace('slow_period', 'int', 20, 50),
                'rsi_period': HyperparameterSpace('rsi_period', 'int', 10, 21),
                'momentum_threshold': HyperparameterSpace('momentum_threshold', 'float', 50, 70),
                'volume_period': HyperparameterSpace('volume_period', 'int', 15, 25),
                'volume_multiplier': HyperparameterSpace('volume_multiplier', 'float', 1.2, 2.5)
            },
            required_indicators=["EMA", "RSI", "Volume"],
            optional_indicators=["MACD", "ADX"],
            fitness_targets=[
                OptimizationTarget("roi", 0.4, "maximize"),
                OptimizationTarget("profit_factor", 0.3, "maximize")
            ]
        )
        templates.append(momentum_template)

        return templates

    def _generate_strategy_from_template(self, template: StrategyTemplate) -> StrategyChromosome:
        """Generate a strategy from a template"""
        strategy_id = str(uuid.uuid4())
        strategy_name = f"{template.template_name}_{random.randint(1000, 9999)}"

        # Generate random parameters within template ranges
        genes = {}
        template_params = {}

        for param_name, param_space in template.parameter_ranges.items():
            if param_space.param_type == 'int':
                value = random.randint(int(param_space.low), int(param_space.high))
            elif param_space.param_type == 'float':
                value = random.uniform(param_space.low, param_space.high)
            else:
                value = random.choice(param_space.choices) if param_space.choices else param_space.low

            template_params[param_name] = value

            # Create gene
            genes[param_name] = StrategyGene(
                param_name, value, param_space.low, param_space.high, 0.1, param_space.param_type
            )

        # Add standard risk management genes
        genes.update({
            'stop_loss_pct': StrategyGene('stop_loss_pct', 0.02, 0.005, 0.05, 0.1, 'numeric'),
            'take_profit_pct': StrategyGene('take_profit_pct', 0.04, 0.01, 0.10, 0.1, 'numeric'),
            'position_size_pct': StrategyGene('position_size_pct', 0.05, 0.01, 0.15, 0.1, 'numeric')
        })

        # Generate conditions from template
        rules = []
        for i, condition_template in enumerate(template.base_conditions):
            # Replace placeholders with actual values
            condition = condition_template.format(**template_params)

            rule = StrategyRule(
                rule_id=f"{strategy_id}_rule_{i}",
                condition=condition,
                rule_type="entry_long" if i == 0 else "filter",
                indicators=template.required_indicators,
                parameters=template_params
            )
            rules.append(rule)

        return StrategyChromosome(
            strategy_id=strategy_id,
            strategy_name=strategy_name,
            genes=genes,
            rules=rules,
            generation=self.generation_counter,
            creation_timestamp=datetime.now()
        )

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🔗 AGENT INTEGRATION AND COMMUNICATION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def start(self):
        """Start the Strategy Evolution Agent"""
        try:
            logger.info("[INIT] Starting Strategy Evolution Agent...")

            if not self.evolution_enabled:
                logger.warning("[WARN] Evolution is disabled, running in monitoring mode only")

            self.is_running = True

            # Start main evolution loop
            evolution_tasks = [
                self._evolution_loop(),
                self._performance_monitoring_loop(),
                self._regime_adaptation_loop(),
                self._strategy_management_loop()
            ]

            await asyncio.gather(*evolution_tasks)

        except Exception as e:
            logger.error(f"[ERROR] Error starting Strategy Evolution Agent: {e}")
            raise

    async def stop(self):
        """Stop the Strategy Evolution Agent"""
        logger.info("[STOP] Stopping Strategy Evolution Agent...")
        self.is_running = False

        # Save current state
        await self._save_evolution_state()

        logger.info("[SUCCESS] Strategy Evolution Agent stopped")

    async def _evolution_loop(self):
        """Main evolution loop"""
        while self.is_running:
            try:
                if self.evolution_enabled:
                    # Check if evolution should run
                    if await self._should_evolve():
                        # Evolve new generation
                        new_population = await self._evolve_generation()

                        # Save evolved strategies
                        await self._save_evolved_strategies(new_population)

                        # Check convergence
                        if await self._check_convergence():
                            logger.info("[TARGET] Evolution converged, pausing evolution")
                            await asyncio.sleep(3600)  # Wait 1 hour before next evolution cycle

                    # Wait before next evolution cycle
                    await asyncio.sleep(self.config.get('evolution_interval', 1800))  # 30 minutes default
                else:
                    await asyncio.sleep(60)  # Check every minute if evolution is re-enabled

            except Exception as e:
                logger.error(f"[ERROR] Error in evolution loop: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error

    async def _performance_monitoring_loop(self):
        """Performance monitoring loop"""
        while self.is_running:
            try:
                # Track performance trends
                await self._track_strategy_performance_trends()

                # Update strategy fitness scores
                if self.evolution_state.population:
                    await self._evaluate_population_fitness(self.evolution_state.population)

                # Generate performance reports
                await self._generate_performance_reports()

                # Wait before next monitoring cycle
                await asyncio.sleep(self.config.get('monitoring_interval', 900))  # 15 minutes default

            except Exception as e:
                logger.error(f"[ERROR] Error in performance monitoring loop: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error

    async def _regime_adaptation_loop(self):
        """Market regime adaptation loop"""
        while self.is_running:
            try:
                if self.evolution_config.regime_adaptation_enabled:
                    # Adapt to current market regime
                    await self._adapt_to_market_regime()

                # Wait before next adaptation cycle
                await asyncio.sleep(self.config.get('regime_adaptation_interval', 1800))  # 30 minutes default

            except Exception as e:
                logger.error(f"[ERROR] Error in regime adaptation loop: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error

    async def _strategy_management_loop(self):
        """Strategy management and cleanup loop"""
        while self.is_running:
            try:
                # Clean up old strategies
                await self._cleanup_old_strategies()

                # Backup strategies
                await self._backup_strategies()

                # Update strategy status
                await self._update_strategy_status()

                # Wait before next management cycle
                await asyncio.sleep(self.config.get('management_interval', 3600))  # 1 hour default

            except Exception as e:
                logger.error(f"[ERROR] Error in strategy management loop: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error

    async def _should_evolve(self) -> bool:
        """Check if evolution should run"""
        try:
            # Check if enough time has passed since last evolution
            if self.evolution_state.generation_timestamps:
                last_evolution = self.evolution_state.generation_timestamps[-1]
                time_since_last = datetime.now() - last_evolution

                min_evolution_interval = timedelta(minutes=self.config.get('min_evolution_interval_minutes', 30))

                if time_since_last < min_evolution_interval:
                    return False

            # Check if we have enough performance data
            strategies_with_data = sum(1 for history in self.strategy_performance_history.values() if history)

            if strategies_with_data < self.evolution_config.population_size * 0.5:
                logger.info("⏳ Waiting for more performance data before evolution")
                return False

            # Check if we haven't reached max generations
            if self.evolution_state.current_generation >= self.evolution_config.max_generations:
                logger.info("🏁 Maximum generations reached")
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Error checking evolution conditions: {e}")
            return False

    async def _check_convergence(self) -> bool:
        """Check if evolution has converged"""
        try:
            if len(self.evolution_state.fitness_history) < 5:
                return False

            # Check if fitness has plateaued
            recent_fitness = self.evolution_state.fitness_history[-5:]
            fitness_variance = np.var(recent_fitness)

            if fitness_variance < self.evolution_config.convergence_threshold:
                self.evolution_state.convergence_counter += 1
            else:
                self.evolution_state.convergence_counter = 0

            # Converged if fitness has been stable for multiple generations
            return self.evolution_state.convergence_counter >= 3

        except Exception as e:
            logger.error(f"[ERROR] Error checking convergence: {e}")
            return False

    async def _save_evolved_strategies(self, strategies: List[StrategyChromosome]):
        """Save evolved strategies to storage"""
        try:
            strategies_dir = Path(self.config.get('storage', {}).get('strategies_dir', 'data/evolved_strategies'))

            for strategy in strategies:
                strategy_file = strategies_dir / f"{strategy.strategy_id}.json"

                with open(strategy_file, 'w', encoding='utf-8') as f:
                    json.dump(strategy.to_dict(), f, indent=2)

            logger.debug(f"💾 Saved {len(strategies)} evolved strategies")

        except Exception as e:
            logger.error(f"[ERROR] Error saving evolved strategies: {e}")

    async def _save_evolution_state(self):
        """Save current evolution state"""
        try:
            state_data = {
                'current_generation': self.evolution_state.current_generation,
                'best_fitness': self.evolution_state.best_fitness,
                'average_fitness': self.evolution_state.average_fitness,
                'fitness_history': self.evolution_state.fitness_history,
                'generation_timestamps': [ts.isoformat() for ts in self.evolution_state.generation_timestamps],
                'convergence_counter': self.evolution_state.convergence_counter,
                'last_improvement_generation': self.evolution_state.last_improvement_generation,
                'population_size': len(self.evolution_state.population),
                'elite_size': len(self.evolution_state.elite_strategies)
            }

            state_file = Path(self.config.get('storage', {}).get('performance_dir', 'data/evolution_performance')) / 'evolution_state.json'

            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2)

            logger.debug("💾 Evolution state saved")

        except Exception as e:
            logger.error(f"[ERROR] Error saving evolution state: {e}")

    async def _generate_performance_reports(self):
        """Generate performance reports"""
        try:
            # Generate summary report
            report_data = {
                'timestamp': datetime.now().isoformat(),
                'evolution_stats': {
                    'current_generation': self.evolution_state.current_generation,
                    'population_size': len(self.evolution_state.population),
                    'best_fitness': self.evolution_state.best_fitness,
                    'average_fitness': self.evolution_state.average_fitness,
                    'elite_count': len(self.evolution_state.elite_strategies)
                },
                'strategy_stats': {
                    'total_active_strategies': len(self.active_strategies),
                    'strategies_with_performance_data': len(self.strategy_performance_history),
                    'regime_strategies': {regime.value: len(strategies)
                                        for regime, strategies in self.market_regime_strategies.items()}
                },
                'top_performers': []
            }

            # Add top performing strategies
            if self.evolution_state.elite_strategies:
                for strategy in self.evolution_state.elite_strategies[:5]:
                    report_data['top_performers'].append({
                        'strategy_name': strategy.strategy_name,
                        'fitness_score': strategy.fitness_score,
                        'generation': strategy.generation,
                        'creation_timestamp': strategy.creation_timestamp.isoformat()
                    })

            # Save report
            report_file = Path(self.config.get('storage', {}).get('performance_dir', 'data/evolution_performance')) / f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2)

            logger.debug(f"[STATUS] Performance report generated: {report_file}")

        except Exception as e:
            logger.error(f"[ERROR] Error generating performance reports: {e}")

    async def _cleanup_old_strategies(self):
        """Clean up old and underperforming strategies"""
        try:
            current_time = datetime.now()
            strategies_to_remove = []

            for strategy_id, strategy in self.active_strategies.items():
                # Remove strategies older than 30 days with poor performance
                age_days = (current_time - strategy.creation_timestamp).days

                if age_days > 30 and strategy.fitness_score < 0.1:
                    strategies_to_remove.append(strategy_id)

                # Remove strategies with consistently poor performance
                if strategy_id in self.strategy_performance_history:
                    performance_history = self.strategy_performance_history[strategy_id]

                    if len(performance_history) >= 5:
                        recent_performance = [metrics.calculate_fitness_score() for metrics in performance_history[-5:]]
                        avg_recent_performance = np.mean(recent_performance)

                        if avg_recent_performance < 0.05:  # Very poor performance
                            strategies_to_remove.append(strategy_id)

            # Remove identified strategies
            for strategy_id in strategies_to_remove:
                if strategy_id in self.active_strategies:
                    strategy_name = self.active_strategies[strategy_id].strategy_name
                    del self.active_strategies[strategy_id]
                    logger.info(f"🗑️ Removed underperforming strategy: {strategy_name}")

                if strategy_id in self.strategy_performance_history:
                    del self.strategy_performance_history[strategy_id]

            # Update population
            self.evolution_state.population = [s for s in self.evolution_state.population
                                             if s.strategy_id not in strategies_to_remove]

            if strategies_to_remove:
                logger.info(f"🧹 Cleaned up {len(strategies_to_remove)} old strategies")

        except Exception as e:
            logger.error(f"[ERROR] Error cleaning up strategies: {e}")

    async def _backup_strategies(self):
        """Backup current strategies"""
        try:
            backup_dir = Path(self.config.get('storage', {}).get('backup_dir', 'data/evolution_backups'))
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = backup_dir / f"strategies_backup_{timestamp}.json"

            backup_data = {
                'timestamp': datetime.now().isoformat(),
                'strategies': [strategy.to_dict() for strategy in self.active_strategies.values()],
                'evolution_state': {
                    'current_generation': self.evolution_state.current_generation,
                    'best_fitness': self.evolution_state.best_fitness,
                    'average_fitness': self.evolution_state.average_fitness
                }
            }

            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2)

            logger.debug(f"💾 Strategies backed up to {backup_file}")

            # Clean up old backups (keep last 10)
            backup_files = sorted(backup_dir.glob('strategies_backup_*.json'))
            if len(backup_files) > 10:
                for old_backup in backup_files[:-10]:
                    old_backup.unlink()
                    logger.debug(f"🗑️ Removed old backup: {old_backup}")

        except Exception as e:
            logger.error(f"[ERROR] Error backing up strategies: {e}")

    async def _update_strategy_status(self):
        """Update strategy status based on performance"""
        try:
            for strategy_id, strategy in self.active_strategies.items():
                if strategy_id in self.strategy_performance_history:
                    performance_history = self.strategy_performance_history[strategy_id]

                    if performance_history:
                        latest_performance = performance_history[-1]
                        fitness_score = latest_performance.calculate_fitness_score()

                        # Update fitness score
                        strategy.fitness_score = fitness_score

                        # Determine status based on performance
                        if fitness_score > 0.8:
                            # Champion strategy
                            logger.debug(f"🏆 Champion strategy: {strategy.strategy_name}")
                        elif fitness_score > 0.5:
                            # Good performing strategy
                            logger.debug(f"[SUCCESS] Good strategy: {strategy.strategy_name}")
                        elif fitness_score > 0.2:
                            # Average strategy
                            logger.debug(f"[STATUS] Average strategy: {strategy.strategy_name}")
                        else:
                            # Poor performing strategy
                            logger.debug(f"[WARN] Poor strategy: {strategy.strategy_name}")

        except Exception as e:
            logger.error(f"[ERROR] Error updating strategy status: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # [CONFIG] UTILITY METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    def get_best_strategies(self, count: int = 10) -> List[StrategyChromosome]:
        """Get the best performing strategies"""
        try:
            sorted_strategies = sorted(self.active_strategies.values(),
                                     key=lambda x: x.fitness_score, reverse=True)
            return sorted_strategies[:count]
        except Exception as e:
            logger.error(f"[ERROR] Error getting best strategies: {e}")
            return []

    def get_strategy_by_id(self, strategy_id: str) -> Optional[StrategyChromosome]:
        """Get strategy by ID"""
        return self.active_strategies.get(strategy_id)

    def get_strategy_performance_history(self, strategy_id: str) -> List[PerformanceMetrics]:
        """Get performance history for a strategy"""
        return self.strategy_performance_history.get(strategy_id, [])

    def get_evolution_statistics(self) -> Dict[str, Any]:
        """Get evolution statistics"""
        return {
            'current_generation': self.evolution_state.current_generation,
            'population_size': len(self.evolution_state.population),
            'best_fitness': self.evolution_state.best_fitness,
            'average_fitness': self.evolution_state.average_fitness,
            'elite_count': len(self.evolution_state.elite_strategies),
            'total_strategies': len(self.active_strategies),
            'strategies_with_data': len(self.strategy_performance_history),
            'convergence_counter': self.evolution_state.convergence_counter,
            'last_improvement_generation': self.evolution_state.last_improvement_generation
        }

    async def force_evolution(self) -> bool:
        """Force evolution of a new generation"""
        try:
            if not self.evolution_enabled:
                logger.warning("[WARN] Evolution is disabled")
                return False

            logger.info("[WORKFLOW] Forcing evolution of new generation")
            new_population = await self._evolve_generation()
            await self._save_evolved_strategies(new_population)

            return True

        except Exception as e:
            logger.error(f"[ERROR] Error forcing evolution: {e}")
            return False

    def enable_evolution(self):
        """Enable evolution"""
        self.evolution_enabled = True
        logger.info("[SUCCESS] Evolution enabled")

    def disable_evolution(self):
        """Disable evolution"""
        self.evolution_enabled = False
        logger.info("⏸️ Evolution disabled")

    async def export_strategies(self, file_path: str) -> bool:
        """Export strategies to file"""
        try:
            export_data = {
                'timestamp': datetime.now().isoformat(),
                'evolution_agent_version': '1.0.0',
                'strategies': [strategy.to_dict() for strategy in self.active_strategies.values()],
                'evolution_state': {
                    'current_generation': self.evolution_state.current_generation,
                    'best_fitness': self.evolution_state.best_fitness,
                    'average_fitness': self.evolution_state.average_fitness,
                    'fitness_history': self.evolution_state.fitness_history
                }
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2)

            logger.info(f"📤 Strategies exported to {file_path}")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Error exporting strategies: {e}")
            return False

    async def import_strategies(self, file_path: str) -> bool:
        """Import strategies from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)

            imported_strategies = import_data.get('strategies', [])

            for strategy_data in imported_strategies:
                chromosome = self._dict_to_chromosome(strategy_data)
                self.active_strategies[chromosome.strategy_id] = chromosome
                self.evolution_state.population.append(chromosome)

            logger.info(f"📥 Imported {len(imported_strategies)} strategies from {file_path}")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Error importing strategies: {e}")
            return False


# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main execution function for testing"""
    try:
        # Initialize agent
        agent = StrategyEvolutionAgent()

        # Setup agent
        await agent.setup()

        # Start agent
        await agent.start()

    except KeyboardInterrupt:
        logger.info("[STOP] Received interrupt signal")
        if 'agent' in locals():
            await agent.stop()
    except Exception as e:
        logger.error(f"[ERROR] Error in main execution: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
