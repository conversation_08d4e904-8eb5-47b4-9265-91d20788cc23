#!/usr/bin/env python3
"""
Test SmartAPI Margin Calculation Functionality
Tests the getMarginApi functionality for calculating margin requirements for intraday trades
"""

import os
import sys
import asyncio
import logging
import yaml
from datetime import datetime
from typing import Dict, Any, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import Angel API utilities
try:
    from utils.angel_api import AngelOneAPIClient, MarginRequirement
except ImportError as e:
    print(f"❌ Failed to import Angel API utilities: {e}")
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MarginCalculationTester:
    """Test class for SmartAPI margin calculation functionality"""
    
    def __init__(self):
        """Initialize the tester"""
        self.config = self._load_config()
        self.angel_api = None
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from environment variables"""
        config = {
            'angel_one_api': {
                'api_key': os.getenv('SMARTAPI_API_KEY'),
                'username': os.getenv('SMARTAPI_USERNAME'),
                'password': os.getenv('SMARTAPI_PASSWORD'),
                'totp_token': os.getenv('SMARTAPI_TOTP_TOKEN'),
                'timeout': 10,
                'max_retries': 3,
                'retry_delay': 2,
                'requests_per_second': 10
            },
            'system': {
                'performance': {
                    'cache_duration_seconds': 30
                }
            },
            'capital_allocation': {
                'intraday_margin': {
                    'margin_buffer_percent': 10.0
                }
            }
        }
        
        # Validate required credentials
        required_fields = ['api_key', 'username', 'password', 'totp_token']
        missing_fields = [field for field in required_fields if not config['angel_one_api'][field]]
        
        if missing_fields:
            logger.error(f"❌ Missing required environment variables: {missing_fields}")
            logger.error("Please set: SMARTAPI_API_KEY, SMARTAPI_USERNAME, SMARTAPI_PASSWORD, SMARTAPI_TOTP_TOKEN")
            return {}
        
        return config
    
    async def initialize(self) -> bool:
        """Initialize Angel One API client"""
        try:
            if not self.config:
                return False
                
            self.angel_api = AngelOneAPIClient(self.config)
            auth_success = await self.angel_api.authenticate()
            
            if auth_success:
                logger.info("✅ Angel One API client initialized and authenticated")
                return True
            else:
                logger.error("❌ Failed to authenticate with Angel One API")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error initializing Angel One API: {e}")
            return False
    
    async def test_margin_calculation_basic(self) -> bool:
        """Test basic margin calculation functionality"""
        try:
            logger.info("🧪 Testing basic margin calculation...")

            # Test parameters for commonly traded stocks with smaller quantities
            test_cases = [
                {
                    'symbol': 'RELIANCE-EQ',
                    'exchange': 'NSE',
                    'quantity': 5,
                    'price': 2500.0,
                    'transaction_type': 'BUY',
                    'product_type': 'MIS'
                },
                {
                    'symbol': 'TCS-EQ',
                    'exchange': 'NSE',
                    'quantity': 3,
                    'price': 3500.0,
                    'transaction_type': 'BUY',
                    'product_type': 'MIS'
                }
            ]

            successful_tests = 0

            for i, test_params in enumerate(test_cases):
                logger.info(f"📊 Test {i+1}: {test_params['symbol']}")

                margin_req = await self.angel_api.get_margin_requirement(**test_params)

                if margin_req is None:
                    logger.error(f"❌ Margin calculation returned None for {test_params['symbol']}")
                    continue

                if margin_req.error_message:
                    logger.error(f"❌ Margin calculation error for {test_params['symbol']}: {margin_req.error_message}")
                    continue

                # Log results
                logger.info(f"✅ Margin calculation successful for {test_params['symbol']}:")
                logger.info(f"   Quantity: {margin_req.quantity}")
                logger.info(f"   Price: ₹{margin_req.price:,.2f}")
                logger.info(f"   Product Type: {margin_req.product_type}")
                logger.info(f"   Margin Required: ₹{margin_req.margin_required:,.2f}")
                logger.info(f"   Available Margin: ₹{margin_req.available_margin:,.2f}")
                logger.info(f"   Trade Allowed: {margin_req.is_allowed}")
                logger.info(f"   Limit Used: {margin_req.limit_used_percent:.2f}%")

                successful_tests += 1

            # Consider test successful if at least one margin calculation worked
            if successful_tests > 0:
                logger.info(f"✅ Basic margin calculation test passed ({successful_tests}/{len(test_cases)} successful)")
                return True
            else:
                logger.error("❌ All margin calculations failed")
                return False

        except Exception as e:
            logger.error(f"❌ Error in basic margin calculation test: {e}")
            return False
    
    async def test_margin_calculation_multiple_stocks(self) -> bool:
        """Test margin calculation for multiple stocks"""
        try:
            logger.info("🧪 Testing margin calculation for multiple stocks...")
            
            # Test parameters for different stocks
            test_stocks = [
                {'symbol': 'RELIANCE-EQ', 'exchange': 'NSE', 'quantity': 10, 'price': 2500.0},
                {'symbol': 'TCS-EQ', 'exchange': 'NSE', 'quantity': 5, 'price': 3500.0},
                {'symbol': 'INFY-EQ', 'exchange': 'NSE', 'quantity': 15, 'price': 1800.0},
                {'symbol': 'ICICIBANK-EQ', 'exchange': 'NSE', 'quantity': 25, 'price': 1200.0},
                {'symbol': 'SBIN-EQ', 'exchange': 'NSE', 'quantity': 50, 'price': 800.0}
            ]
            
            results = []
            
            for stock in test_stocks:
                margin_req = await self.angel_api.get_margin_requirement(
                    symbol=stock['symbol'],
                    exchange=stock['exchange'],
                    quantity=stock['quantity'],
                    price=stock['price'],
                    transaction_type='BUY',
                    product_type='MIS'
                )
                
                if margin_req and not margin_req.error_message:
                    results.append(margin_req)
                    logger.info(f"✅ {stock['symbol']}: Margin ₹{margin_req.margin_required:,.2f}, Allowed: {margin_req.is_allowed}")
                else:
                    error_msg = margin_req.error_message if margin_req else "Unknown error"
                    logger.warning(f"⚠️ {stock['symbol']}: Failed - {error_msg}")
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.5)
            
            if results:
                total_margin = sum(r.margin_required for r in results)
                logger.info(f"✅ Multiple stock test completed. Total margin for all stocks: ₹{total_margin:,.2f}")
                return True
            else:
                logger.error("❌ No successful margin calculations")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error in multiple stocks margin calculation test: {e}")
            return False
    
    async def test_margin_validation(self) -> bool:
        """Test margin validation functionality"""
        try:
            logger.info("🧪 Testing margin validation...")
            
            # Test with a reasonable trade
            is_valid, reason, margin_req = await self.angel_api.validate_trade_margin(
                symbol='HDFCBANK-EQ',
                exchange='NSE',
                quantity=10,
                price=1500.0,
                transaction_type='BUY',
                product_type='MIS'
            )
            
            logger.info(f"✅ Margin validation result:")
            logger.info(f"   Valid: {is_valid}")
            logger.info(f"   Reason: {reason}")
            
            if margin_req:
                logger.info(f"   Margin Required: ₹{margin_req.margin_required:,.2f}")
                logger.info(f"   Available Margin: ₹{margin_req.available_margin:,.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error in margin validation test: {e}")
            return False
    
    async def test_rms_limits(self) -> bool:
        """Test RMS limits functionality"""
        try:
            logger.info("🧪 Testing RMS limits...")
            
            rms_limits = await self.angel_api.get_rms_limits()
            
            if rms_limits is None:
                logger.error("❌ Failed to fetch RMS limits")
                return False
            
            logger.info(f"✅ RMS limits fetched successfully:")
            logger.info(f"   Net Available Cash: ₹{rms_limits.net_available_cash:,.2f}")
            logger.info(f"   Available Margin: ₹{rms_limits.available_margin:,.2f}")
            logger.info(f"   Utilized Margin: ₹{rms_limits.utilized_margin:,.2f}")
            logger.info(f"   Margin Used: {rms_limits.margin_used_percent:.2f}%")
            logger.info(f"   Gross Collateral Value: ₹{rms_limits.gross_collateral_value:,.2f}")
            logger.info(f"   Net Collateral Value: ₹{rms_limits.net_collateral_value:,.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error in RMS limits test: {e}")
            return False
    
    async def run_all_tests(self) -> bool:
        """Run all margin calculation tests"""
        try:
            logger.info("🚀 Starting SmartAPI Margin Calculation Tests...")
            
            # Initialize API client
            if not await self.initialize():
                logger.error("❌ Failed to initialize API client")
                return False
            
            # Run tests
            tests = [
                ("Basic Margin Calculation", self.test_margin_calculation_basic),
                ("Multiple Stocks Margin Calculation", self.test_margin_calculation_multiple_stocks),
                ("Margin Validation", self.test_margin_validation),
                ("RMS Limits", self.test_rms_limits)
            ]
            
            passed_tests = 0
            total_tests = len(tests)
            
            for test_name, test_func in tests:
                logger.info(f"\n{'='*60}")
                logger.info(f"Running: {test_name}")
                logger.info(f"{'='*60}")
                
                try:
                    result = await test_func()
                    if result:
                        logger.info(f"✅ {test_name} - PASSED")
                        passed_tests += 1
                    else:
                        logger.error(f"❌ {test_name} - FAILED")
                except Exception as e:
                    logger.error(f"❌ {test_name} - ERROR: {e}")
                
                # Small delay between tests
                await asyncio.sleep(1)
            
            # Summary
            logger.info(f"\n{'='*60}")
            logger.info(f"TEST SUMMARY")
            logger.info(f"{'='*60}")
            logger.info(f"Passed: {passed_tests}/{total_tests}")
            logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
            
            if passed_tests == total_tests:
                logger.info("🎉 All tests passed!")
                return True
            else:
                logger.warning(f"⚠️ {total_tests - passed_tests} test(s) failed")
                return False
            
        except Exception as e:
            logger.error(f"❌ Error running tests: {e}")
            return False
        finally:
            # Cleanup
            if self.angel_api:
                await self.angel_api.close_session()

async def main():
    """Main function to run margin calculation tests"""
    tester = MarginCalculationTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n✅ All margin calculation tests completed successfully!")
        return 0
    else:
        print("\n❌ Some tests failed. Check logs for details.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
