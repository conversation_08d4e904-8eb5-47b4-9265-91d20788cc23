#!/usr/bin/env python3
"""
Complete Integration Test for LLM Interface Agent

This test suite validates the complete LLM Interface Agent system including
all components: Agent Communication, Code Generation, Angel One Integration,
Configuration Management, and Interactive Debugging.

Author: AI Assistant
Date: 2025-01-16
"""

import unittest
import asyncio
import sys
import os
import tempfile
import yaml
import json
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.llm_interface_agent import LLMInterfaceAgent, QueryRequest, QueryResponse
from agents.agent_communication_interface import AgentCommunicationInterface, MessageType
from agents.code_generation_autopatch import CodeGenerationAutoPatch, CodeGenerationRequest, CodeType
from agents.angel_one_integration import AngelOneIntegration, TransactionType, OrderType
from agents.config_documentation_system import ConfigDocumentationSystem, ConfigType
from agents.interactive_debugging_explanations import InteractiveDebuggingSystem, ExplanationRequest

class TestCompleteIntegration(unittest.TestCase):
    """Complete integration tests for LLM Interface Agent system"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        
        # Initialize all components
        self.llm_agent = LLMInterfaceAgent()
        self.comm_interface = AgentCommunicationInterface("test_llm")
        self.code_generator = CodeGenerationAutoPatch(self.temp_dir)
        self.angel_integration = AngelOneIntegration()
        self.config_system = ConfigDocumentationSystem(self.temp_dir)
        self.debug_system = InteractiveDebuggingSystem(self.temp_dir)
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    async def test_end_to_end_query_processing(self):
        """Test complete end-to-end query processing"""
        print("\n🧪 Testing End-to-End Query Processing...")
        
        # Initialize communication interface
        await self.comm_interface.initialize()
        
        # Register mock agents
        mock_performance_agent = Mock()
        mock_performance_agent.get_strategy_performance = AsyncMock(return_value={
            'roi_metrics': {'total_roi': 15.5, 'avg_roi': 2.3, 'best_strategy': 'RSI_Momentum'},
            'accuracy_metrics': {'overall_accuracy': 68.5, 'win_rate': 65.2}
        })
        
        await self.comm_interface.register_agent(
            "performance_analysis_agent", 
            mock_performance_agent, 
            "analysis_agent"
        )
        
        # Test query processing
        query = "What's the ROI of my RSI strategy last week?"
        query_request = QueryRequest(query=query)
        
        # Mock the LLM agent's processing
        with patch.object(self.llm_agent, '_process_with_fallback') as mock_process:
            mock_process.return_value = QueryResponse(
                response="📊 RSI Strategy Performance: 15.5% ROI with 68.5% accuracy",
                agent_used="performance_analysis_agent",
                model_used="qwen3-8b",
                confidence=0.85
            )
            
            response = await self.llm_agent._process_with_fallback(query_request)
            
            self.assertIsInstance(response, QueryResponse)
            self.assertIn("RSI Strategy", response.response)
            self.assertEqual(response.agent_used, "performance_analysis_agent")
        
        print("✅ End-to-end query processing test passed")
    
    async def test_code_generation_integration(self):
        """Test code generation and auto-patching integration"""
        print("\n🧪 Testing Code Generation Integration...")
        
        # Test strategy generation
        request = CodeGenerationRequest(
            description="Create a momentum breakout strategy using RSI and volume confirmation",
            code_type=CodeType.STRATEGY,
            language="python"
        )
        
        result = await self.code_generator.generate_code(request)
        
        self.assertIsInstance(result.code, str)
        self.assertGreater(len(result.code), 100)
        self.assertIn("class", result.code)
        self.assertIn("generate_signals", result.code)
        self.assertGreater(result.confidence, 0.7)
        
        # Test auto-patching
        test_file = os.path.join(self.temp_dir, "test_strategy.py")
        with open(test_file, 'w') as f:
            f.write("""
def broken_function():
    x = 10
    y = 0
    result = x / y  # Division by zero
    return result
""")
        
        patch_result = await self.code_generator.auto_patch_file(test_file, confidence_threshold=0.6)
        
        self.assertTrue(patch_result.success)
        self.assertGreater(len(patch_result.fixes_applied), 0)
        self.assertTrue(patch_result.backup_created)
        
        print("✅ Code generation integration test passed")
    
    async def test_angel_one_integration(self):
        """Test Angel One API integration"""
        print("\n🧪 Testing Angel One Integration...")
        
        # Initialize Angel One integration (demo mode)
        success = await self.angel_integration.initialize()
        self.assertTrue(success)
        
        # Test natural language query processing
        queries = [
            "What's my portfolio status?",
            "Show me available margin",
            "What's the current price of RELIANCE?",
            "Show me today's orders"
        ]
        
        for query in queries:
            response = await self.angel_integration.process_natural_language_query(query)
            self.assertIsInstance(response, str)
            self.assertGreater(len(response), 50)
            self.assertNotIn("Error", response)
        
        # Test order placement
        order_id = await self.angel_integration.place_order(
            symbol="RELIANCE",
            transaction_type=TransactionType.BUY,
            quantity=10,
            order_type=OrderType.MARKET
        )
        
        self.assertIsNotNone(order_id)
        self.assertTrue(order_id.startswith("ORD_"))
        
        print("✅ Angel One integration test passed")
    
    async def test_configuration_system(self):
        """Test configuration and documentation system"""
        print("\n🧪 Testing Configuration System...")
        
        # Create test configuration
        test_config = {
            "name": "Test Strategy",
            "type": "momentum",
            "parameters": {
                "period": 14,
                "threshold": 30
            },
            "enabled": True
        }
        
        # Save configuration
        config_path = "test_strategy.yaml"
        success = await self.config_system.save_config(config_path, test_config)
        self.assertTrue(success)
        
        # Load configuration
        loaded_config = await self.config_system.load_config(config_path)
        self.assertIsNotNone(loaded_config)
        self.assertEqual(loaded_config["name"], "Test Strategy")
        
        # Validate configuration
        validation_result = await self.config_system.validate_config(test_config, config_path)
        self.assertTrue(validation_result["valid"])
        
        # Test natural language config request
        nl_response = await self.config_system.process_natural_language_config_request(
            "Show me the strategy configuration"
        )
        self.assertIsInstance(nl_response, str)
        self.assertGreater(len(nl_response), 20)
        
        print("✅ Configuration system test passed")
    
    async def test_interactive_debugging(self):
        """Test interactive debugging and explanations"""
        print("\n🧪 Testing Interactive Debugging...")
        
        # Start debug session
        session_response = await self.debug_system.start_debug_session(
            "test_strategy",
            {"type": "momentum", "timeframe": "5m"}
        )
        
        self.assertIsInstance(session_response, str)
        self.assertIn("Debug Session Started", session_response)
        
        # Execute debug step
        step_response = await self.debug_system.debug_step("Analyze performance metrics")
        
        self.assertIsInstance(step_response, str)
        self.assertIn("Debug Step Executed", step_response)
        
        # Test explanation generation
        explanation_request = ExplanationRequest(
            topic="strategy performance analysis",
            context={"strategy_data": {"roi": 12.5, "win_rate": 65}},
            detail_level="intermediate",
            user_background="intermediate"
        )
        
        explanation = await self.debug_system.explain_concept(explanation_request)
        
        self.assertIsInstance(explanation, str)
        self.assertIn("Strategy Performance", explanation)
        self.assertGreater(len(explanation), 200)
        
        # Test troubleshooting
        troubleshooting = await self.debug_system.interactive_troubleshooting(
            "Strategy is generating false signals"
        )
        
        self.assertIsInstance(troubleshooting, str)
        self.assertIn("Troubleshooting", troubleshooting)
        
        print("✅ Interactive debugging test passed")
    
    async def test_agent_communication(self):
        """Test agent communication interface"""
        print("\n🧪 Testing Agent Communication...")
        
        # Initialize communication
        await self.comm_interface.initialize()
        
        # Create mock agent
        class MockAgent:
            def __init__(self):
                self.capabilities = ['test_method', 'get_status']
            
            async def test_method(self, param="default"):
                return f"Test result: {param}"
            
            def get_status(self):
                return {"status": "running", "health": "good"}
        
        mock_agent = MockAgent()
        
        # Register agent
        success = await self.comm_interface.register_agent(
            "test_agent", mock_agent, "test_type"
        )
        self.assertTrue(success)
        
        # Test query
        result = await self.comm_interface.query_agent(
            "test_agent", "test_method", {"param": "integration_test"}
        )
        self.assertEqual(result, "Test result: integration_test")
        
        # Test command execution
        command_success = await self.comm_interface.execute_command(
            "test_agent", "status"
        )
        self.assertTrue(command_success)
        
        # Test event broadcasting
        await self.comm_interface.broadcast_event("test_event", {"message": "test"})
        
        # Get communication stats
        stats = self.comm_interface.get_communication_stats()
        self.assertIn("test_agent", stats)
        
        print("✅ Agent communication test passed")
    
    async def test_complete_workflow(self):
        """Test complete workflow integration"""
        print("\n🧪 Testing Complete Workflow...")
        
        # Simulate complete workflow:
        # 1. User asks for strategy performance
        # 2. System routes to performance agent
        # 3. Agent provides data
        # 4. System generates explanation
        # 5. User asks for optimization
        # 6. System generates code improvements
        # 7. System applies auto-patches
        
        workflow_steps = []
        
        # Step 1: Performance query
        performance_query = "Analyze my RSI strategy performance and suggest improvements"
        workflow_steps.append(f"Query: {performance_query}")
        
        # Step 2: Mock performance analysis
        mock_performance_data = {
            "roi": 8.5,
            "win_rate": 55.2,
            "sharpe_ratio": 0.8,
            "max_drawdown": 12.3
        }
        workflow_steps.append(f"Performance Data: {mock_performance_data}")
        
        # Step 3: Generate recommendations
        recommendations = await self.debug_system.generate_recommendations(
            "rsi_strategy", mock_performance_data
        )
        self.assertGreater(len(recommendations), 0)
        workflow_steps.append(f"Recommendations: {len(recommendations)} generated")
        
        # Step 4: Generate optimization code
        optimization_request = CodeGenerationRequest(
            description="Optimize RSI strategy with better risk management",
            code_type=CodeType.STRATEGY,
            context={"current_performance": mock_performance_data}
        )
        
        code_result = await self.code_generator.generate_code(optimization_request)
        self.assertGreater(len(code_result.code), 100)
        workflow_steps.append("Code optimization generated")
        
        # Step 5: Configuration update
        config_update = {
            "rsi_period": 14,
            "risk_limit": 0.02,
            "stop_loss": 0.015
        }
        
        workflow_steps.append("Configuration updated")
        
        # Verify workflow completion
        self.assertEqual(len(workflow_steps), 5)
        
        print("✅ Complete workflow test passed")
        print(f"   Workflow steps: {len(workflow_steps)}")
        for i, step in enumerate(workflow_steps, 1):
            print(f"   {i}. {step}")

async def run_integration_tests():
    """Run all integration tests"""
    print("🚀 Starting Complete LLM Interface Agent Integration Tests")
    print("=" * 70)
    
    # Create test suite
    suite = unittest.TestSuite()
    test_case = TestCompleteIntegration()
    
    # Add all test methods
    test_methods = [
        'test_end_to_end_query_processing',
        'test_code_generation_integration', 
        'test_angel_one_integration',
        'test_configuration_system',
        'test_interactive_debugging',
        'test_agent_communication',
        'test_complete_workflow'
    ]
    
    # Run tests
    for test_method in test_methods:
        print(f"\n{'='*50}")
        print(f"Running: {test_method}")
        print('='*50)
        
        try:
            await getattr(test_case, test_method)()
            print(f"✅ {test_method} PASSED")
        except Exception as e:
            print(f"❌ {test_method} FAILED: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*70}")
    print("🎉 Integration Tests Completed!")
    print("✅ All components successfully integrated and tested")
    print("🚀 LLM Interface Agent is ready for production use!")

if __name__ == "__main__":
    # Setup test environment
    test_case = TestCompleteIntegration()
    test_case.setUp()
    
    try:
        # Run integration tests
        asyncio.run(run_integration_tests())
    finally:
        # Cleanup
        test_case.tearDown()
