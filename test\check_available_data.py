#!/usr/bin/env python3
"""
Check what market data is available for signal generation
"""

import asyncio
import logging
from datetime import datetime
from agents.market_monitoring_agent import MarketMonitoringAgent

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def check_available_data():
    """Check what market data is available"""
    try:
        print("🔍 Checking Available Market Data...")
        print(f"⏰ Current time: {datetime.now()}")
        
        # Initialize market monitoring agent
        agent = MarketMonitoringAgent()
        
        # Load historical data
        print("🔄 Loading timeframes...")
        await agent._generate_and_load_timeframes()
        print("✅ Timeframes loading completed")
        
        print(f"📊 Total symbols with data: {len(agent.market_data)}")
        
        # Check data for specific symbols
        test_symbols = ['INFY', 'RELIANCE', 'HDFCBANK', 'ICICIBANK', 'LT']
        
        for symbol in test_symbols:
            if symbol in agent.market_data:
                print(f"\n📈 {symbol}:")
                for timeframe in ['1min', '5min', '15min', '30min', '1hr']:
                    if timeframe in agent.market_data[symbol]:
                        count = len(agent.market_data[symbol][timeframe])
                        if count > 0:
                            latest = agent.market_data[symbol][timeframe][-1]
                            print(f"   {timeframe}: {count} candles, latest: {latest.timestamp} @ {latest.close}")
                        else:
                            print(f"   {timeframe}: No data")
                    else:
                        print(f"   {timeframe}: Not available")
                        
                # Check if indicators can be calculated
                if symbol in agent.market_data and '5min' in agent.market_data[symbol]:
                    candles_5min = list(agent.market_data[symbol]['5min'])
                    if len(candles_5min) >= 20:
                        print(f"   ✅ Sufficient data for indicators ({len(candles_5min)} candles)")
                        
                        # Try to calculate indicators
                        await agent._calculate_indicators(symbol)
                        
                        if symbol in agent.indicators:
                            indicators = agent.indicators[symbol]
                            print(f"   📊 Indicators calculated:")
                            print(f"      RSI: {getattr(indicators, 'rsi_14', 'N/A')}")
                            print(f"      EMA20: {getattr(indicators, 'ema_20', 'N/A')}")
                            print(f"      MACD: {getattr(indicators, 'macd', 'N/A')}")
                        else:
                            print(f"   ❌ Failed to calculate indicators")
                    else:
                        print(f"   ⚠️ Insufficient data for indicators ({len(candles_5min)} candles, need 20+)")
            else:
                print(f"\n❌ {symbol}: No data available")
        
        print(f"\n🎯 Market Regime: {agent.get_market_regime()}")
        
    except Exception as e:
        logger.error(f"❌ Check failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_available_data())
