"""
Tests for Feature 8: Backtest Visualization & Debugging
"""
import pytest
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
import tempfile
import os

from agents.enhanced_backtesting_polars import BacktestVisualizer


class TestBacktestVisualizer:
    """Test suite for BacktestVisualizer class"""
    
    @pytest.fixture
    def visualizer(self, temp_directory):
        """Create BacktestVisualizer instance"""
        return BacktestVisualizer(output_directory=temp_directory)
    
    @pytest.mark.asyncio
    async def test_initialization(self, visualizer, temp_directory):
        """Test proper initialization of BacktestVisualizer"""
        assert visualizer.output_directory == temp_directory
        assert visualizer.chart_config == {}
        assert visualizer.interactive_mode == True
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.plt')
    async def test_create_equity_curve(self, mock_plt, visualizer, sample_trades):
        """Test equity curve creation"""
        mock_fig = MagicMock()
        mock_plt.figure.return_value = mock_fig
        
        chart_path = await visualizer.create_equity_curve(
            sample_trades, initial_capital=100000, title="Test Equity Curve"
        )
        
        assert chart_path is not None
        mock_plt.figure.assert_called_once()
        mock_plt.plot.assert_called()
        mock_plt.title.assert_called_with("Test Equity Curve")
        mock_plt.xlabel.assert_called()
        mock_plt.ylabel.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.plt')
    async def test_create_drawdown_chart(self, mock_plt, visualizer, sample_trades):
        """Test drawdown chart creation"""
        mock_fig = MagicMock()
        mock_plt.figure.return_value = mock_fig
        
        chart_path = await visualizer.create_drawdown_chart(
            sample_trades, title="Drawdown Analysis"
        )
        
        assert chart_path is not None
        mock_plt.figure.assert_called_once()
        mock_plt.fill_between.assert_called()
        mock_plt.title.assert_called_with("Drawdown Analysis")
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.plt')
    async def test_create_trade_distribution(self, mock_plt, visualizer, sample_trades):
        """Test trade distribution chart creation"""
        mock_fig = MagicMock()
        mock_plt.figure.return_value = mock_fig
        
        chart_path = await visualizer.create_trade_distribution(
            sample_trades, title="Trade P&L Distribution"
        )
        
        assert chart_path is not None
        mock_plt.figure.assert_called_once()
        mock_plt.hist.assert_called()
        mock_plt.title.assert_called_with("Trade P&L Distribution")
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.plt')
    async def test_create_monthly_returns_heatmap(self, mock_plt, visualizer, sample_trades):
        """Test monthly returns heatmap creation"""
        mock_fig = MagicMock()
        mock_plt.figure.return_value = mock_fig
        
        with patch('agents.enhanced_backtesting_polars.sns') as mock_sns:
            chart_path = await visualizer.create_monthly_returns_heatmap(
                sample_trades, title="Monthly Returns Heatmap"
            )
            
            assert chart_path is not None
            mock_plt.figure.assert_called_once()
            mock_sns.heatmap.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.go')
    async def test_create_interactive_dashboard(self, mock_go, visualizer, 
                                              sample_trades, sample_market_data):
        """Test interactive dashboard creation"""
        mock_fig = MagicMock()
        mock_go.Figure.return_value = mock_fig
        
        dashboard_path = await visualizer.create_interactive_dashboard(
            sample_trades, sample_market_data, title="Interactive Dashboard"
        )
        
        assert dashboard_path is not None
        mock_go.Figure.assert_called()
        mock_fig.add_trace.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.plt')
    async def test_create_performance_comparison(self, mock_plt, visualizer, sample_backtest_results):
        """Test performance comparison chart"""
        mock_fig = MagicMock()
        mock_plt.figure.return_value = mock_fig
        
        # Create multiple strategy results for comparison
        strategy_results = {
            'Strategy A': sample_backtest_results,
            'Strategy B': {**sample_backtest_results, 'total_return': 18.5},
            'Strategy C': {**sample_backtest_results, 'total_return': 12.3}
        }
        
        chart_path = await visualizer.create_performance_comparison(
            strategy_results, metrics=['total_return', 'sharpe_ratio', 'max_drawdown']
        )
        
        assert chart_path is not None
        mock_plt.figure.assert_called_once()
        mock_plt.bar.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.plt')
    async def test_create_trade_timeline(self, mock_plt, visualizer, sample_trades, sample_market_data):
        """Test trade timeline visualization"""
        mock_fig = MagicMock()
        mock_plt.figure.return_value = mock_fig
        
        chart_path = await visualizer.create_trade_timeline(
            sample_trades, sample_market_data, title="Trade Timeline"
        )
        
        assert chart_path is not None
        mock_plt.figure.assert_called_once()
        mock_plt.plot.assert_called()  # Price line
        mock_plt.scatter.assert_called()  # Trade markers
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.plt')
    async def test_create_risk_metrics_radar(self, mock_plt, visualizer, sample_metrics):
        """Test risk metrics radar chart"""
        mock_fig = MagicMock()
        mock_plt.figure.return_value = mock_fig
        
        with patch('agents.enhanced_backtesting_polars.plt.subplot', return_value=MagicMock()) as mock_subplot:
            chart_path = await visualizer.create_risk_metrics_radar(
                sample_metrics, title="Risk Metrics Radar"
            )
            
            assert chart_path is not None
            mock_plt.figure.assert_called_once()
            mock_subplot.assert_called()
    
    @pytest.mark.asyncio
    async def test_create_correlation_matrix(self, visualizer):
        """Test correlation matrix visualization"""
        # Sample correlation data
        correlation_data = {
            'Strategy A': [1.0, 0.3, 0.1],
            'Strategy B': [0.3, 1.0, 0.2],
            'Strategy C': [0.1, 0.2, 1.0]
        }
        
        with patch('agents.enhanced_backtesting_polars.plt') as mock_plt, \
             patch('agents.enhanced_backtesting_polars.sns') as mock_sns:
            
            mock_fig = MagicMock()
            mock_plt.figure.return_value = mock_fig
            
            chart_path = await visualizer.create_correlation_matrix(
                correlation_data, title="Strategy Correlation Matrix"
            )
            
            assert chart_path is not None
            mock_plt.figure.assert_called_once()
            mock_sns.heatmap.assert_called()
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.plt')
    async def test_create_rolling_metrics(self, mock_plt, visualizer, sample_trades):
        """Test rolling metrics visualization"""
        mock_fig = MagicMock()
        mock_plt.figure.return_value = mock_fig
        
        chart_path = await visualizer.create_rolling_metrics(
            sample_trades, window_size=30, metrics=['sharpe_ratio', 'volatility']
        )
        
        assert chart_path is not None
        mock_plt.figure.assert_called_once()
        mock_plt.plot.assert_called()
    
    @pytest.mark.asyncio
    async def test_debug_trade_execution(self, visualizer, sample_trades, sample_market_data):
        """Test trade execution debugging"""
        debug_info = await visualizer.debug_trade_execution(
            sample_trades, sample_market_data, trade_index=0
        )
        
        assert 'trade_details' in debug_info
        assert 'market_context' in debug_info
        assert 'execution_analysis' in debug_info
        assert 'recommendations' in debug_info
    
    @pytest.mark.asyncio
    async def test_analyze_trade_clustering(self, visualizer, sample_trades):
        """Test trade clustering analysis"""
        clustering_result = await visualizer.analyze_trade_clustering(
            sample_trades, features=['pnl_pct', 'duration', 'entry_price']
        )
        
        assert 'clusters' in clustering_result
        assert 'cluster_characteristics' in clustering_result
        assert 'outlier_trades' in clustering_result
    
    @pytest.mark.asyncio
    async def test_create_sensitivity_analysis_chart(self, visualizer):
        """Test sensitivity analysis visualization"""
        # Sample sensitivity data
        sensitivity_data = {
            'parameter': ['rsi_period', 'sma_period', 'threshold'],
            'sensitivity_score': [0.8, 0.6, 0.9],
            'impact_range': [5.2, 3.1, 7.8]
        }
        
        with patch('agents.enhanced_backtesting_polars.plt') as mock_plt:
            mock_fig = MagicMock()
            mock_plt.figure.return_value = mock_fig
            
            chart_path = await visualizer.create_sensitivity_analysis_chart(
                sensitivity_data, title="Parameter Sensitivity Analysis"
            )
            
            assert chart_path is not None
            mock_plt.figure.assert_called_once()
            mock_plt.bar.assert_called()
    
    @pytest.mark.asyncio
    async def test_export_charts_to_pdf(self, visualizer):
        """Test PDF export functionality"""
        chart_paths = [
            os.path.join(visualizer.output_directory, 'chart1.png'),
            os.path.join(visualizer.output_directory, 'chart2.png')
        ]
        
        # Create dummy chart files
        for path in chart_paths:
            with open(path, 'w') as f:
                f.write('dummy chart data')
        
        with patch('agents.enhanced_backtesting_polars.PdfPages') as mock_pdf:
            pdf_path = await visualizer.export_charts_to_pdf(
                chart_paths, output_filename="backtest_report.pdf"
            )
            
            assert pdf_path is not None
            assert pdf_path.endswith('.pdf')
    
    @pytest.mark.asyncio
    async def test_create_animated_equity_curve(self, visualizer, sample_trades):
        """Test animated equity curve creation"""
        with patch('agents.enhanced_backtesting_polars.FuncAnimation') as mock_animation:
            animation_path = await visualizer.create_animated_equity_curve(
                sample_trades, initial_capital=100000
            )
            
            assert animation_path is not None
            mock_animation.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_chart_configuration(self, visualizer):
        """Test chart configuration management"""
        config = {
            'figure_size': (12, 8),
            'dpi': 300,
            'style': 'seaborn',
            'color_palette': 'viridis'
        }
        
        visualizer.set_chart_config(config)
        
        assert visualizer.chart_config == config
        
        # Test applying configuration
        with patch('agents.enhanced_backtesting_polars.plt') as mock_plt:
            await visualizer._apply_chart_config()
            mock_plt.style.use.assert_called_with('seaborn')
    
    @pytest.mark.asyncio
    async def test_error_handling_invalid_data(self, visualizer):
        """Test error handling for invalid data"""
        invalid_trades = []  # Empty trades list
        
        with pytest.raises(ValueError, match="No trades data provided"):
            await visualizer.create_equity_curve(invalid_trades, initial_capital=100000)
    
    @pytest.mark.asyncio
    async def test_memory_efficient_plotting(self, visualizer):
        """Test memory-efficient plotting for large datasets"""
        # Create large dataset
        large_trades = []
        for i in range(10000):
            large_trades.append({
                'entry_time': datetime(2023, 1, 1) + timedelta(hours=i),
                'exit_time': datetime(2023, 1, 1) + timedelta(hours=i+1),
                'pnl': np.random.normal(100, 50),
                'pnl_pct': np.random.normal(1, 2)
            })
        
        with patch('agents.enhanced_backtesting_polars.plt') as mock_plt:
            mock_fig = MagicMock()
            mock_plt.figure.return_value = mock_fig
            
            # Should handle large dataset without memory issues
            chart_path = await visualizer.create_equity_curve(
                large_trades, initial_capital=100000, downsample=True
            )
            
            assert chart_path is not None
    
    @pytest.mark.asyncio
    async def test_custom_theme_application(self, visualizer):
        """Test custom theme application"""
        custom_theme = {
            'background_color': '#1e1e1e',
            'text_color': '#ffffff',
            'grid_color': '#404040',
            'accent_color': '#00ff00'
        }
        
        await visualizer.apply_custom_theme(custom_theme)
        
        assert visualizer.current_theme == custom_theme
