#!/usr/bin/env python3
"""
Enhanced Backtesting Agent Integration

This module demonstrates how the Enhanced Backtesting Agent integrates with
the existing agent ecosystem, including:
- Strategy Evolution Agent
- AI Training Agent
- Performance Analysis Agent
- LLM Interface Agent
"""

import os
import sys
import asyncio
import logging
import json
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.enhanced_backtesting_agent import EnhancedBacktestingAgent, BacktestResults

class EnhancedBacktestingIntegration:
    """Integration layer for Enhanced Backtesting Agent"""
    
    def __init__(self, config_path: str = "config/enhanced_backtesting_config.yaml"):
        self.config_path = config_path
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Agent endpoints
        self.agent_endpoints = {
            'strategy_evolution': 'http://localhost:8001/strategy_evolution',
            'ai_training': 'http://localhost:8002/ai_training',
            'performance_analysis': 'http://localhost:8003/performance_analysis',
            'llm_interface': 'http://localhost:8004/llm_interface',
        }
        
        # Integration status
        self.integration_status = {}
    
    async def run_integrated_backtesting(self, **kwargs) -> bool:
        """Run backtesting with full ecosystem integration"""
        try:
            self.logger.info("[INIT] Starting Integrated Backtesting Process...")
            
            # Initialize Enhanced Backtesting Agent
            agent = EnhancedBacktestingAgent(self.config_path)
            
            if not await agent.initialize():
                self.logger.error("[ERROR] Failed to initialize Enhanced Backtesting Agent")
                return False
            
            # Check agent connectivity
            await self._check_agent_connectivity()
            
            # Run backtesting
            self.logger.info("[TARGET] Running enhanced backtesting...")
            success = await agent.start_backtesting(**kwargs)
            
            if not success:
                self.logger.error("[ERROR] Backtesting failed")
                return False
            
            # Get results
            results = agent.current_results
            
            if results:
                # Send results to integrated agents
                await self._send_results_to_agents(results)
                
                # Generate comprehensive analysis
                await self._generate_integrated_analysis(results)
                
                # Update strategy evolution
                await self._update_strategy_evolution(results)
                
                # Train AI models
                await self._update_ai_training(results)
            
            # Stop agent
            await agent.stop()
            
            self.logger.info("[SUCCESS] Integrated backtesting completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"[ERROR] Integrated backtesting failed: {e}")
            return False
    
    async def _check_agent_connectivity(self):
        """Check connectivity to other agents"""
        self.logger.info("🔗 Checking agent connectivity...")
        
        for agent_name, endpoint in self.agent_endpoints.items():
            try:
                # Simple health check
                response = requests.get(f"{endpoint}/health", timeout=5)
                if response.status_code == 200:
                    self.integration_status[agent_name] = "connected"
                    self.logger.info(f"   • {agent_name}: [SUCCESS] Connected")
                else:
                    self.integration_status[agent_name] = "error"
                    self.logger.warning(f"   • {agent_name}: [WARN] Error (status: {response.status_code})")
            
            except requests.exceptions.RequestException:
                self.integration_status[agent_name] = "offline"
                self.logger.warning(f"   • {agent_name}: [ERROR] Offline")
    
    async def _send_results_to_agents(self, results: List[BacktestResults]):
        """Send backtesting results to integrated agents"""
        self.logger.info("📤 Sending results to integrated agents...")
        
        # Prepare results data
        results_data = self._prepare_results_for_integration(results)
        
        # Send to Strategy Evolution Agent
        if self.integration_status.get('strategy_evolution') == 'connected':
            await self._send_to_strategy_evolution(results_data)
        
        # Send to AI Training Agent
        if self.integration_status.get('ai_training') == 'connected':
            await self._send_to_ai_training(results_data)
        
        # Send to Performance Analysis Agent
        if self.integration_status.get('performance_analysis') == 'connected':
            await self._send_to_performance_analysis(results_data)
    
    def _prepare_results_for_integration(self, results: List[BacktestResults]) -> Dict[str, Any]:
        """Prepare results data for integration with other agents"""
        try:
            # Aggregate results
            total_strategies = len(results)
            profitable_strategies = len([r for r in results if r.performance_metrics.roi > 0])
            
            # Best and worst performers
            best_strategy = max(results, key=lambda r: r.performance_metrics.roi) if results else None
            worst_strategy = min(results, key=lambda r: r.performance_metrics.roi) if results else None
            
            # Performance statistics
            roi_values = [r.performance_metrics.roi for r in results]
            sharpe_values = [r.performance_metrics.sharpe_ratio for r in results]
            drawdown_values = [r.performance_metrics.max_drawdown for r in results]
            
            # Prepare integration data
            integration_data = {
                'timestamp': datetime.now().isoformat(),
                'backtesting_summary': {
                    'total_strategies': total_strategies,
                    'profitable_strategies': profitable_strategies,
                    'profitability_rate': (profitable_strategies / total_strategies) * 100 if total_strategies > 0 else 0,
                    'avg_roi': sum(roi_values) / len(roi_values) if roi_values else 0,
                    'avg_sharpe_ratio': sum(sharpe_values) / len(sharpe_values) if sharpe_values else 0,
                    'avg_max_drawdown': sum(drawdown_values) / len(drawdown_values) if drawdown_values else 0,
                },
                'best_strategy': {
                    'name': best_strategy.performance_metrics.strategy_name if best_strategy else None,
                    'symbol': best_strategy.performance_metrics.symbol if best_strategy else None,
                    'roi': best_strategy.performance_metrics.roi if best_strategy else 0,
                    'sharpe_ratio': best_strategy.performance_metrics.sharpe_ratio if best_strategy else 0,
                    'win_rate': best_strategy.performance_metrics.win_rate if best_strategy else 0,
                } if best_strategy else None,
                'worst_strategy': {
                    'name': worst_strategy.performance_metrics.strategy_name if worst_strategy else None,
                    'symbol': worst_strategy.performance_metrics.symbol if worst_strategy else None,
                    'roi': worst_strategy.performance_metrics.roi if worst_strategy else 0,
                    'sharpe_ratio': worst_strategy.performance_metrics.sharpe_ratio if worst_strategy else 0,
                    'win_rate': worst_strategy.performance_metrics.win_rate if worst_strategy else 0,
                } if worst_strategy else None,
                'detailed_results': [
                    {
                        'strategy_name': r.performance_metrics.strategy_name,
                        'symbol': r.performance_metrics.symbol,
                        'timeframe': r.performance_metrics.timeframe,
                        'roi': r.performance_metrics.roi,
                        'sharpe_ratio': r.performance_metrics.sharpe_ratio,
                        'max_drawdown': r.performance_metrics.max_drawdown,
                        'win_rate': r.performance_metrics.win_rate,
                        'total_trades': r.performance_metrics.total_trades,
                        'profit_factor': r.performance_metrics.profit_factor,
                    }
                    for r in results
                ]
            }
            
            return integration_data
            
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to prepare results for integration: {e}")
            return {}
    
    async def _send_to_strategy_evolution(self, results_data: Dict[str, Any]):
        """Send results to Strategy Evolution Agent"""
        try:
            self.logger.info("[METRICS] Sending results to Strategy Evolution Agent...")
            
            # Prepare data for strategy evolution
            evolution_data = {
                'source': 'enhanced_backtesting',
                'timestamp': results_data['timestamp'],
                'performance_data': results_data['detailed_results'],
                'summary': results_data['backtesting_summary'],
                'recommendations': {
                    'promote_strategies': [
                        r for r in results_data['detailed_results']
                        if r['roi'] > 5 and r['sharpe_ratio'] > 1.0 and r['max_drawdown'] < 10
                    ],
                    'demote_strategies': [
                        r for r in results_data['detailed_results']
                        if r['roi'] < -5 or r['sharpe_ratio'] < 0 or r['max_drawdown'] > 20
                    ]
                }
            }
            
            # Send to Strategy Evolution Agent
            response = requests.post(
                f"{self.agent_endpoints['strategy_evolution']}/update_performance",
                json=evolution_data,
                timeout=30
            )
            
            if response.status_code == 200:
                self.logger.info("[SUCCESS] Results sent to Strategy Evolution Agent")
            else:
                self.logger.warning(f"[WARN] Strategy Evolution Agent responded with status: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to send results to Strategy Evolution Agent: {e}")
    
    async def _send_to_ai_training(self, results_data: Dict[str, Any]):
        """Send results to AI Training Agent"""
        try:
            self.logger.info("[AGENT] Sending results to AI Training Agent...")
            
            # Prepare training data
            training_data = {
                'source': 'enhanced_backtesting',
                'timestamp': results_data['timestamp'],
                'training_samples': [
                    {
                        'features': {
                            'strategy_name': r['strategy_name'],
                            'symbol': r['symbol'],
                            'timeframe': r['timeframe'],
                        },
                        'targets': {
                            'roi': r['roi'],
                            'sharpe_ratio': r['sharpe_ratio'],
                            'max_drawdown': r['max_drawdown'],
                            'win_rate': r['win_rate'],
                            'is_profitable': r['roi'] > 0,
                        }
                    }
                    for r in results_data['detailed_results']
                ],
                'metadata': {
                    'total_samples': len(results_data['detailed_results']),
                    'profitable_samples': len([r for r in results_data['detailed_results'] if r['roi'] > 0]),
                }
            }
            
            # Send to AI Training Agent
            response = requests.post(
                f"{self.agent_endpoints['ai_training']}/add_training_data",
                json=training_data,
                timeout=30
            )
            
            if response.status_code == 200:
                self.logger.info("[SUCCESS] Training data sent to AI Training Agent")
            else:
                self.logger.warning(f"[WARN] AI Training Agent responded with status: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to send training data to AI Training Agent: {e}")
    
    async def _send_to_performance_analysis(self, results_data: Dict[str, Any]):
        """Send results to Performance Analysis Agent"""
        try:
            self.logger.info("[STATUS] Sending results to Performance Analysis Agent...")
            
            # Prepare analysis data
            analysis_data = {
                'source': 'enhanced_backtesting',
                'timestamp': results_data['timestamp'],
                'performance_metrics': results_data['detailed_results'],
                'summary_statistics': results_data['backtesting_summary'],
                'analysis_request': {
                    'generate_report': True,
                    'compare_strategies': True,
                    'risk_analysis': True,
                    'correlation_analysis': True,
                }
            }
            
            # Send to Performance Analysis Agent
            response = requests.post(
                f"{self.agent_endpoints['performance_analysis']}/analyze_performance",
                json=analysis_data,
                timeout=60
            )
            
            if response.status_code == 200:
                self.logger.info("[SUCCESS] Analysis request sent to Performance Analysis Agent")
            else:
                self.logger.warning(f"[WARN] Performance Analysis Agent responded with status: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to send analysis request to Performance Analysis Agent: {e}")
    
    async def _generate_integrated_analysis(self, results: List[BacktestResults]):
        """Generate comprehensive integrated analysis"""
        try:
            self.logger.info("[DEBUG] Generating integrated analysis...")
            
            # Generate cross-agent insights
            insights = {
                'strategy_performance_ranking': self._rank_strategies(results),
                'risk_adjusted_recommendations': self._generate_risk_recommendations(results),
                'market_regime_analysis': self._analyze_market_regimes(results),
                'optimization_opportunities': self._identify_optimization_opportunities(results),
            }
            
            # Save integrated analysis
            analysis_path = "data/backtest/integrated_analysis.json"
            os.makedirs(os.path.dirname(analysis_path), exist_ok=True)
            
            with open(analysis_path, 'w') as f:
                json.dump(insights, f, indent=2, default=str)
            
            self.logger.info(f"📄 Integrated analysis saved to {analysis_path}")
            
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to generate integrated analysis: {e}")
    
    def _rank_strategies(self, results: List[BacktestResults]) -> List[Dict[str, Any]]:
        """Rank strategies by composite score"""
        try:
            strategy_scores = []
            
            for result in results:
                metrics = result.performance_metrics
                
                # Calculate composite score
                score = (
                    metrics.roi * 0.3 +
                    metrics.sharpe_ratio * 20 * 0.3 +
                    (100 - metrics.max_drawdown) * 0.2 +
                    metrics.win_rate * 0.2
                )
                
                strategy_scores.append({
                    'strategy_name': metrics.strategy_name,
                    'symbol': metrics.symbol,
                    'timeframe': metrics.timeframe,
                    'composite_score': score,
                    'roi': metrics.roi,
                    'sharpe_ratio': metrics.sharpe_ratio,
                    'max_drawdown': metrics.max_drawdown,
                    'win_rate': metrics.win_rate,
                })
            
            # Sort by composite score
            strategy_scores.sort(key=lambda x: x['composite_score'], reverse=True)
            
            return strategy_scores
            
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to rank strategies: {e}")
            return []
    
    def _generate_risk_recommendations(self, results: List[BacktestResults]) -> Dict[str, Any]:
        """Generate risk-based recommendations"""
        try:
            high_risk_strategies = []
            low_risk_strategies = []
            
            for result in results:
                metrics = result.performance_metrics
                
                if metrics.max_drawdown > 15 or metrics.sharpe_ratio < 0.5:
                    high_risk_strategies.append({
                        'strategy_name': metrics.strategy_name,
                        'symbol': metrics.symbol,
                        'risk_factors': {
                            'high_drawdown': metrics.max_drawdown > 15,
                            'low_sharpe': metrics.sharpe_ratio < 0.5,
                            'low_win_rate': metrics.win_rate < 40,
                        }
                    })
                elif metrics.max_drawdown < 8 and metrics.sharpe_ratio > 1.0:
                    low_risk_strategies.append({
                        'strategy_name': metrics.strategy_name,
                        'symbol': metrics.symbol,
                        'risk_profile': 'conservative',
                        'max_drawdown': metrics.max_drawdown,
                        'sharpe_ratio': metrics.sharpe_ratio,
                    })
            
            return {
                'high_risk_strategies': high_risk_strategies,
                'low_risk_strategies': low_risk_strategies,
                'recommendations': {
                    'reduce_position_size': [s['strategy_name'] for s in high_risk_strategies],
                    'increase_allocation': [s['strategy_name'] for s in low_risk_strategies[:5]],
                }
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to generate risk recommendations: {e}")
            return {}
    
    def _analyze_market_regimes(self, results: List[BacktestResults]) -> Dict[str, Any]:
        """Analyze performance across market regimes"""
        # Placeholder for market regime analysis
        return {
            'regime_performance': {},
            'regime_recommendations': {},
        }
    
    def _identify_optimization_opportunities(self, results: List[BacktestResults]) -> Dict[str, Any]:
        """Identify optimization opportunities"""
        # Placeholder for optimization opportunity identification
        return {
            'parameter_optimization': {},
            'strategy_combinations': {},
        }
    
    async def _update_strategy_evolution(self, results: List[BacktestResults]):
        """Update strategy evolution based on results"""
        # This would trigger strategy evolution processes
        pass
    
    async def _update_ai_training(self, results: List[BacktestResults]):
        """Update AI training based on results"""
        # This would trigger AI model retraining
        pass

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main execution function for testing integration"""
    try:
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        
        # Create integration instance
        integration = EnhancedBacktestingIntegration()
        
        # Run integrated backtesting
        success = await integration.run_integrated_backtesting()
        
        if success:
            print("[SUCCESS] Integrated backtesting completed successfully!")
        else:
            print("[ERROR] Integrated backtesting failed!")
        
    except Exception as e:
        print(f"[ERROR] Integration execution failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
