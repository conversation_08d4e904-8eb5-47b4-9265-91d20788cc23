#!/usr/bin/env python3
"""
Timeframe Converter: Convert 5-minute historical data to 15min, 30min, and 1hr timeframes
Uses polars, pyarrow, and asyncio with chunking for memory efficiency
"""

import polars as pl
import pyarrow as pa
import asyncio
import os
import gc
from pathlib import Path
from typing import List, Dict, Any
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TimeframeConverter:
    """
    Converts 5-minute historical data to multiple timeframes using chunked processing
    """
    
    def __init__(self, input_file: str, output_dir: str, chunk_size: int = 500000):
        """
        Initialize the timeframe converter
        
        Args:
            input_file: Path to the 5-minute parquet file
            output_dir: Directory to save output files
            chunk_size: Number of rows to process per chunk
        """
        self.input_file = input_file
        self.output_dir = Path(output_dir)
        self.chunk_size = chunk_size
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Define timeframe configurations
        self.timeframes = {
            "15min": {"minutes": 15, "output_file": "historical_15min.parquet"},
            "30min": {"minutes": 30, "output_file": "historical_30min.parquet"},
            "1hr": {"minutes": 60, "output_file": "historical_1hr.parquet"}
        }

        # Initialize batch storage for each timeframe
        self.batch_data = {tf: [] for tf in self.timeframes.keys()}
        self.batch_counts = {tf: 0 for tf in self.timeframes.keys()}

        # Initialize output files (clear existing files)
        self._initialize_output_files()
    
    def _initialize_output_files(self):
        """Initialize/clear output files"""
        for tf_config in self.timeframes.values():
            output_path = self.output_dir / tf_config["output_file"]
            if output_path.exists():
                output_path.unlink()
                logger.info(f"Cleared existing file: {output_path}")
    
    def _create_datetime_column(self, df: pl.DataFrame) -> pl.DataFrame:
        """Create a datetime column from date and time columns (IST timezone)"""
        return df.with_columns([
            pl.concat_str([
                pl.col("date").cast(pl.Utf8),
                pl.lit(" "),
                pl.col("time").cast(pl.Utf8)
            ]).str.strptime(pl.Datetime, "%d-%m-%Y %H:%M:%S").alias("datetime")
        ])
    
    def _resample_to_timeframe(self, df: pl.DataFrame, minutes: int) -> pl.DataFrame:
        """
        Resample 5-minute data to specified timeframe
        
        Args:
            df: Input dataframe with 5-minute data
            minutes: Target timeframe in minutes (15, 30, or 60)
        
        Returns:
            Resampled dataframe
        """
        # Create datetime column
        df = self._create_datetime_column(df)
        
        # Group by stock and resample (using actual column names)
        resampled = df.group_by([
            pl.col("symbol"),
            pl.col("datetime").dt.truncate(f"{minutes}m")
        ]).agg([
            # OHLC aggregation
            pl.col("open").first().alias("open"),
            pl.col("high").max().alias("high"),
            pl.col("low").min().alias("low"),
            pl.col("close").last().alias("close"),
            pl.col("volume").sum().alias("volume"),
            # Keep original date and time from first record in group
            pl.col("date").first().alias("date"),
            pl.col("time").first().alias("time")
        ]).sort(["symbol", "datetime"])
        
        # Update date and time columns to reflect the resampled timeframe (IST)
        resampled = resampled.with_columns([
            pl.col("datetime").dt.strftime("%d-%m-%Y").alias("date"),
            pl.col("datetime").dt.strftime("%H:%M:%S").alias("time")
        ]).drop("datetime")
        
        return resampled
    
    async def _process_chunk_for_timeframe(self, chunk: pl.DataFrame, timeframe: str, minutes: int) -> pl.DataFrame:
        """Process a single chunk for a specific timeframe"""
        try:
            # Resample the chunk
            resampled_chunk = self._resample_to_timeframe(chunk, minutes)
            logger.info(f"Processed chunk for {timeframe}: {len(resampled_chunk)} rows")
            return resampled_chunk
        except Exception as e:
            logger.error(f"Error processing chunk for {timeframe}: {e}")
            raise
    
    async def _add_to_batch(self, chunk: pl.DataFrame, timeframe: str):
        """Add processed chunk to batch for a timeframe"""
        self.batch_data[timeframe].append(chunk)
        self.batch_counts[timeframe] += 1
        
        # Write batch if it gets too large (every 5 chunks)
        if self.batch_counts[timeframe] >= 5:
            await self._write_batch(timeframe)
    
    async def _write_batch(self, timeframe: str):
        """Write accumulated batch data to file"""
        if not self.batch_data[timeframe]:
            return
            
        try:
            # Combine all chunks in the batch
            combined_chunk = pl.concat(self.batch_data[timeframe], how="vertical")
            
            # Write to file (append mode)
            output_path = self.output_dir / self.timeframes[timeframe]["output_file"]
            
            if output_path.exists():
                # Append to existing file
                existing_df = pl.read_parquet(output_path)
                combined_df = pl.concat([existing_df, combined_chunk], how="vertical")
                combined_df.write_parquet(output_path)
            else:
                # Create new file
                combined_chunk.write_parquet(output_path)

            total_rows = len(combined_chunk)
            logger.info(f"Written batch of {total_rows} rows to {output_path} ({self.batch_counts[timeframe]} chunks)")

            # Clear the batch
            self.batch_data[timeframe] = []
            self.batch_counts[timeframe] = 0

            # Clear memory
            del combined_chunk
            gc.collect()

        except Exception as e:
            logger.error(f"Error writing batch for {timeframe}: {e}")
            raise

    async def _write_final_batches(self):
        """Write any remaining data in batches"""
        for timeframe in self.timeframes.keys():
            if self.batch_data[timeframe]:
                await self._write_batch(timeframe)
    
    async def _process_single_chunk(self, chunk: pl.DataFrame, chunk_num: int):
        """Process a single chunk for all timeframes"""
        logger.info(f"Processing chunk {chunk_num} with {len(chunk)} rows")

        # Process chunk for each timeframe concurrently
        tasks = []
        for tf_name, tf_config in self.timeframes.items():
            task = self._process_chunk_for_timeframe(chunk, tf_name, tf_config["minutes"])
            tasks.append((tf_name, task))

        # Wait for all timeframe processing to complete and add to batches
        for tf_name, task in tasks:
            try:
                processed_chunk = await task
                await self._add_to_batch(processed_chunk, tf_name)
            except Exception as e:
                logger.error(f"Failed to process chunk {chunk_num} for {tf_name}: {e}")
                continue

        logger.info(f"Completed processing chunk {chunk_num}")

        # Clear memory
        del chunk
        gc.collect()
    
    async def convert_timeframes(self):
        """
        Main function to convert timeframes using chunked processing
        """
        try:
            logger.info(f"Starting timeframe conversion from {self.input_file}")
            logger.info(f"Target timeframes: {list(self.timeframes.keys())}")
            logger.info(f"Chunk size: {self.chunk_size}")

            # Get total number of rows for progress tracking
            total_rows = len(pl.read_parquet(self.input_file, columns=["date"]))
            total_chunks = (total_rows + self.chunk_size - 1) // self.chunk_size
            logger.info(f"Total rows: {total_rows:,}, Total chunks: {total_chunks}")

            # Process data in chunks
            for chunk_num in range(total_chunks):
                offset = chunk_num * self.chunk_size
                logger.info(f"Loading chunk {chunk_num + 1}/{total_chunks} (offset: {offset})")
                
                # Read chunk
                chunk = pl.read_parquet(self.input_file).slice(offset, self.chunk_size)
                
                # Process chunk for all timeframes
                await self._process_single_chunk(chunk, chunk_num + 1)
                
                # Progress update
                progress = ((chunk_num + 1) / total_chunks) * 100
                logger.info(f"Progress: {progress:.1f}% ({chunk_num + 1}/{total_chunks} chunks)")

            # Write any remaining batches
            logger.info("Writing final batches...")
            await self._write_final_batches()

            logger.info("Timeframe conversion completed successfully!")

            # Log final file sizes
            for tf_name, tf_config in self.timeframes.items():
                output_path = self.output_dir / tf_config["output_file"]
                if output_path.exists():
                    final_df = pl.read_parquet(output_path)
                    logger.info(f"{tf_name} output: {len(final_df):,} rows -> {output_path}")
                    del final_df
                    gc.collect()
        
        except Exception as e:
            logger.error(f"Error during timeframe conversion: {e}")
            raise

async def main():
    """Main function"""
    # Configuration
    input_file = "data/historical_5min.parquet"
    output_dir = "data"
    chunk_size = 200000  # Adjust based on available memory
    
    # Check if input file exists
    if not Path(input_file).exists():
        logger.error(f"Input file not found: {input_file}")
        return
    
    # Create converter and run
    converter = TimeframeConverter(input_file, output_dir, chunk_size)
    await converter.convert_timeframes()

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
