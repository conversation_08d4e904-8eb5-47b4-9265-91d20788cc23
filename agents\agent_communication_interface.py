#!/usr/bin/env python3
"""
Agent Communication Interface - Standardized Communication Layer

This module provides a standardized interface for communicating with all trading agents,
enabling seamless integration, message passing, and coordination between agents.

Features:
🔗 1. Standardized Agent Interface
- Common communication protocol for all agents
- Async message passing with response handling
- Health monitoring and status checking
- Load balancing and failover support

[SIGNAL] 2. Message Bus System
- Event-driven communication
- Pub/Sub pattern for agent coordination
- Message queuing and buffering
- Priority-based message handling

[WORKFLOW] 3. Agent Lifecycle Management
- Agent discovery and registration
- Health monitoring and auto-recovery
- Graceful shutdown and restart
- Resource management and cleanup

[SECURITY] 4. Error Handling and Resilience
- Circuit breaker pattern
- Retry mechanisms with exponential backoff
- Fallback strategies
- Comprehensive error logging

Author: AI Assistant
Date: 2025-01-16
"""

import asyncio
import logging
import json
import time
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
from collections import defaultdict, deque
import uuid
import importlib
import inspect

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] COMMUNICATION MODELS
# ═══════════════════════════════════════════════════════════════════════════════

class MessageType(Enum):
    """Message types for agent communication"""
    QUERY = "query"
    COMMAND = "command"
    EVENT = "event"
    RESPONSE = "response"
    STATUS = "status"
    ERROR = "error"
    HEARTBEAT = "heartbeat"

class AgentStatus(Enum):
    """Agent status enumeration"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    BUSY = "busy"
    ERROR = "error"
    STOPPING = "stopping"
    STOPPED = "stopped"

@dataclass
class AgentMessage:
    """Standard message format for agent communication"""
    id: str
    type: MessageType
    sender: str
    recipient: str
    payload: Dict[str, Any]
    timestamp: datetime
    correlation_id: Optional[str] = None
    priority: int = 5  # 1=highest, 10=lowest
    timeout: int = 30  # seconds
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class AgentInfo:
    """Agent information and capabilities"""
    name: str
    type: str
    status: AgentStatus
    capabilities: List[str]
    endpoints: Dict[str, str]
    health_score: float
    last_heartbeat: datetime
    load_factor: float
    instance: Optional[Any] = None
    config: Optional[Dict[str, Any]] = None

@dataclass
class CommunicationStats:
    """Communication statistics"""
    messages_sent: int = 0
    messages_received: int = 0
    messages_failed: int = 0
    avg_response_time: float = 0.0
    error_rate: float = 0.0
    last_activity: Optional[datetime] = None

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 AGENT COMMUNICATION INTERFACE
# ═══════════════════════════════════════════════════════════════════════════════

class AgentCommunicationInterface:
    """
    Standardized communication interface for all trading agents
    
    Provides unified communication layer with message routing, health monitoring,
    load balancing, and error handling for seamless agent coordination.
    """
    
    def __init__(self, agent_name: str = "llm_interface"):
        """Initialize communication interface"""
        self.agent_name = agent_name
        self.logger = logging.getLogger(__name__)
        
        # Agent registry
        self.agents: Dict[str, AgentInfo] = {}
        self.agent_instances: Dict[str, Any] = {}
        
        # Message handling
        self.message_queue = asyncio.Queue()
        self.pending_messages: Dict[str, AgentMessage] = {}
        self.message_handlers: Dict[MessageType, List[Callable]] = defaultdict(list)
        
        # Communication stats
        self.stats: Dict[str, CommunicationStats] = defaultdict(CommunicationStats)
        
        # Circuit breaker states
        self.circuit_breakers: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'state': 'closed',  # closed, open, half-open
            'failure_count': 0,
            'last_failure': None,
            'timeout': 60  # seconds
        })
        
        # Event subscribers
        self.event_subscribers: Dict[str, List[Callable]] = defaultdict(list)
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
        
        self.logger.info(f"🔗 Agent Communication Interface initialized for {agent_name}")
    
    async def initialize(self) -> bool:
        """Initialize the communication interface"""
        try:
            self.logger.info("[INIT] Initializing Agent Communication Interface...")
            
            # Start background tasks
            self.background_tasks = [
                asyncio.create_task(self._message_processor()),
                asyncio.create_task(self._health_monitor()),
                asyncio.create_task(self._circuit_breaker_monitor()),
                asyncio.create_task(self._stats_collector())
            ]
            
            self.is_running = True
            self.logger.info("[SUCCESS] Agent Communication Interface initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error initializing communication interface: {e}")
            return False
    
    async def register_agent(self, agent_name: str, agent_instance: Any, 
                           agent_type: str = "trading_agent",
                           capabilities: List[str] = None) -> bool:
        """Register an agent with the communication interface"""
        try:
            # Extract agent capabilities
            if capabilities is None:
                capabilities = self._extract_capabilities(agent_instance)
            
            # Extract endpoints
            endpoints = self._extract_endpoints(agent_instance)
            
            # Create agent info
            agent_info = AgentInfo(
                name=agent_name,
                type=agent_type,
                status=AgentStatus.INITIALIZING,
                capabilities=capabilities,
                endpoints=endpoints,
                health_score=1.0,
                last_heartbeat=datetime.now(),
                load_factor=0.0,
                instance=agent_instance,
                config=getattr(agent_instance, 'config', {})
            )
            
            # Register agent
            self.agents[agent_name] = agent_info
            self.agent_instances[agent_name] = agent_instance
            
            # Initialize stats
            self.stats[agent_name] = CommunicationStats()
            
            # Send registration event
            await self._publish_event("agent_registered", {
                "agent_name": agent_name,
                "agent_type": agent_type,
                "capabilities": capabilities
            })
            
            self.logger.info(f"[SUCCESS] Registered agent: {agent_name} with {len(capabilities)} capabilities")
            return True
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error registering agent {agent_name}: {e}")
            return False
    
    async def send_message(self, recipient: str, message_type: MessageType,
                          payload: Dict[str, Any], priority: int = 5,
                          timeout: int = 30, correlation_id: str = None) -> Optional[AgentMessage]:
        """Send message to an agent"""
        try:
            # Create message
            message = AgentMessage(
                id=str(uuid.uuid4()),
                type=message_type,
                sender=self.agent_name,
                recipient=recipient,
                payload=payload,
                timestamp=datetime.now(),
                correlation_id=correlation_id,
                priority=priority,
                timeout=timeout
            )
            
            # Check circuit breaker
            if not self._check_circuit_breaker(recipient):
                raise Exception(f"Circuit breaker open for {recipient}")
            
            # Add to queue
            await self.message_queue.put(message)
            
            # Track pending message if expecting response
            if message_type in [MessageType.QUERY, MessageType.COMMAND]:
                self.pending_messages[message.id] = message
            
            # Update stats
            self.stats[recipient].messages_sent += 1
            self.stats[recipient].last_activity = datetime.now()
            
            self.logger.debug(f"📤 Sent {message_type.value} to {recipient}: {message.id}")
            return message
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error sending message to {recipient}: {e}")
            self._record_failure(recipient)
            return None
    
    async def query_agent(self, agent_name: str, method: str, 
                         params: Dict[str, Any] = None, timeout: int = 30) -> Optional[Any]:
        """Query an agent with a specific method"""
        try:
            if agent_name not in self.agents:
                raise Exception(f"Agent {agent_name} not registered")
            
            agent_info = self.agents[agent_name]
            if agent_info.status != AgentStatus.RUNNING:
                raise Exception(f"Agent {agent_name} not running (status: {agent_info.status.value})")
            
            # Prepare payload
            payload = {
                "method": method,
                "params": params or {},
                "timestamp": datetime.now().isoformat()
            }
            
            # Send query message
            message = await self.send_message(
                recipient=agent_name,
                message_type=MessageType.QUERY,
                payload=payload,
                timeout=timeout
            )
            
            if not message:
                return None
            
            # Wait for response
            response = await self._wait_for_response(message.id, timeout)
            
            if response and response.type == MessageType.RESPONSE:
                return response.payload.get('result')
            else:
                raise Exception(f"No valid response from {agent_name}")
                
        except Exception as e:
            self.logger.error(f"[ERROR] Error querying {agent_name}.{method}: {e}")
            self._record_failure(agent_name)
            return None
    
    async def execute_command(self, agent_name: str, command: str,
                            params: Dict[str, Any] = None, timeout: int = 30) -> bool:
        """Execute a command on an agent"""
        try:
            payload = {
                "command": command,
                "params": params or {},
                "timestamp": datetime.now().isoformat()
            }
            
            message = await self.send_message(
                recipient=agent_name,
                message_type=MessageType.COMMAND,
                payload=payload,
                timeout=timeout
            )
            
            if not message:
                return False
            
            response = await self._wait_for_response(message.id, timeout)
            return response and response.payload.get('success', False)
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error executing command {command} on {agent_name}: {e}")
            return False
    
    async def broadcast_event(self, event_type: str, data: Dict[str, Any]):
        """Broadcast event to all registered agents"""
        try:
            payload = {
                "event_type": event_type,
                "data": data,
                "timestamp": datetime.now().isoformat()
            }
            
            for agent_name in self.agents:
                await self.send_message(
                    recipient=agent_name,
                    message_type=MessageType.EVENT,
                    payload=payload,
                    priority=3  # Higher priority for events
                )
            
            # Notify event subscribers
            await self._notify_event_subscribers(event_type, data)
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error broadcasting event {event_type}: {e}")
    
    def subscribe_to_events(self, event_type: str, callback: Callable):
        """Subscribe to specific event types"""
        self.event_subscribers[event_type].append(callback)
        self.logger.info(f"[SIGNAL] Subscribed to events: {event_type}")
    
    def add_message_handler(self, message_type: MessageType, handler: Callable):
        """Add message handler for specific message types"""
        self.message_handlers[message_type].append(handler)
        self.logger.info(f"📨 Added handler for {message_type.value} messages")
    
    async def get_agent_status(self, agent_name: str) -> Optional[AgentInfo]:
        """Get status of a specific agent"""
        return self.agents.get(agent_name)
    
    async def get_all_agents_status(self) -> Dict[str, AgentInfo]:
        """Get status of all registered agents"""
        return self.agents.copy()
    
    def get_communication_stats(self) -> Dict[str, CommunicationStats]:
        """Get communication statistics"""
        return {name: stats for name, stats in self.stats.items()}

    # ═══════════════════════════════════════════════════════════════════════════════
    # [WORKFLOW] BACKGROUND PROCESSING METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _message_processor(self):
        """Background task to process messages"""
        while self.is_running:
            try:
                # Get message from queue
                message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)

                # Process message
                await self._process_message(message)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"[ERROR] Error in message processor: {e}")
                await asyncio.sleep(1)

    async def _process_message(self, message: AgentMessage):
        """Process individual message"""
        try:
            recipient = message.recipient

            # Check if recipient exists
            if recipient not in self.agents:
                await self._send_error_response(message, f"Agent {recipient} not found")
                return

            agent_info = self.agents[recipient]
            agent_instance = agent_info.instance

            # Update agent load
            agent_info.load_factor = min(agent_info.load_factor + 0.1, 1.0)

            # Process based on message type
            if message.type == MessageType.QUERY:
                await self._handle_query(message, agent_instance)
            elif message.type == MessageType.COMMAND:
                await self._handle_command(message, agent_instance)
            elif message.type == MessageType.EVENT:
                await self._handle_event(message, agent_instance)
            else:
                # Call custom handlers
                for handler in self.message_handlers.get(message.type, []):
                    await handler(message)

            # Update stats
            self.stats[recipient].messages_received += 1

        except Exception as e:
            self.logger.error(f"[ERROR] Error processing message {message.id}: {e}")
            await self._send_error_response(message, str(e))
        finally:
            # Reduce agent load
            if message.recipient in self.agents:
                self.agents[message.recipient].load_factor = max(
                    self.agents[message.recipient].load_factor - 0.1, 0.0
                )

    async def _handle_query(self, message: AgentMessage, agent_instance: Any):
        """Handle query message"""
        try:
            method_name = message.payload.get('method')
            params = message.payload.get('params', {})

            if not hasattr(agent_instance, method_name):
                raise Exception(f"Method {method_name} not found")

            method = getattr(agent_instance, method_name)

            # Call method
            if inspect.iscoroutinefunction(method):
                result = await method(**params)
            else:
                result = method(**params)

            # Send response
            await self._send_response(message, {'result': result, 'success': True})

        except Exception as e:
            await self._send_error_response(message, str(e))

    async def _handle_command(self, message: AgentMessage, agent_instance: Any):
        """Handle command message"""
        try:
            command = message.payload.get('command')
            params = message.payload.get('params', {})

            # Map common commands
            command_map = {
                'start': 'start',
                'stop': 'stop',
                'restart': 'restart',
                'status': 'get_status',
                'health_check': 'health_check'
            }

            method_name = command_map.get(command, command)

            if not hasattr(agent_instance, method_name):
                raise Exception(f"Command {command} not supported")

            method = getattr(agent_instance, method_name)

            # Execute command
            if inspect.iscoroutinefunction(method):
                result = await method(**params)
            else:
                result = method(**params)

            # Send response
            await self._send_response(message, {'result': result, 'success': True})

        except Exception as e:
            await self._send_error_response(message, str(e))

    async def _handle_event(self, message: AgentMessage, agent_instance: Any):
        """Handle event message"""
        try:
            event_type = message.payload.get('event_type')
            data = message.payload.get('data', {})

            # Check if agent has event handler
            handler_name = f"on_{event_type}"
            if hasattr(agent_instance, handler_name):
                handler = getattr(agent_instance, handler_name)

                if inspect.iscoroutinefunction(handler):
                    await handler(data)
                else:
                    handler(data)

        except Exception as e:
            self.logger.warning(f"[WARN] Error handling event {message.payload.get('event_type')}: {e}")

    async def _send_response(self, original_message: AgentMessage, payload: Dict[str, Any]):
        """Send response to original message"""
        response = AgentMessage(
            id=str(uuid.uuid4()),
            type=MessageType.RESPONSE,
            sender=original_message.recipient,
            recipient=original_message.sender,
            payload=payload,
            timestamp=datetime.now(),
            correlation_id=original_message.id
        )

        await self.message_queue.put(response)

    async def _send_error_response(self, original_message: AgentMessage, error_message: str):
        """Send error response"""
        await self._send_response(original_message, {
            'error': error_message,
            'success': False
        })

    async def _wait_for_response(self, message_id: str, timeout: int) -> Optional[AgentMessage]:
        """Wait for response to a message"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            # Check for response in queue
            temp_messages = []

            try:
                while True:
                    message = await asyncio.wait_for(self.message_queue.get(), timeout=0.1)

                    if (message.type == MessageType.RESPONSE and
                        message.correlation_id == message_id):

                        # Put back other messages
                        for temp_msg in temp_messages:
                            await self.message_queue.put(temp_msg)

                        # Remove from pending
                        self.pending_messages.pop(message_id, None)

                        return message
                    else:
                        temp_messages.append(message)

            except asyncio.TimeoutError:
                # Put back messages and continue waiting
                for temp_msg in temp_messages:
                    await self.message_queue.put(temp_msg)

                await asyncio.sleep(0.1)

        # Timeout reached
        self.pending_messages.pop(message_id, None)
        return None

    async def _health_monitor(self):
        """Monitor agent health"""
        while self.is_running:
            try:
                current_time = datetime.now()

                for agent_name, agent_info in self.agents.items():
                    # Check heartbeat
                    time_since_heartbeat = (current_time - agent_info.last_heartbeat).total_seconds()

                    if time_since_heartbeat > 60:  # 1 minute timeout
                        agent_info.health_score = max(0.0, agent_info.health_score - 0.1)

                        if agent_info.health_score < 0.3:
                            agent_info.status = AgentStatus.ERROR
                            self.logger.warning(f"[WARN] Agent {agent_name} health degraded")

                    # Send heartbeat request
                    if time_since_heartbeat > 30:  # Send heartbeat every 30 seconds
                        await self.send_message(
                            recipient=agent_name,
                            message_type=MessageType.HEARTBEAT,
                            payload={'timestamp': current_time.isoformat()},
                            priority=1
                        )

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                self.logger.error(f"[ERROR] Error in health monitor: {e}")
                await asyncio.sleep(60)

    async def _circuit_breaker_monitor(self):
        """Monitor and manage circuit breakers"""
        while self.is_running:
            try:
                current_time = datetime.now()

                for agent_name, breaker in self.circuit_breakers.items():
                    if breaker['state'] == 'open':
                        # Check if timeout has passed
                        if (current_time - breaker['last_failure']).total_seconds() > breaker['timeout']:
                            breaker['state'] = 'half-open'
                            breaker['failure_count'] = 0
                            self.logger.info(f"[WORKFLOW] Circuit breaker for {agent_name} moved to half-open")

                await asyncio.sleep(10)  # Check every 10 seconds

            except Exception as e:
                self.logger.error(f"[ERROR] Error in circuit breaker monitor: {e}")
                await asyncio.sleep(30)

    async def _stats_collector(self):
        """Collect and update communication statistics"""
        while self.is_running:
            try:
                current_time = datetime.now()

                for agent_name, stats in self.stats.items():
                    # Calculate error rate
                    total_messages = stats.messages_sent + stats.messages_received
                    if total_messages > 0:
                        stats.error_rate = stats.messages_failed / total_messages

                    # Update last activity
                    if stats.last_activity:
                        time_since_activity = (current_time - stats.last_activity).total_seconds()
                        if time_since_activity > 300:  # 5 minutes
                            # Reset some stats for inactive agents
                            stats.avg_response_time = 0.0

                await asyncio.sleep(60)  # Update every minute

            except Exception as e:
                self.logger.error(f"[ERROR] Error in stats collector: {e}")
                await asyncio.sleep(120)

    # ═══════════════════════════════════════════════════════════════════════════════
    # [TOOLS] UTILITY METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    def _extract_capabilities(self, agent_instance: Any) -> List[str]:
        """Extract capabilities from agent instance"""
        capabilities = []

        # Check for explicit capabilities attribute
        if hasattr(agent_instance, 'capabilities'):
            capabilities.extend(agent_instance.capabilities)

        # Extract public methods as capabilities
        for attr_name in dir(agent_instance):
            if not attr_name.startswith('_'):
                attr = getattr(agent_instance, attr_name)
                if callable(attr):
                    capabilities.append(attr_name)

        return list(set(capabilities))

    def _extract_endpoints(self, agent_instance: Any) -> Dict[str, str]:
        """Extract endpoints from agent instance"""
        endpoints = {}

        # Check for explicit endpoints
        if hasattr(agent_instance, 'endpoints'):
            endpoints.update(agent_instance.endpoints)

        # Default endpoints based on common methods
        common_methods = [
            'get_status', 'health_check', 'get_performance_metrics',
            'start', 'stop', 'restart'
        ]

        for method in common_methods:
            if hasattr(agent_instance, method):
                endpoints[method] = f"/{method}"

        return endpoints

    def _check_circuit_breaker(self, agent_name: str) -> bool:
        """Check if circuit breaker allows communication"""
        breaker = self.circuit_breakers[agent_name]

        if breaker['state'] == 'open':
            return False
        elif breaker['state'] == 'half-open':
            # Allow limited requests in half-open state
            return breaker['failure_count'] < 3
        else:
            return True  # closed state

    def _record_failure(self, agent_name: str):
        """Record communication failure"""
        breaker = self.circuit_breakers[agent_name]
        breaker['failure_count'] += 1
        breaker['last_failure'] = datetime.now()

        # Update stats
        self.stats[agent_name].messages_failed += 1

        # Open circuit breaker if too many failures
        if breaker['failure_count'] >= 5:
            breaker['state'] = 'open'
            self.logger.warning(f"🔴 Circuit breaker opened for {agent_name}")

    def _record_success(self, agent_name: str):
        """Record successful communication"""
        breaker = self.circuit_breakers[agent_name]

        if breaker['state'] == 'half-open':
            # Close circuit breaker on success in half-open state
            breaker['state'] = 'closed'
            breaker['failure_count'] = 0
            self.logger.info(f"🟢 Circuit breaker closed for {agent_name}")

    async def _publish_event(self, event_type: str, data: Dict[str, Any]):
        """Publish event to internal event system"""
        await self._notify_event_subscribers(event_type, data)

    async def _notify_event_subscribers(self, event_type: str, data: Dict[str, Any]):
        """Notify event subscribers"""
        subscribers = self.event_subscribers.get(event_type, [])

        for callback in subscribers:
            try:
                if inspect.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception as e:
                self.logger.error(f"[ERROR] Error in event subscriber: {e}")

    async def shutdown(self):
        """Shutdown the communication interface"""
        try:
            self.logger.info("[STOP] Shutting down Agent Communication Interface...")

            self.is_running = False

            # Cancel background tasks
            for task in self.background_tasks:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

            # Notify agents of shutdown
            await self.broadcast_event("system_shutdown", {
                "timestamp": datetime.now().isoformat(),
                "reason": "Communication interface shutdown"
            })

            # Clear registrations
            self.agents.clear()
            self.agent_instances.clear()

            self.logger.info("[SUCCESS] Agent Communication Interface shutdown complete")

        except Exception as e:
            self.logger.error(f"[ERROR] Error during shutdown: {e}")

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 DEMO AND TESTING FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

class MockAgent:
    """Mock agent for testing"""

    def __init__(self, name: str):
        self.name = name
        self.capabilities = ['test_method', 'get_status', 'health_check']
        self.status = "running"

    async def test_method(self, param1: str = "default") -> str:
        return f"Test result from {self.name}: {param1}"

    def get_status(self) -> Dict[str, Any]:
        return {"status": self.status, "agent": self.name}

    async def health_check(self) -> bool:
        return True

    def on_test_event(self, data: Dict[str, Any]):
        print(f"Agent {self.name} received event: {data}")

async def demo_agent_communication():
    """Demo function for agent communication"""
    print("🔗 Agent Communication Interface Demo")
    print("=" * 50)

    # Initialize communication interface
    comm = AgentCommunicationInterface("demo_interface")
    await comm.initialize()

    # Create mock agents
    agent1 = MockAgent("test_agent_1")
    agent2 = MockAgent("test_agent_2")

    # Register agents
    await comm.register_agent("agent1", agent1, "test_agent")
    await comm.register_agent("agent2", agent2, "test_agent")

    print(f"\n[LIST] Registered Agents: {list(comm.agents.keys())}")

    # Test query
    print("\n[DEBUG] Testing Query...")
    result = await comm.query_agent("agent1", "test_method", {"param1": "Hello World"})
    print(f"Query Result: {result}")

    # Test command
    print("\n⚙️ Testing Command...")
    success = await comm.execute_command("agent1", "status")
    print(f"Command Success: {success}")

    # Test event broadcast
    print("\n[SIGNAL] Testing Event Broadcast...")
    await comm.broadcast_event("test_event", {"message": "Hello from demo"})

    # Show stats
    print("\n[STATUS] Communication Stats:")
    stats = comm.get_communication_stats()
    for agent_name, agent_stats in stats.items():
        print(f"  {agent_name}: {agent_stats.messages_sent} sent, {agent_stats.messages_received} received")

    # Show agent status
    print("\n[AGENT] Agent Status:")
    agents_status = await comm.get_all_agents_status()
    for name, info in agents_status.items():
        print(f"  {name}: {info.status.value} (Health: {info.health_score:.1f})")

    # Cleanup
    await comm.shutdown()
    print("\n[SUCCESS] Demo completed!")

if __name__ == "__main__":
    asyncio.run(demo_agent_communication())
