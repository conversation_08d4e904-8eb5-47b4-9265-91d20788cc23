# Market Monitoring Agent Configuration
# Real-time market data tracking, environment detection, and strategy triggering

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 SHARED CONFIGURATION REFERENCE
# ═══════════════════════════════════════════════════════════════════════════════
# This agent uses centralized environment configuration
# See: config/environment_config.yaml and .env file

# Angel One SmartAPI Configuration (Uses centralized environment variables)
smartapi:
  # Credentials from environment variables (defined in .env file)
  api_key: "${SMARTAPI_API_KEY}"
  username: "${SMARTAPI_USERNAME}"
  password: "${SMARTAPI_PASSWORD}"
  totp_token: "${SMARTAPI_TOTP_TOKEN}"
  
  # WebSocket Configuration
  websocket:
    reconnect_attempts: 5
    reconnect_delay: 5  # seconds
    heartbeat_interval: 30  # seconds
    max_message_queue: 1000
    
  # Subscription Configuration
  subscription:
    mode: 1  # LTP mode (1=LTP, 2=Quote, 3=SnapQuote)
    exchange_type: 1  # NSE (1=NSE, 2=NFO, 3=BSE, etc.)
    
# Market Data Configuration
market_data:
  # Symbols to monitor (will be loaded from Nifty500 universe)
  symbols_source: "nifty500"
  max_symbols: 500
  min_symbols: 100
  symbols: []
  
  # Timeframes for OHLC aggregation
  timeframes:
    - "1min"
    - "5min"
    - "15min"
    - "30min"
    - "1hr"
    
  # Data retention (in memory)
  max_candles_per_timeframe: 1000
  
  # Indicator Configuration
  indicators:
    ema_periods: [5, 10, 13, 20, 21, 30, 50, 100]
    sma_periods: [20]
    rsi_periods: [5, 14]
    macd_config:
      fast: 12
      slow: 26
      signal: 9
    stoch_config:
      k_period: 14
      d_period: 3
    cci_period: 14
    adx_period: 14
    mfi_period: 14
    bb_config:
      period: 20
      std_dev: 2
    atr_period: 14
    supertrend_config:
      period: 10
      multiplier: 3
    donchian_period: 20
    
# Environment Detection Configuration
environment:
  # Market Regime Detection
  regime_detection:
    lookback_period: 50  # days
    volatility_threshold: 0.02  # 2% daily volatility
    trend_threshold: 0.1  # 10% price change for trend
    
  # Market Breadth (Nifty 500 analysis)
  market_breadth:
    enable: true
    index_symbols: ["NIFTY", "BANKNIFTY", "FINNIFTY"]
    breadth_threshold: 0.6  # 60% stocks above EMA20
    
  # Volatility Analysis
  volatility:
    vix_symbol: "INDIA VIX"
    high_vol_threshold: 25
    low_vol_threshold: 15
    
  # Correlation Analysis
  correlation:
    enable: true
    lookback_period: 20  # days
    high_correlation_threshold: 0.7
    
  # Volume Analysis
  volume:
    unusual_volume_multiplier: 2.0  # 2x average volume
    volume_ma_period: 20
    
# Strategy Triggering Configuration
strategy_triggering:
  # AI Model Integration
  ai_model:
    enable: true
    model_path: "data/models"
    confidence_threshold: 0.7
    
  # Entry Conditions
  entry_conditions:
    min_liquidity: 1000000  # Min volume in rupees
    max_spread_percent: 0.5  # Max bid-ask spread %
    max_volatility_percentile: 90  # Max volatility percentile
    
  # Risk Management
  risk_management:
    max_position_size_percent: 1.0  # 1% of capital per trade
    max_daily_trades: 10
    max_concurrent_positions: 5
    
  # Dynamic R:R based on regime
  risk_reward:
    bull_market: 1.5
    bear_market: 2.0
    sideways_market: 1.2
    
# Notifications Configuration (Uses centralized environment variables)
notifications:
  # Telegram Bot
  telegram:
    enable: true
    bot_token: "${TELEGRAM_BOT_TOKEN}"
    chat_id: "${TELEGRAM_CHAT_ID}"
    
    # Message Templates
    templates:
      signal: "SIGNAL: {symbol} | {strategy} | {action} | Price: {price} | Target: {target} | SL: {stop_loss}"
      regime_change: "REGIME CHANGE: Market shifted to {regime} | VIX: {vix} | Breadth: {breadth}%"
      error: "ERROR: {component} | {message}"
      
  # Slack Integration (optional)
  slack:
    enable: false
    webhook_url: "${SLACK_WEBHOOK_URL}"

  # Email Alerts (optional)
  email:
    enable: false
    smtp_server: "${EMAIL_SMTP_SERVER}"
    smtp_port: "${EMAIL_SMTP_PORT}"
    username: "${EMAIL_USERNAME}"
    password: "${EMAIL_PASSWORD}"
    
# Logging Configuration
logging:
  level: "DEBUG"  # DEBUG, INFO, WARNING, ERROR
  
  # File Logging
  file_logging:
    enable: true
    log_dir: "logs"
    max_file_size: "10MB"
    backup_count: 5
    
  # Signal Logging
  signal_logging:
    enable: true
    signal_log_file: "logs/signals.log"
    include_market_context: true
    
  # Performance Logging
  performance_logging:
    enable: true
    metrics_file: "logs/performance_metrics.log"
    log_interval: 300  # seconds
    
# Dashboard Configuration
dashboard:
  enable: true
  update_interval: 5  # seconds
  
  # Web Dashboard (optional)
  web_dashboard:
    enable: false
    host: "localhost"
    port: 8080
    
# Storage Configuration
storage:
  # Real-time data storage
  realtime_data:
    enable: true
    storage_path: "data/realtime"
    compression: "gzip"
    
  # Signal storage
  signals:
    storage_path: "data/signals"
    format: "parquet"
    
  # Market context storage
  market_context:
    storage_path: "data/market_context"
    retention_days: 30
    
# Performance Configuration
performance:
  # Processing Configuration
  processing:
    max_workers: 4
    chunk_size: 1000
    
  # Memory Management
  memory:
    max_memory_usage_percent: 80
    cleanup_interval: 3600  # seconds
    
  # CPU Usage
  cpu:
    max_cpu_usage_percent: 70
    
# Error Handling Configuration
error_handling:
  # Retry Configuration
  retry:
    max_attempts: 3
    backoff_factor: 2
    
  # Circuit Breaker
  circuit_breaker:
    failure_threshold: 5
    recovery_timeout: 300  # seconds
    
  # Graceful Shutdown
  shutdown:
    timeout: 30  # seconds
    save_state: true
