"""
Tests for Feature 1: Multi-Strategy & Multi-Timeframe Backtesting
"""
import pytest
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch

from agents.enhanced_backtesting_polars import (
    MultiStrategyBacktester, BacktestConfig, BacktestMode, RiskModel
)


class TestMultiStrategyBacktester:
    """Test suite for MultiStrategyBacktester class"""
    
    @pytest.fixture
    def backtester(self):
        """Create MultiStrategyBacktester instance"""
        return MultiStrategyBacktester()
    
    @pytest.mark.asyncio
    async def test_initialization(self, backtester):
        """Test proper initialization of MultiStrategyBacktester"""
        assert backtester.strategies == []
        assert backtester.portfolio_results == {}
        assert backtester.correlation_matrix is None
        assert backtester.diversification_ratio == 0.0
    
    @pytest.mark.asyncio
    async def test_add_strategy(self, backtester, sample_strategy):
        """Test adding strategies to the backtester"""
        await backtester.add_strategy(sample_strategy, weight=0.5)
        
        assert len(backtester.strategies) == 1
        assert backtester.strategies[0]['strategy'] == sample_strategy
        assert backtester.strategies[0]['weight'] == 0.5
        assert backtester.strategies[0]['timeframe'] == '1D'  # default
    
    @pytest.mark.asyncio
    async def test_add_multiple_strategies(self, backtester, multiple_strategies):
        """Test adding multiple strategies"""
        for i, strategy in enumerate(multiple_strategies):
            weight = strategy.get('weight', 1.0 / len(multiple_strategies))
            await backtester.add_strategy(strategy, weight=weight)
        
        assert len(backtester.strategies) == 2
        total_weight = sum(s['weight'] for s in backtester.strategies)
        assert abs(total_weight - 1.0) < 0.01  # Should sum to approximately 1.0
    
    @pytest.mark.asyncio
    async def test_weight_normalization(self, backtester, multiple_strategies):
        """Test that weights are properly normalized"""
        # Add strategies with weights that don't sum to 1
        await backtester.add_strategy(multiple_strategies[0], weight=0.3)
        await backtester.add_strategy(multiple_strategies[1], weight=0.5)
        
        normalized_weights = await backtester._normalize_weights()
        
        assert len(normalized_weights) == 2
        assert abs(sum(normalized_weights) - 1.0) < 1e-10
        assert abs(normalized_weights[0] - 0.375) < 1e-10  # 0.3/0.8
        assert abs(normalized_weights[1] - 0.625) < 1e-10  # 0.5/0.8
    
    @pytest.mark.asyncio
    async def test_portfolio_backtest_empty_strategies(self, backtester, sample_market_data, sample_backtest_config):
        """Test portfolio backtest with no strategies"""
        result = await backtester.run_portfolio_backtest(sample_market_data, sample_backtest_config)
        
        assert result['total_strategies'] == 0
        assert result['portfolio_metrics']['total_return'] == 0
        assert len(result['individual_results']) == 0
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_portfolio_backtest_single_strategy(self, mock_simulate, backtester, 
                                                    sample_strategy, sample_market_data, 
                                                    sample_backtest_config, sample_trades):
        """Test portfolio backtest with single strategy"""
        # Mock the simulate_trades_vectorized function
        mock_simulate.return_value = sample_trades
        
        await backtester.add_strategy(sample_strategy, weight=1.0)
        result = await backtester.run_portfolio_backtest(sample_market_data, sample_backtest_config)
        
        assert result['total_strategies'] == 1
        assert 'portfolio_metrics' in result
        assert 'individual_results' in result
        assert len(result['individual_results']) == 1
        assert result['individual_results'][0]['strategy_name'] == sample_strategy['name']
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_portfolio_backtest_multiple_strategies(self, mock_simulate, backtester,
                                                        multiple_strategies, sample_market_data,
                                                        sample_backtest_config, sample_trades):
        """Test portfolio backtest with multiple strategies"""
        # Mock different returns for each strategy
        def mock_trades_side_effect(*args, **kwargs):
            strategy = args[1]  # Second argument is strategy
            if 'Strategy A' in strategy['name']:
                return sample_trades[:10]  # First 10 trades
            else:
                return sample_trades[10:]  # Last 10 trades
        
        mock_simulate.side_effect = mock_trades_side_effect
        
        for strategy in multiple_strategies:
            weight = strategy.get('weight', 0.5)
            await backtester.add_strategy(strategy, weight=weight)
        
        result = await backtester.run_portfolio_backtest(sample_market_data, sample_backtest_config)
        
        assert result['total_strategies'] == 2
        assert len(result['individual_results']) == 2
        assert 'correlation_matrix' in result
        assert 'diversification_ratio' in result
    
    @pytest.mark.asyncio
    async def test_calculate_correlation_matrix(self, backtester):
        """Test correlation matrix calculation"""
        # Create sample returns data
        returns_data = {
            'Strategy A': [0.01, 0.02, -0.01, 0.015, 0.005],
            'Strategy B': [0.005, 0.01, 0.02, -0.005, 0.01],
            'Strategy C': [-0.01, 0.015, 0.01, 0.02, -0.005]
        }
        
        correlation_matrix = await backtester._calculate_correlation_matrix(returns_data)
        
        # Check matrix properties
        assert correlation_matrix.shape == (3, 3)
        
        # Diagonal should be 1.0 (self-correlation)
        for i in range(3):
            assert abs(correlation_matrix[i, i] - 1.0) < 1e-10
        
        # Matrix should be symmetric
        for i in range(3):
            for j in range(3):
                assert abs(correlation_matrix[i, j] - correlation_matrix[j, i]) < 1e-10
    
    @pytest.mark.asyncio
    async def test_calculate_diversification_ratio(self, backtester):
        """Test diversification ratio calculation"""
        # Sample correlation matrix and weights
        correlation_matrix = np.array([
            [1.0, 0.3, 0.1],
            [0.3, 1.0, 0.2],
            [0.1, 0.2, 1.0]
        ])
        weights = np.array([0.4, 0.4, 0.2])
        volatilities = np.array([0.15, 0.18, 0.12])
        
        div_ratio = await backtester._calculate_diversification_ratio(
            correlation_matrix, weights, volatilities
        )
        
        # Diversification ratio should be >= 1.0
        assert div_ratio >= 1.0
        
        # With perfect correlation (correlation = 1.0), ratio should be 1.0
        perfect_corr_matrix = np.ones((3, 3))
        div_ratio_perfect = await backtester._calculate_diversification_ratio(
            perfect_corr_matrix, weights, volatilities
        )
        assert abs(div_ratio_perfect - 1.0) < 1e-10
    
    @pytest.mark.asyncio
    async def test_combine_portfolio_returns(self, backtester):
        """Test portfolio return combination"""
        individual_results = [
            {
                'strategy_name': 'Strategy A',
                'trades': [
                    {'pnl_pct': 1.0, 'entry_time': datetime(2023, 1, 1)},
                    {'pnl_pct': -0.5, 'entry_time': datetime(2023, 1, 2)}
                ]
            },
            {
                'strategy_name': 'Strategy B',
                'trades': [
                    {'pnl_pct': 0.5, 'entry_time': datetime(2023, 1, 1)},
                    {'pnl_pct': 1.5, 'entry_time': datetime(2023, 1, 2)}
                ]
            }
        ]
        weights = [0.6, 0.4]
        
        portfolio_returns = await backtester._combine_portfolio_returns(
            individual_results, weights
        )
        
        assert len(portfolio_returns) == 2
        # First return: 0.6 * 1.0 + 0.4 * 0.5 = 0.8
        assert abs(portfolio_returns[0] - 0.8) < 1e-10
        # Second return: 0.6 * (-0.5) + 0.4 * 1.5 = 0.3
        assert abs(portfolio_returns[1] - 0.3) < 1e-10
    
    @pytest.mark.asyncio
    async def test_multi_timeframe_coordination(self, backtester, sample_strategy, sample_market_data):
        """Test multi-timeframe strategy coordination"""
        # Add same strategy with different timeframes
        await backtester.add_strategy(sample_strategy, weight=0.5, timeframe='1D')
        
        strategy_4h = sample_strategy.copy()
        strategy_4h['name'] = 'Test SMA Crossover 4H'
        await backtester.add_strategy(strategy_4h, weight=0.5, timeframe='4H')
        
        # Test timeframe coordination
        coordination_result = await backtester._coordinate_multi_timeframe_signals(
            sample_market_data, datetime(2023, 1, 15)
        )
        
        assert 'timeframe_signals' in coordination_result
        assert 'coordination_score' in coordination_result
        assert coordination_result['coordination_score'] >= 0
        assert coordination_result['coordination_score'] <= 1
    
    @pytest.mark.asyncio
    async def test_error_handling_invalid_weights(self, backtester, sample_strategy):
        """Test error handling for invalid weights"""
        with pytest.raises(ValueError, match="Weight must be positive"):
            await backtester.add_strategy(sample_strategy, weight=-0.1)
        
        with pytest.raises(ValueError, match="Weight must be positive"):
            await backtester.add_strategy(sample_strategy, weight=0)
    
    @pytest.mark.asyncio
    async def test_error_handling_invalid_timeframe(self, backtester, sample_strategy):
        """Test error handling for invalid timeframes"""
        with pytest.raises(ValueError, match="Invalid timeframe"):
            await backtester.add_strategy(sample_strategy, weight=0.5, timeframe='invalid')
