#!/usr/bin/env python3
"""
Test Automatic Date Management and Data Retention
Demonstrates automatic cutoff date updates and intelligent data retention
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from agents.ai_training_agent import AITrainingAgent
from utils.data_retention_manager import DataRetentionManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_automatic_date_management():
    """Test automatic training cutoff date management"""
    
    logger.info("📅 Testing Automatic Date Management")
    logger.info("=" * 50)
    
    try:
        # Initialize AI Training Agent
        agent = AITrainingAgent()
        
        # Show current cutoff date
        current_cutoff = getattr(agent.config, 'training_cutoff_date', 'Not set')
        logger.info(f"📊 Current training cutoff date: {current_cutoff}")
        
        # Test with sample data to see date update
        feature_file = "data/features/features_historical_15min.parquet"
        
        if not Path(feature_file).exists():
            logger.warning(f"⚠️ Feature file not found: {feature_file}")
            return
        
        # Enable online learning and date management
        agent.config.online_learning_enabled = True
        agent.config.date_based_filtering = True
        agent.config.memory_efficient_mode = True
        
        logger.info("🔄 Testing automatic date update...")
        
        # Process a small chunk to test date update
        chunk_count = 0
        latest_date = None
        
        for chunk in agent.stream_data_with_date_filter(feature_file, chunk_size=1000):
            chunk_count += 1
            if 'date' in chunk.columns:
                latest_date = chunk['date'].max()
            
            # Test the date update function
            agent._update_training_cutoff_date(chunk)
            
            # Only test with first chunk
            break
        
        # Check if date was updated
        new_cutoff = getattr(agent.config, 'training_cutoff_date', 'Not set')
        logger.info(f"📊 Updated training cutoff date: {new_cutoff}")
        logger.info(f"📅 Latest data date processed: {latest_date}")
        
        if current_cutoff != new_cutoff:
            logger.info("✅ Automatic date update working correctly!")
        else:
            logger.info("ℹ️ Date not updated (may be same as current)")
        
        # Check training metadata
        metadata_path = Path("data/models/metadata/training_metadata.json")
        if metadata_path.exists():
            import json
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            logger.info("📋 Training Metadata:")
            for key, value in metadata.items():
                logger.info(f"   {key}: {value}")
        
    except Exception as e:
        logger.error(f"❌ Automatic date management test failed: {e}")

def test_data_retention_analysis():
    """Test data retention analysis and recommendations"""
    
    logger.info("🗂️ Testing Data Retention Analysis")
    logger.info("=" * 50)
    
    try:
        # Initialize Data Retention Manager
        manager = DataRetentionManager()
        
        # Get data usage analysis
        logger.info("📊 Analyzing current data usage...")
        analysis = manager.analyze_data_usage()
        
        total_size_gb = 0
        total_files = 0
        
        for data_type, info in analysis.items():
            logger.info(f"\n📁 {data_type.upper()}")
            logger.info(f"   Directory: {info['directory']}")
            logger.info(f"   Files: {info['total_files']}")
            logger.info(f"   Size: {info['total_size_gb']:.2f} GB")
            logger.info(f"   To delete: {info['files_to_delete']} files")
            logger.info(f"   To archive: {info['files_to_archive']} files")
            
            total_size_gb += info['total_size_gb']
            total_files += info['total_files']
            
            if info['recommendations']:
                logger.info(f"   💡 Recommendations:")
                for rec in info['recommendations']:
                    logger.info(f"      • {rec}")
        
        # Get retention summary
        summary = manager.get_retention_summary()
        
        logger.info(f"\n📋 RETENTION SUMMARY")
        logger.info(f"   Total data: {summary['total_data_size_gb']} GB")
        logger.info(f"   Total files: {summary['total_files']}")
        logger.info(f"   Potential savings: {summary['potential_space_savings_gb']} GB")
        
        if summary['recommendations']:
            logger.info(f"   🎯 Global Recommendations:")
            for rec in summary['recommendations']:
                logger.info(f"      • {rec}")
        
        # Test cleanup (dry run)
        logger.info(f"\n🧹 Testing cleanup (dry run)...")
        cleanup_results = manager.cleanup_old_data(dry_run=True)
        
        logger.info(f"   Would delete: {len(cleanup_results['deleted'])} files")
        logger.info(f"   Would archive: {len(cleanup_results['archived'])} files")
        logger.info(f"   Space to free: {cleanup_results['space_freed_mb']:.2f} MB")
        
        if cleanup_results['errors']:
            logger.warning(f"   Errors: {len(cleanup_results['errors'])}")
        
        # Show some examples
        if cleanup_results['deleted'][:3]:
            logger.info(f"   Example deletions:")
            for item in cleanup_results['deleted'][:3]:
                logger.info(f"      • {item}")
        
        if cleanup_results['archived'][:3]:
            logger.info(f"   Example archives:")
            for item in cleanup_results['archived'][:3]:
                logger.info(f"      • {item}")
        
    except Exception as e:
        logger.error(f"❌ Data retention analysis failed: {e}")

def show_data_retention_recommendations():
    """Show specific recommendations for the user's scenario"""
    
    logger.info("💡 Data Retention Recommendations for Your Scenario")
    logger.info("=" * 60)
    
    recommendations = [
        {
            'category': '📅 Automatic Date Management',
            'items': [
                'Training cutoff date will auto-update after each training run',
                'No manual date changes needed in config files',
                'System tracks latest processed data automatically',
                'Training metadata saved for audit trail'
            ]
        },
        {
            'category': '🗂️ Historical Data',
            'items': [
                'Keep last 90 days of raw historical data',
                'Archive data older than 30 days with compression',
                'Delete data older than 90 days (can be re-downloaded)',
                'Prioritize 5min and 15min timeframes'
            ]
        },
        {
            'category': '🧠 Feature Data',
            'items': [
                'Keep last 60 days of processed features (expensive to regenerate)',
                'Archive features older than 21 days',
                'Features are high priority - avoid deletion',
                'Compress archived features with brotli'
            ]
        },
        {
            'category': '📊 Backtesting Results',
            'items': [
                'Keep 6 months of backtest results for analysis',
                'Archive results older than 60 days',
                'Preserve final/production backtest results permanently',
                'Regular cleanup of experimental results'
            ]
        },
        {
            'category': '🤖 Model Data',
            'items': [
                'Keep last 10 model versions',
                'Archive old versions instead of deleting',
                'Never delete production models',
                'Backup models before any cleanup'
            ]
        },
        {
            'category': '⚡ Performance Benefits',
            'items': [
                'Reduced storage costs (50-80% space savings)',
                'Faster data loading and processing',
                'Improved backup and sync performance',
                'Better system responsiveness'
            ]
        }
    ]
    
    for rec in recommendations:
        logger.info(f"\n{rec['category']}")
        for item in rec['items']:
            logger.info(f"   ✓ {item}")
    
    logger.info(f"\n🎯 ANSWER TO YOUR QUESTIONS:")
    logger.info(f"   ❓ Do I need to manually update dates?")
    logger.info(f"   ✅ NO - System auto-updates training cutoff date")
    logger.info(f"")
    logger.info(f"   ❓ Do I need to keep all old data?")
    logger.info(f"   ✅ NO - Smart retention keeps only what's needed")
    logger.info(f"")
    logger.info(f"   ❓ What data is safe to delete?")
    logger.info(f"   ✅ Historical data >90 days (can re-download)")
    logger.info(f"   ✅ Old backtest results >6 months")
    logger.info(f"   ✅ Log files >30 days")
    logger.info(f"")
    logger.info(f"   ❓ What data should I keep?")
    logger.info(f"   ✅ Feature data (expensive to regenerate)")
    logger.info(f"   ✅ Recent models and checkpoints")
    logger.info(f"   ✅ Production backtest results")

async def main():
    """Main test function"""
    
    logger.info("🚀 Testing Automatic Management Systems")
    logger.info("=" * 80)
    
    # Test 1: Automatic date management
    await test_automatic_date_management()
    
    print("\n" + "=" * 80 + "\n")
    
    # Test 2: Data retention analysis
    test_data_retention_analysis()
    
    print("\n" + "=" * 80 + "\n")
    
    # Test 3: Show recommendations
    show_data_retention_recommendations()
    
    logger.info("\n🎉 All tests completed!")
    logger.info("\n💡 Next Steps:")
    logger.info("   1. Download 60 days of data - system will auto-filter")
    logger.info("   2. Run training - cutoff date will auto-update")
    logger.info("   3. Enable auto-cleanup for ongoing maintenance")
    logger.info("   4. Monitor data usage with retention manager")

if __name__ == "__main__":
    asyncio.run(main())
