# Performance Analysis Agent Guide

## Overview

The Performance Analysis Agent is a comprehensive post-trade analytics system that provides detailed performance metrics, strategy analysis, execution quality tracking, and integration with Angel One broker API for real-time performance monitoring.

## 🎯 Key Features

### 📈 1. Post-Trade Metrics Calculation
- **ROI**: Return on capital for each strategy/symbol combination
- **Accuracy**: Percentage of profitable trades
- **Expectancy**: Average profit per trade over time
- **Risk-Reward Ratios**: Average win/loss ratios
- **Drawdown Analysis**: Maximum drawdown and recovery duration
- **Sharpe Ratio**: Risk-adjusted return over volatility
- **Profit Factor**: Sum of profits divided by sum of losses
- **Holding Time**: Average trade duration analysis

### 🧠 2. Strategy-Wise and Regime-Wise Performance
- **Strategy Comparison**: Identify consistently outperforming strategies
- **RR Combo Ranking**: Compare different risk-reward ratio combinations
- **Regime-Specific Stats**: Performance during Bull/Bear/Sideways markets
- **Time-Based Analysis**: Morning vs. closing hours performance
- **Symbol-Strategy Mapping**: Optimal strategy-stock combinations

### 🧾 3. Execution Quality Analysis
- **Signal-to-Fill Time**: Latency from signal generation to execution
- **Slippage Tracking**: Entry/exit price vs. expected price analysis
- **Missed Trade Logging**: Signals not executed due to various reasons
- **Fill Ratio**: Percentage of signals successfully executed

### 📤 4. Output Reports + Feedback
- **CSV/Parquet Export**: For ML training and external analysis
- **Daily PnL Reports**: Per strategy, symbol, and timeframe
- **Equity Curve Tracking**: Rolling cumulative PnL visualization
- **Agent Feedback**: Performance insights for other agents

### 📡 5. Angel One Broker Integration
- **Tradebook API**: Pull actual trades from Angel One
- **Order Status Verification**: Match orders with actual fills
- **Funds & PnL API**: Track available margin and daily PnL
- **Execution Time Logs**: Millisecond-level execution tracking

## 🏗️ Architecture

### Core Components

1. **PerformanceAnalysisAgent**: Main agent class
2. **Data Models**: TradeMetrics, StrategyPerformance, ExecutionQuality
3. **Configuration**: YAML-based configuration system
4. **Storage**: Polars/PyArrow-based data persistence
5. **Reporting**: Automated report generation
6. **Integration**: Angel One API and other agent connections

### Data Flow

```
[ Trade Execution Agent ] + [ Signal Agent ] + [ Angel One API ]
                                ⬇︎
                    [ Performance Analysis Agent ]
                                ⬇︎
        [ Data Processing ] → [ Metrics Calculation ] → [ Storage ]
                                ⬇︎
        [ Report Generation ] → [ Agent Feedback ] → [ ML Training Data ]
```

## 🚀 Quick Start

### 1. Installation

Ensure you have the required dependencies:

```bash
pip install polars pyarrow polars_ta smartapi-python
```

### 2. Configuration

Copy and customize the configuration file:

```bash
cp config/performance_analysis_config.yaml config/my_performance_config.yaml
```

Key configuration sections:
- `angel_one_api`: Angel One API settings
- `data_sources`: Data source paths and settings
- `analysis`: Analysis parameters and thresholds
- `storage`: Data storage configuration
- `reporting`: Report generation settings

### 3. Environment Variables

Set up Angel One API credentials:

```bash
export ANGEL_API_KEY="your_api_key"
export ANGEL_CLIENT_CODE="your_client_code"
export ANGEL_PASSWORD="your_password"
export ANGEL_TOTP_SECRET="your_totp_secret"
```

### 4. Running the Agent

```bash
# Basic usage
python agents/run_performance_analysis.py

# With custom config
python agents/run_performance_analysis.py --config config/my_performance_config.yaml

# With debug logging
python agents/run_performance_analysis.py --log-level DEBUG
```

## 📊 Performance Metrics

### Individual Trade Metrics

Each trade is analyzed for:
- Entry/exit prices and times
- PnL (absolute and percentage)
- Holding time
- Slippage analysis
- Market regime context
- Signal-to-fill latency

### Strategy-Level Metrics

Strategies are evaluated on:
- Total number of trades
- Win rate and accuracy
- Average win/loss amounts
- Expectancy and ROI
- Sharpe ratio
- Profit factor
- Maximum drawdown
- Average holding time

### Execution Quality Metrics

Execution performance includes:
- Average signal-to-fill time
- Slippage percentages
- Fill ratios
- Missed trade counts
- Order rejection rates

## 🔧 Configuration Options

### Analysis Settings

```yaml
analysis:
  analysis_interval_minutes: 15
  initial_capital: 100000
  min_trades_for_analysis: 5
  regime_detection:
    enabled: true
    lookback_days: 30
```

### Storage Settings

```yaml
storage:
  trade_data_path: "data/performance/trades"
  strategy_performance_path: "data/performance/strategies"
  file_format: "parquet"
  compression: "zstd"
```

### Reporting Settings

```yaml
reporting:
  daily_report:
    enabled: true
    generation_time: "16:00"
  formats:
    json: true
    csv: true
    parquet: true
```

## 📈 Data Integration

### Data Sources

The agent integrates with multiple data sources:

1. **Angel One API**: Live tradebook and position data
2. **Execution Agent**: Trade execution logs
3. **Signal Generation Agent**: Signal data for execution quality
4. **Backtesting Results**: Historical strategy performance
5. **Market Monitoring Agent**: Market regime data

### Data Formats

All data is stored in efficient formats:
- **Parquet**: Primary storage format with compression
- **JSON**: Reports and configuration
- **CSV**: Optional export format

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
python -m pytest test/test_performance_analysis_agent.py -v

# Run specific test
python -m pytest test/test_performance_analysis_agent.py::TestPerformanceAnalysisAgent::test_strategy_performance_calculation -v
```

## 📋 Example Output

### Daily Report Structure

```json
{
  "date": "2024-01-15",
  "summary": {
    "total_trades": 36,
    "winning_trades": 22,
    "win_rate": 0.61,
    "total_pnl": 1250.50,
    "strategies_tracked": 8
  },
  "strategy_performance": [
    {
      "strategy": "VWAP_Reversal",
      "symbol": "RELIANCE-EQ",
      "timeframe": "5min",
      "roi": 11.2,
      "sharpe_ratio": 1.67,
      "profit_factor": 1.93
    }
  ],
  "execution_quality": [
    {
      "symbol": "RELIANCE-EQ",
      "avg_signal_to_fill_ms": 150.5,
      "avg_slippage_percent": 0.08,
      "fill_ratio": 95.2
    }
  ]
}
```

## 🔗 Integration with Other Agents

### Signal Generation Agent
- Receives signal data for execution quality analysis
- Provides performance feedback for strategy optimization

### Execution Agent
- Receives trade execution data
- Provides execution quality metrics

### Risk Management Agent
- Receives performance alerts and thresholds
- Provides risk-adjusted performance metrics

### AI Training Agent
- Exports performance data for model training
- Receives model predictions for strategy ranking

## 🚨 Monitoring and Alerts

### Health Checks
- Memory usage monitoring
- Processing time tracking
- Data freshness validation
- API connection status

### Performance Alerts
- ROI below threshold
- Drawdown exceeding limits
- Win rate degradation
- Execution quality issues

## 🛠️ Troubleshooting

### Common Issues

1. **Configuration Errors**
   - Verify YAML syntax
   - Check required sections
   - Validate file paths

2. **API Connection Issues**
   - Verify Angel One credentials
   - Check network connectivity
   - Review API rate limits

3. **Data Processing Errors**
   - Check data format compatibility
   - Verify column mappings
   - Review memory usage

4. **Performance Issues**
   - Adjust chunk sizes
   - Enable parallel processing
   - Optimize data retention

### Logging

Enable detailed logging for debugging:

```yaml
logging:
  level: "DEBUG"
  performance_logging:
    enable: true
    interval_minutes: 1
```

## 📚 Advanced Usage

### Custom Metrics

Extend the agent with custom performance metrics:

```python
def custom_metric_calculation(self, df: pl.DataFrame) -> float:
    # Your custom metric logic
    return calculated_value
```

### Integration Patterns

Connect with external systems:

```python
async def external_integration(self):
    # Export data to external systems
    # Import additional data sources
    pass
```

## 🔄 Maintenance

### Data Cleanup

Configure automatic data retention:

```yaml
storage:
  retention:
    trade_data_days: 365
    reports_days: 90
    logs_days: 30
```

### Backup Strategy

Enable automatic backups:

```yaml
storage:
  backup:
    enabled: true
    backup_interval_hours: 24
    max_backup_files: 7
```

This comprehensive Performance Analysis Agent provides the foundation for sophisticated trading performance analytics with real-time monitoring, detailed reporting, and seamless integration with your trading infrastructure.
