# AI Training Agent - Complete Guide

## 🎯 Overview

The AI Training Agent is a sophisticated multi-target machine learning system designed to predict strategy performance metrics and rank trading strategies. It combines **LightGBM** and **TabNet** in an ensemble approach to predict multiple continuous targets rather than just binary classification.

### Key Features

- **Multi-Target Prediction**: Predicts ROI, Sharpe ratio, expectancy, max drawdown, profit factor, risk-reward ratio, and accuracy
- **Ensemble Learning**: Combines LightGBM (gradient boosting) and TabNet (neural networks) for optimal performance
- **Strategy Ranking**: Multi-objective ranking with Pareto optimization and market regime conditioning
- **GPU Acceleration**: Supports CUDA for faster training and inference
- **Hyperparameter Optimization**: Automated tuning with Optuna
- **Real-time Serving**: FastAPI endpoints for production deployment
- **Comprehensive Testing**: Full test suite with unit and integration tests

## 🏗️ Architecture

```
Input Data (Backtesting Results)
         ↓
    Data Pipeline (Polars/cuDF)
         ↓
    Feature Engineering
         ↓
    ┌─────────────────┐    ┌─────────────────┐
    │    LightGBM     │    │     TabNet      │
    │  (Baseline)     │    │  (Non-linear)   │
    └─────────────────┘    └─────────────────┘
         ↓                          ↓
    ┌─────────────────────────────────────────┐
    │         Ensemble Predictions            │
    └─────────────────────────────────────────┘
         ↓
    ┌─────────────────────────────────────────┐
    │      Strategy Ranking System            │
    └─────────────────────────────────────────┘
         ↓
    FastAPI Serving / Batch Predictions
```

## 📊 Target Metrics

The agent predicts the following performance metrics:

1. **ROI** (Return on Investment) - Primary profitability metric
2. **Sharpe Ratio** - Risk-adjusted returns
3. **Expectancy** - Expected value per trade
4. **Max Drawdown** - Maximum peak-to-trough decline
5. **Profit Factor** - Ratio of gross profit to gross loss
6. **Risk-Reward Ratio** - Average win to average loss ratio
7. **Accuracy** - Percentage of winning trades

## 🔧 Installation

### Prerequisites

- Python 3.8+
- CUDA 11.8+ (optional, for GPU acceleration)
- 8GB+ RAM (16GB+ recommended)
- RTX 3060ti or better (for GPU training)

### Install Dependencies

```bash
# Install core requirements
pip install -r requirements_ai_training.txt

# For GPU support (optional)
pip install cudf-cu11 cupy-cuda11x

# For development
pip install pytest black flake8 mypy
```

### Verify Installation

```python
from agents.ai_training_utils import check_dependencies
deps = check_dependencies()
print(deps)
```

## 🚀 Quick Start

### 1. Basic Training

```bash
# Train with default settings
python agents/run_ai_training.py

# Train with custom data
python agents/run_ai_training.py --data-file data/backtest/my_results.parquet

# Fast training (no hyperparameter optimization)
python agents/run_ai_training.py --no-optimize
```

### 2. Python API Usage

```python
import asyncio
from agents.ai_training_agent import AITrainingAgent

async def train_models():
    # Initialize agent
    agent = AITrainingAgent()
    
    # Train models
    results = await agent.train_async(
        file_path="data/backtest/enhanced_strategy_results.parquet",
        optimize_hyperparams=True
    )
    
    # Print results
    print(f"Overall R²: {results['evaluation_metrics']['overall']['r2']:.4f}")
    
    return agent

# Run training
agent = asyncio.run(train_models())
```

### 3. Making Predictions

```python
import numpy as np

# Load trained models
agent.load_models("ai_training_ensemble")

# Create sample features
features = np.array([[
    50,      # n_trades
    2.5,     # avg_holding_period
    0.05,    # capital_at_risk
    0.8,     # liquidity
    0.2,     # volatility
    0,       # market_regime (0=bull, 1=bear, 2=sideways)
    0.3,     # correlation_index
    5.0,     # drawdown_duration
    30,      # winning_trades
    20,      # losing_trades
    150.0,   # avg_win
    -100.0,  # avg_loss
    1500.0,  # total_pnl
    0.02     # position_size_pct
]])

# Make predictions
predictions, confidence = agent.predict(features)
print(f"Predicted ROI: {predictions[0][0]:.4f}")
print(f"Confidence: {confidence[0]:.4f}")

# Rank strategies
rankings = agent.rank_strategies(predictions, ["My_Strategy"], "bull")
print(f"Composite Score: {rankings[0]['composite_score']:.4f}")
```

## 🌐 FastAPI Server

### Start the Server

```bash
# Start API server
python agents/ai_training_api.py

# Or with uvicorn
uvicorn agents.ai_training_api:app --host 0.0.0.0 --port 8000
```

### API Endpoints

#### Health Check
```bash
curl http://localhost:8000/health
```

#### Single Prediction
```bash
curl -X POST "http://localhost:8000/predict" \
  -H "Content-Type: application/json" \
  -d '{
    "n_trades": 50,
    "avg_holding_period": 2.5,
    "capital_at_risk": 0.05,
    "liquidity": 0.8,
    "volatility": 0.2,
    "market_regime": "bull",
    "correlation_index": 0.3,
    "drawdown_duration": 5.0,
    "winning_trades": 30,
    "losing_trades": 20,
    "avg_win": 150.0,
    "avg_loss": -100.0,
    "total_pnl": 1500.0,
    "position_size_pct": 0.02
  }'
```

#### Bulk Predictions
```bash
curl -X POST "http://localhost:8000/predict_bulk" \
  -H "Content-Type: application/json" \
  -d '{
    "strategies": [
      {"strategy_name": "Strategy_1", "n_trades": 50, ...},
      {"strategy_name": "Strategy_2", "n_trades": 75, ...}
    ],
    "market_regime": "bull"
  }'
```

## ⚙️ Configuration

### Configuration File: `config/ai_training_config.yaml`

```yaml
# Data Configuration
data:
  data_dir: "data/backtest"
  input_file: "enhanced_strategy_results.parquet"
  models_dir: "data/models"

# Training Configuration
training:
  test_size: 0.2
  validation_size: 0.2
  ensemble_weights:
    lightgbm: 0.7
    tabnet: 0.3

# LightGBM Configuration
lightgbm:
  num_leaves: 31
  learning_rate: 0.05
  num_boost_round: 1000

# TabNet Configuration
tabnet:
  n_d: 8
  n_a: 8
  n_steps: 3
  max_epochs: 200

# Optuna Configuration
optuna:
  n_trials: 100
  timeout: 3600
```

### Custom Configuration

```python
from agents.ai_training_agent import AITrainingConfig

config = AITrainingConfig(
    data_dir="custom/data/path",
    target_columns=['ROI', 'sharpe_ratio'],
    optuna_trials=50,
    use_gpu=True
)

agent = AITrainingAgent(config)
```

## 🧪 Testing

### Run Tests

```bash
# Run all tests
pytest test/test_ai_training_agent.py -v

# Run specific test
pytest test/test_ai_training_agent.py::test_agent_initialization -v

# Run with coverage
pytest test/test_ai_training_agent.py --cov=agents --cov-report=html
```

### Test Categories

1. **Unit Tests**: Individual component testing
2. **Integration Tests**: End-to-end pipeline testing
3. **Performance Tests**: Speed and memory benchmarks
4. **API Tests**: FastAPI endpoint testing

## 📈 Performance Optimization

### Hardware Recommendations

- **CPU**: 8+ cores (Ryzen 5600x or better)
- **RAM**: 24GB+ for large datasets
- **GPU**: RTX 3060ti+ with 8GB+ VRAM
- **Storage**: SSD for faster data loading

### Optimization Tips

1. **Use GPU acceleration** when available
2. **Tune chunk sizes** based on available memory
3. **Optimize Optuna trials** vs training time trade-off
4. **Use compressed data formats** (Parquet with Brotli)
5. **Enable async processing** for better resource utilization

### Memory Management

```python
# For large datasets
config = AITrainingConfig(
    optuna_trials=50,  # Reduce for faster training
    lgb_params={'num_boost_round': 500},  # Reduce iterations
    tabnet_params={'max_epochs': 100}  # Reduce epochs
)
```

## 🔍 Monitoring and Debugging

### Logging

```python
from agents.ai_training_utils import setup_logging

# Setup detailed logging
setup_logging("DEBUG", "logs/ai_training.log")
```

### Feature Importance Analysis

```python
# Get feature importance
importance = agent.feature_importance
for model, features in importance.items():
    print(f"\n{model} Feature Importance:")
    for feature, score in sorted(features.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {feature}: {score:.4f}")
```

### Model Evaluation

```python
# Detailed evaluation
metrics = agent.evaluate_models(X_test, y_test)
for target, scores in metrics.items():
    print(f"{target}: RMSE={scores['rmse']:.4f}, R²={scores['r2']:.4f}")
```

## 🔄 Integration with Trading Workflow

### 1. Data Flow

```
Backtesting Agent → AI Training Agent → Strategy Evolution Agent
                                    ↓
                              Signal Generation Agent
```

### 2. Automated Retraining

```python
# Schedule periodic retraining
import schedule
import time

def retrain_models():
    agent = AITrainingAgent()
    asyncio.run(agent.train_async())

# Retrain weekly
schedule.every().week.do(retrain_models)

while True:
    schedule.run_pending()
    time.sleep(3600)  # Check hourly
```

### 3. Real-time Strategy Scoring

```python
# In your strategy generation pipeline
def score_new_strategies(strategies):
    predictions, confidence = agent.predict(strategy_features)
    rankings = agent.rank_strategies(predictions, strategy_names)
    
    # Filter top strategies
    top_strategies = [r for r in rankings if r['composite_score'] > 0.5]
    return top_strategies
```

## ❓ FAQ

**Q: Do I need historical data and feature engineering data after training?**

A: For **historical data**: You can remove it after feature engineering to save space. For **feature engineering data**: Keep it if you plan to retrain models or need to analyze feature importance. The trained models don't need the original data for predictions.

**Q: How often should I retrain the models?**

A: Retrain weekly or when you have significant new backtesting data (>1000 new strategy results). Monitor model performance degradation as an indicator.

**Q: Can I use this with different timeframes?**

A: Yes, train separate models for each timeframe or include timeframe as a categorical feature. The agent handles both approaches.

**Q: What if I have limited GPU memory?**

A: Reduce batch sizes, use CPU-only mode, or train models separately. The agent gracefully falls back to CPU if GPU memory is insufficient.

## 🚨 Troubleshooting

### Common Issues

1. **CUDA out of memory**: Reduce batch sizes or use CPU-only mode
2. **Missing dependencies**: Install requirements_ai_training.txt
3. **Data format errors**: Ensure parquet files have required columns
4. **Training slow**: Reduce Optuna trials or use --no-optimize flag
5. **Low model performance**: Check data quality and feature engineering

### Getting Help

- Check logs in `logs/ai_training_agent.log`
- Run tests to verify installation
- Use `--verbose` flag for detailed output
- Monitor system resources during training

## 📝 License

This AI Training Agent is part of the Intraday-AI trading system. See main repository for license details.
