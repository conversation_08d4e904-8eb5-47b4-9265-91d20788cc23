#!/usr/bin/env python3
"""
Test Suite for Market Monitoring Agent
Comprehensive testing for real-time market data tracking and strategy triggering
"""

import os
import sys
import pytest
import asyncio
import json
import yaml
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import polars as pl
import pyarrow as pa

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.market_monitoring_agent import (
    MarketMonitoringAgent,
    MarketTick,
    OHLCV,
    MarketIndicators,
    MarketRegime,
    TradingSignal,
    MarketMonitoringConfig,
    load_config,
    setup_logging,
    get_system_info,
    check_dependencies
)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TEST FIXTURES
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.fixture
def sample_config():
    """Create sample configuration for testing"""
    return MarketMonitoringConfig(
        smartapi_config={
            'api_key': 'test_api_key',
            'username': 'test_user',
            'password': 'test_pass',
            'totp_token': 'test_token',
            'websocket': {
                'reconnect_attempts': 3,
                'reconnect_delay': 1,
                'heartbeat_interval': 10
            }
        },
        market_data_config={
            'symbols': ['RELIANCE', 'TCS', 'INFY'],
            'timeframes': ['1min', '5min'],
            'max_candles_per_timeframe': 100,
            'indicators': {
                'ema_periods': [5, 20],
                'rsi_periods': [14],
                'macd_config': {'fast': 12, 'slow': 26, 'signal': 9}
            }
        },
        environment_config={
            'regime_detection': {
                'lookback_period': 20,
                'volatility_threshold': 0.02,
                'trend_threshold': 0.1
            },
            'volatility': {
                'high_vol_threshold': 75,
                'low_vol_threshold': 25
            }
        },
        strategy_config={
            'ai_model': {
                'enable': False,
                'confidence_threshold': 0.7
            },
            'risk_management': {
                'max_position_size_percent': 1.0,
                'max_daily_trades': 5
            }
        },
        notifications_config={
            'telegram': {
                'enable': False,
                'bot_token': 'test_token',
                'chat_id': 'test_chat'
            }
        },
        logging_config={
            'level': 'INFO',
            'file_logging': {'enable': False}
        },
        storage_config={
            'realtime_data': {'storage_path': 'test_data/realtime'},
            'signals': {'storage_path': 'test_data/signals'}
        },
        performance_config={
            'processing': {'max_workers': 2}
        },
        error_handling_config={
            'retry': {'max_attempts': 2}
        }
    )

@pytest.fixture
def sample_market_tick():
    """Create sample market tick data"""
    return MarketTick(
        symbol='RELIANCE',
        token='2885',
        timestamp=datetime.now(),
        ltp=2500.0,
        volume=1000,
        open_price=2480.0,
        high_price=2520.0,
        low_price=2470.0,
        close_price=2500.0
    )

@pytest.fixture
def sample_ohlcv():
    """Create sample OHLCV data"""
    return OHLCV(
        symbol='RELIANCE',
        timestamp=datetime.now(),
        timeframe='1min',
        open=2480.0,
        high=2520.0,
        low=2470.0,
        close=2500.0,
        volume=1000
    )

@pytest.fixture
def sample_indicators():
    """Create sample indicators"""
    return MarketIndicators(
        symbol='RELIANCE',
        timestamp=datetime.now(),
        ema_5=2505.0,
        ema_20=2490.0,
        rsi_14=65.0,
        macd=5.2,
        macd_signal=4.8,
        atr=25.0,
        vwap=2495.0
    )

@pytest.fixture
def sample_market_regime():
    """Create sample market regime"""
    return MarketRegime(
        regime='bull',
        confidence=0.8,
        volatility_level='medium',
        trend_strength=0.6,
        market_breadth=65.0,
        correlation_level=0.5,
        timestamp=datetime.now()
    )

@pytest.fixture
def mock_agent(sample_config):
    """Create mock agent for testing"""
    with patch('agents.market_monitoring_agent.SmartConnect', None):
        with patch('agents.market_monitoring_agent.SmartWebSocketV2', None):
            agent = MarketMonitoringAgent()
            agent.config = sample_config

            # Mock components
            agent.smartapi_client = Mock()
            agent.websocket_client = Mock()
            agent.telegram_bot = Mock()

            return agent

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 CONFIGURATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

def test_load_config():
    """Test configuration loading"""
    # Test with default config file
    config_path = "config/market_monitoring_config.yaml"
    if os.path.exists(config_path):
        config = load_config(config_path)
        assert isinstance(config, MarketMonitoringConfig)
        assert 'smartapi' in config.smartapi_config or config.smartapi_config == {}

def test_check_dependencies():
    """Test dependency checking"""
    deps = check_dependencies()
    assert isinstance(deps, dict)
    assert 'polars' in deps
    assert 'pandas' in deps
    assert 'numpy' in deps

def test_get_system_info():
    """Test system information gathering"""
    info = get_system_info()
    assert isinstance(info, dict)
    assert 'timestamp' in info

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 DATA STRUCTURE TESTS
# ═══════════════════════════════════════════════════════════════════════════════

def test_market_tick_creation(sample_market_tick):
    """Test MarketTick data structure"""
    tick = sample_market_tick
    assert tick.symbol == 'RELIANCE'
    assert tick.ltp == 2500.0
    assert isinstance(tick.timestamp, datetime)

def test_ohlcv_creation(sample_ohlcv):
    """Test OHLCV data structure"""
    candle = sample_ohlcv
    assert candle.symbol == 'RELIANCE'
    assert candle.open == 2480.0
    assert candle.high == 2520.0
    assert candle.low == 2470.0
    assert candle.close == 2500.0

def test_market_indicators_creation(sample_indicators):
    """Test MarketIndicators data structure"""
    indicators = sample_indicators
    assert indicators.symbol == 'RELIANCE'
    assert indicators.ema_5 == 2505.0
    assert indicators.rsi_14 == 65.0

def test_market_regime_creation(sample_market_regime):
    """Test MarketRegime data structure"""
    regime = sample_market_regime
    assert regime.regime == 'bull'
    assert regime.confidence == 0.8
    assert regime.volatility_level == 'medium'

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 AGENT INITIALIZATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_agent_initialization():
    """Test agent initialization"""
    with patch('agents.market_monitoring_agent.load_config') as mock_load_config:
        with patch('agents.market_monitoring_agent.setup_logging'):
            mock_load_config.return_value = Mock()
            
            agent = MarketMonitoringAgent()
            assert agent.is_running == False
            assert agent.is_connected == False
            assert len(agent.active_signals) == 0

@pytest.mark.asyncio
async def test_agent_setup(mock_agent):
    """Test agent setup process"""
    agent = mock_agent
    
    # Mock setup methods
    agent._setup_smartapi = AsyncMock()
    agent._setup_ai_agent = AsyncMock()
    agent._setup_notifications = AsyncMock()
    agent._create_storage_directories = Mock()
    agent._load_symbols = AsyncMock()
    
    await agent.setup()
    
    agent._setup_smartapi.assert_called_once()
    agent._setup_ai_agent.assert_called_once()
    agent._setup_notifications.assert_called_once()

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 DATA PROCESSING TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_process_market_tick(mock_agent, sample_market_tick):
    """Test market tick processing"""
    agent = mock_agent
    
    # Mock methods
    agent._parse_websocket_message = Mock(return_value={
        'symbol': 'RELIANCE',
        'token': '2885',
        'ltp': 2500.0,
        'volume': 1000
    })
    agent._update_ohlc_data = AsyncMock()
    agent._calculate_indicators = AsyncMock()
    agent._detect_regime_changes = AsyncMock()
    agent._check_trading_signals = AsyncMock()
    
    await agent._process_market_tick({'test': 'data'})
    
    agent._update_ohlc_data.assert_called_once()
    agent._calculate_indicators.assert_called_once()

@pytest.mark.asyncio
async def test_update_ohlc_data(mock_agent, sample_market_tick):
    """Test OHLC data update"""
    agent = mock_agent
    tick = sample_market_tick
    
    # Mock candle creation
    agent._get_or_create_candle = Mock(return_value=OHLCV(
        symbol='RELIANCE',
        timestamp=datetime.now(),
        timeframe='1min',
        open=2480.0,
        high=2480.0,
        low=2480.0,
        close=2480.0,
        volume=0
    ))
    agent._store_candle = Mock()
    
    await agent._update_ohlc_data(tick)
    
    agent._store_candle.assert_called()

def test_get_candle_timestamp(mock_agent):
    """Test candle timestamp calculation"""
    agent = mock_agent
    
    timestamp = datetime(2024, 1, 1, 10, 23, 45)
    
    # Test 1min timeframe
    candle_ts = agent._get_candle_timestamp(timestamp, '1min')
    assert candle_ts == datetime(2024, 1, 1, 10, 23, 0)
    
    # Test 5min timeframe
    candle_ts = agent._get_candle_timestamp(timestamp, '5min')
    assert candle_ts == datetime(2024, 1, 1, 10, 20, 0)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 INDICATOR CALCULATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_calculate_indicators(mock_agent):
    """Test indicator calculation with Polars"""
    agent = mock_agent

    # Create sample candle data (need 100+ for indicators)
    candles = []
    for i in range(150):
        candle = OHLCV(
            symbol='RELIANCE',
            timestamp=datetime.now() - timedelta(minutes=150-i),
            timeframe='1min',
            open=2500 + i,
            high=2510 + i,
            low=2490 + i,
            close=2500 + i,
            volume=1000
        )
        candles.append(candle)

    agent.market_data['RELIANCE']['1min'] = candles

    # Mock polars-talib if not available
    with patch('agents.market_monitoring_agent.POLARS_TALIB_AVAILABLE', False):
        await agent._calculate_indicators('RELIANCE')

        assert 'RELIANCE' in agent.indicators
        indicators = agent.indicators['RELIANCE']
        assert hasattr(indicators, 'ema_5')
        assert hasattr(indicators, 'rsi_14')
        assert indicators.ema_5 is not None
        assert indicators.rsi_14 is not None
        assert indicators.vwap is not None

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 REGIME DETECTION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_detect_regime_changes(mock_agent):
    """Test market regime detection"""
    agent = mock_agent
    
    # Mock regime detection methods
    agent._calculate_market_breadth = AsyncMock(return_value={'above_ema20_percent': 70.0})
    agent._calculate_market_volatility = AsyncMock(return_value={'volatility_percentile': 50.0})
    agent._notify_regime_change = AsyncMock()
    
    await agent._detect_regime_changes()
    
    assert agent.market_regime is not None
    assert agent.market_regime.regime in ['bull', 'bear', 'sideways']

def test_determine_market_regime(mock_agent):
    """Test market regime determination logic"""
    agent = mock_agent
    
    # Test bull market
    breadth_data = {'above_ema20_percent': 75.0}
    volatility_data = {'volatility_percentile': 50.0}
    
    regime = agent._determine_market_regime(breadth_data, volatility_data)
    assert regime.regime == 'bull'
    assert regime.confidence > 0
    
    # Test bear market
    breadth_data = {'above_ema20_percent': 25.0}
    regime = agent._determine_market_regime(breadth_data, volatility_data)
    assert regime.regime == 'bear'

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 SIGNAL GENERATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_check_entry_conditions(mock_agent, sample_indicators):
    """Test entry condition checking"""
    agent = mock_agent
    
    # Test RSI oversold condition
    indicators = sample_indicators
    indicators.rsi_14 = 25.0  # Oversold
    
    candle = OHLCV(
        symbol='RELIANCE',
        timestamp=datetime.now(),
        timeframe='1min',
        open=2480.0,
        high=2520.0,
        low=2470.0,
        close=2500.0,
        volume=1000
    )
    
    signals = await agent._check_entry_conditions('RELIANCE', indicators, candle)
    
    # Should generate buy signal for oversold RSI
    buy_signals = [s for s in signals if s['action'] == 'BUY']
    assert len(buy_signals) > 0

@pytest.mark.asyncio
async def test_create_trading_signal(mock_agent, sample_indicators):
    """Test trading signal creation"""
    agent = mock_agent
    
    # Setup market data
    candle = OHLCV(
        symbol='RELIANCE',
        timestamp=datetime.now(),
        timeframe='1min',
        open=2480.0,
        high=2520.0,
        low=2470.0,
        close=2500.0,
        volume=1000
    )
    agent.market_data['RELIANCE']['1min'] = [candle]
    agent.indicators['RELIANCE'] = sample_indicators
    agent.market_regime = MarketRegime(
        regime='bull',
        confidence=0.8,
        volatility_level='medium',
        trend_strength=0.6,
        market_breadth=65.0,
        correlation_level=0.5,
        timestamp=datetime.now()
    )
    
    signal_data = {
        'strategy': 'test_strategy',
        'action': 'BUY',
        'confidence': 0.8
    }
    
    signal = await agent._create_trading_signal('RELIANCE', signal_data, None)
    
    assert signal is not None
    assert signal.symbol == 'RELIANCE'
    assert signal.action == 'BUY'
    assert signal.confidence == 0.8

@pytest.mark.asyncio
async def test_validate_signal(mock_agent):
    """Test signal validation"""
    agent = mock_agent
    
    signal = TradingSignal(
        symbol='RELIANCE',
        strategy='test_strategy',
        action='BUY',
        price=2500.0,
        target=2550.0,
        stop_loss=2450.0,
        quantity=10,
        confidence=0.8,
        market_regime='bull',
        timestamp=datetime.now(),
        context={}
    )
    
    is_valid = await agent._validate_signal(signal)
    assert is_valid == True
    
    # Test low confidence signal
    signal.confidence = 0.5
    is_valid = await agent._validate_signal(signal)
    assert is_valid == False

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 NOTIFICATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_send_telegram_notification(mock_agent):
    """Test Telegram notification sending"""
    agent = mock_agent
    agent.telegram_bot = AsyncMock()
    
    # Update config for testing
    agent.config.notifications_config['telegram']['chat_id'] = 'test_chat_id'
    
    signal = TradingSignal(
        symbol='RELIANCE',
        strategy='test_strategy',
        action='BUY',
        price=2500.0,
        target=2550.0,
        stop_loss=2450.0,
        quantity=10,
        confidence=0.8,
        market_regime='bull',
        timestamp=datetime.now(),
        context={}
    )
    
    await agent._send_telegram_notification(signal)
    
    agent.telegram_bot.send_message.assert_called_once()

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 INTEGRATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_full_signal_processing_flow(mock_agent):
    """Test complete signal processing flow"""
    agent = mock_agent
    
    # Setup test data
    agent.indicators['RELIANCE'] = MarketIndicators(
        symbol='RELIANCE',
        timestamp=datetime.now(),
        rsi_14=25.0,  # Oversold
        ema_5=2505.0,
        ema_20=2490.0,
        atr=25.0
    )
    
    candle = OHLCV(
        symbol='RELIANCE',
        timestamp=datetime.now(),
        timeframe='1min',
        open=2480.0,
        high=2520.0,
        low=2470.0,
        close=2500.0,
        volume=1000
    )
    agent.market_data['RELIANCE']['1min'] = [candle]
    
    agent.market_regime = MarketRegime(
        regime='bull',
        confidence=0.8,
        volatility_level='medium',
        trend_strength=0.6,
        market_breadth=65.0,
        correlation_level=0.5,
        timestamp=datetime.now()
    )
    
    # Mock notification methods
    agent._send_signal_notification = AsyncMock()
    agent._log_signal = AsyncMock()
    
    # Process signals
    await agent._check_trading_signals('RELIANCE')
    
    # Should have generated signals
    assert len(agent.active_signals) > 0

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
