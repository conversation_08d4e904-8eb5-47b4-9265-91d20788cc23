# Async/Concurrent Backtesting with cuDF and cuPy

This enhanced backtesting agent implements asynchronous and concurrent processing to maximize GPU utilization for processing multiple strategies simultaneously.

## 🚀 Key Features

### 1. **Concurrent Strategy Processing**
- Process 5-10 strategies simultaneously using asyncio
- ThreadPoolExecutor for CPU-bound tasks
- Batch processing to manage GPU memory

### 2. **GPU Stream Management**
- Multiple CUDA streams for parallel GPU operations
- Automatic stream allocation and synchronization
- Better GPU utilization through concurrent kernels

### 3. **Dask-cuDF Integration**
- Distributed GPU processing for large datasets
- Automatic partitioning and parallel execution
- Fallback to async processing if <PERSON><PERSON> fails

### 4. **Performance Monitoring**
- Real-time GPU utilization tracking
- Memory usage monitoring
- Async task performance metrics

## 📦 Installation

### 1. Install Dependencies
```bash
python install_async_dependencies.py
```

### 2. Manual Installation (if needed)
```bash
# Core async dependencies
pip install nest-asyncio asyncio-throttle

# GPU distributed processing (if GPU available)
pip install dask-cudf dask-cuda ucx-py

# Performance monitoring
pip install pynvml psutil

# Optional performance improvements
pip install uvloop aiodns  # Linux/macOS only
```

## ⚙️ Configuration

### 1. Edit `config/async_config.yaml`
```yaml
gpu:
  concurrent_strategies: 5    # Adjust based on GPU memory
  cuda_streams: 5            # Match concurrent_strategies
  chunk_size: 200000         # Adjust based on VRAM

async:
  max_workers: 8             # 2-4x CPU cores
  strategy_batch_size: 5     # Strategies per batch
  batch_delay: 0.1           # Seconds between batches
```

### 2. Adjust Constants in `backtesting_agent.py`
```python
CONCURRENT_STRATEGIES = 5   # Number of strategies to process concurrently
MAX_WORKERS = 8            # Maximum concurrent workers
CHUNKSIZE = 200_000        # Chunk size for processing
```

## 🔧 Hardware Optimization

### GPU Memory Guidelines
- **6GB VRAM**: `CONCURRENT_STRATEGIES = 3-4`
- **8GB VRAM**: `CONCURRENT_STRATEGIES = 5-6`
- **12GB+ VRAM**: `CONCURRENT_STRATEGIES = 8-10`

### CPU Guidelines
- **4 cores**: `MAX_WORKERS = 8`
- **8 cores**: `MAX_WORKERS = 16`
- **16+ cores**: `MAX_WORKERS = 32`

## 🚀 Usage

### 1. Basic Usage (same as before)
```bash
python backtesting_agent.py
```

### 2. With Performance Monitoring
```python
from performance_monitor import PerformanceMonitor

monitor = PerformanceMonitor(log_interval=5)
monitor.start_monitoring()

# Run your backtesting
# ...

monitor.stop_monitoring()
```

### 3. Monitor GPU Utilization
```bash
# In another terminal
watch -n 1 nvidia-smi
```

## 📊 Performance Improvements

### Expected Speedup
- **2-3x faster** with async processing (5 concurrent strategies)
- **3-5x faster** with Dask-cuDF (distributed processing)
- **Better GPU utilization** (60-90% vs 20-40% before)

### Memory Efficiency
- Batch processing prevents memory overflow
- Stream synchronization ensures proper cleanup
- Automatic fallback for memory-constrained systems

## 🔍 Monitoring and Debugging

### 1. Performance Logs
```
🚀 Starting async strategy 1/50: RSI_EMA_Strategy
✅ R:R 1:2, PosSize 5% - 15 results
📦 Processing strategy batch 1/10 (5 strategies)
💾 Written 75 results for RELIANCE - RSI_EMA_Strategy
```

### 2. GPU Utilization Logs
```
CPU: 45.2% | RAM: 8.3GB (52.1%) | Threads: 12
GPU: 78.5% | VRAM: 4.2/8.0GB | Temp: 65°C
```

### 3. Performance Metrics File
- Saved to `data/performance_metrics.csv`
- Includes CPU, GPU, memory usage over time
- Use for optimization and troubleshooting

## 🛠️ Troubleshooting

### Common Issues

#### 1. Out of GPU Memory
```
Solution: Reduce CONCURRENT_STRATEGIES or CHUNKSIZE
```

#### 2. Low GPU Utilization
```
Solution: Increase CONCURRENT_STRATEGIES or use Dask-cuDF
```

#### 3. Async Tasks Hanging
```
Solution: Check MAX_WORKERS setting, ensure proper error handling
```

#### 4. Dask-cuDF Errors
```
Solution: Falls back to async processing automatically
Check CUDA and Dask installation
```

### Performance Tuning

#### 1. Find Optimal Settings
```python
# Test different configurations
for concurrent in [3, 5, 8, 10]:
    for workers in [8, 16, 24]:
        # Run small test and measure performance
        # Choose settings with highest GPU utilization
```

#### 2. Monitor Resource Usage
```bash
# GPU monitoring
nvidia-smi -l 1

# System monitoring  
htop

# Memory monitoring
free -h
```

## 🔬 Technical Details

### 1. Async Architecture
- **Event Loop**: Single event loop for coordination
- **ThreadPoolExecutor**: CPU-bound strategy calculations
- **Batch Processing**: Memory-efficient strategy grouping

### 2. GPU Stream Management
- **Round-robin allocation**: Distribute work across streams
- **Automatic synchronization**: Prevent race conditions
- **Memory cleanup**: Proper resource management

### 3. Dask Integration
- **Lazy evaluation**: Build computation graph first
- **Automatic partitioning**: Split data across GPU cores
- **Fault tolerance**: Automatic retry and fallback

## 📈 Expected Results

With proper configuration, you should see:
- **GPU utilization**: 70-90% (vs 20-40% before)
- **Processing speed**: 3-5x faster
- **Memory efficiency**: Better resource utilization
- **Scalability**: Easy to add more strategies

## 🎯 Next Steps

1. Run the installation script
2. Adjust configuration based on your hardware
3. Monitor performance during initial runs
4. Fine-tune settings for optimal GPU utilization
5. Scale up to more strategies as needed

The system is designed to automatically detect and utilize available hardware efficiently while providing fallbacks for different configurations.
