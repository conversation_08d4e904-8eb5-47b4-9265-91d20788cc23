"""
Tests for Feature 5: Scenario & Regime-Based Testing
"""
import pytest
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock

from agents.enhanced_backtesting_polars import (
    MarketRegimeAnalyzer, ScenarioTester, MarketRegime
)


class TestMarketRegimeAnalyzer:
    """Test suite for MarketRegimeAnalyzer class"""
    
    @pytest.fixture
    def analyzer(self):
        """Create MarketRegimeAnalyzer instance"""
        return MarketRegimeAnalyzer()
    
    @pytest.mark.asyncio
    async def test_initialization(self, analyzer):
        """Test proper initialization of MarketRegimeAnalyzer"""
        assert analyzer.regime_history == []
        assert analyzer.regime_thresholds == {
            'bull_threshold': 0.15,
            'bear_threshold': -0.15,
            'high_vol_threshold': 0.25,
            'low_vol_threshold': 0.10
        }
    
    @pytest.mark.asyncio
    async def test_detect_market_regime(self, analyzer, sample_market_data):
        """Test market regime detection"""
        regime = await analyzer.detect_market_regime(sample_market_data)
        
        assert 'primary_regime' in regime
        assert 'volatility_regime' in regime
        assert 'confidence_score' in regime
        assert 'regime_duration' in regime
        
        # Check that regime is one of the expected values
        valid_regimes = [MarketRegime.BULL, MarketRegime.BEAR, MarketRegime.SIDEWAYS]
        assert regime['primary_regime'] in valid_regimes
        
        valid_vol_regimes = [MarketRegime.HIGH_VOLATILITY, MarketRegime.LOW_VOLATILITY]
        assert regime['volatility_regime'] in valid_vol_regimes
        
        assert 0 <= regime['confidence_score'] <= 1
    
    @pytest.mark.asyncio
    async def test_calculate_trend_strength(self, analyzer, sample_market_data):
        """Test trend strength calculation"""
        trend_strength = await analyzer._calculate_trend_strength(sample_market_data)
        
        assert isinstance(trend_strength, float)
        assert -1 <= trend_strength <= 1  # Should be normalized between -1 and 1
    
    @pytest.mark.asyncio
    async def test_calculate_volatility_regime(self, analyzer, sample_market_data):
        """Test volatility regime calculation"""
        vol_regime = await analyzer._calculate_volatility_regime(sample_market_data)
        
        assert vol_regime in [MarketRegime.HIGH_VOLATILITY, MarketRegime.LOW_VOLATILITY]
    
    @pytest.mark.asyncio
    async def test_regime_transition_detection(self, analyzer):
        """Test regime transition detection"""
        # Create sample regime history
        analyzer.regime_history = [
            {'timestamp': datetime(2023, 1, 1), 'regime': MarketRegime.BULL},
            {'timestamp': datetime(2023, 1, 15), 'regime': MarketRegime.BULL},
            {'timestamp': datetime(2023, 2, 1), 'regime': MarketRegime.SIDEWAYS},
            {'timestamp': datetime(2023, 2, 15), 'regime': MarketRegime.BEAR}
        ]
        
        transitions = await analyzer.detect_regime_transitions()
        
        assert len(transitions) >= 2  # Should detect at least 2 transitions
        assert all('from_regime' in t and 'to_regime' in t for t in transitions)
        assert all('transition_date' in t for t in transitions)
    
    @pytest.mark.asyncio
    async def test_regime_persistence_analysis(self, analyzer):
        """Test regime persistence analysis"""
        # Sample regime data
        regime_data = [
            MarketRegime.BULL, MarketRegime.BULL, MarketRegime.BULL,
            MarketRegime.SIDEWAYS, MarketRegime.SIDEWAYS,
            MarketRegime.BEAR, MarketRegime.BEAR, MarketRegime.BEAR, MarketRegime.BEAR
        ]
        
        persistence = await analyzer._analyze_regime_persistence(regime_data)
        
        assert 'average_duration' in persistence
        assert 'regime_stability' in persistence
        assert persistence['average_duration'] > 0
        assert 0 <= persistence['regime_stability'] <= 1


class TestScenarioTester:
    """Test suite for ScenarioTester class"""
    
    @pytest.fixture
    def tester(self):
        """Create ScenarioTester instance"""
        return ScenarioTester()
    
    @pytest.mark.asyncio
    async def test_initialization(self, tester):
        """Test proper initialization of ScenarioTester"""
        assert tester.scenario_results == {}
        assert tester.stress_test_results == {}
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_run_scenario_analysis(self, mock_simulate, tester, sample_strategy, 
                                       sample_market_data, sample_backtest_config, sample_trades):
        """Test scenario analysis execution"""
        mock_simulate.return_value = sample_trades
        
        scenarios = ['market_crash', 'bull_market', 'high_volatility']
        
        results = await tester.run_scenario_analysis(
            sample_strategy, sample_market_data, sample_backtest_config, scenarios
        )
        
        assert 'scenario_results' in results
        assert 'summary_statistics' in results
        assert len(results['scenario_results']) == len(scenarios)
        
        for scenario in scenarios:
            assert scenario in results['scenario_results']
            assert 'performance_metrics' in results['scenario_results'][scenario]
    
    @pytest.mark.asyncio
    async def test_generate_market_crash_scenario(self, tester, sample_market_data):
        """Test market crash scenario generation"""
        crash_data = await tester._generate_market_crash_scenario(
            sample_market_data, crash_magnitude=0.3, crash_duration=10
        )
        
        assert len(crash_data) == len(sample_market_data)
        assert 'close' in crash_data.columns
        
        # Check that prices dropped during crash period
        original_prices = sample_market_data['close'].to_list()
        crash_prices = crash_data['close'].to_list()
        
        # At least some prices should be lower
        lower_prices = sum(1 for i in range(len(original_prices)) 
                          if crash_prices[i] < original_prices[i])
        assert lower_prices > 0
    
    @pytest.mark.asyncio
    async def test_generate_flash_crash_scenario(self, tester, sample_market_data):
        """Test flash crash scenario generation"""
        flash_crash_data = await tester._generate_flash_crash_scenario(
            sample_market_data, crash_magnitude=0.15, recovery_periods=5
        )
        
        assert len(flash_crash_data) == len(sample_market_data)
        assert 'close' in flash_crash_data.columns
    
    @pytest.mark.asyncio
    async def test_generate_bull_market_scenario(self, tester, sample_market_data):
        """Test bull market scenario generation"""
        bull_data = await tester._generate_bull_market_scenario(
            sample_market_data, trend_strength=0.2, duration=30
        )
        
        assert len(bull_data) == len(sample_market_data)
        assert 'close' in bull_data.columns
        
        # Check for upward trend
        bull_prices = bull_data['close'].to_list()
        if len(bull_prices) > 10:
            early_avg = np.mean(bull_prices[:10])
            late_avg = np.mean(bull_prices[-10:])
            assert late_avg > early_avg  # Prices should trend upward
    
    @pytest.mark.asyncio
    async def test_generate_high_volatility_scenario(self, tester, sample_market_data):
        """Test high volatility scenario generation"""
        high_vol_data = await tester._generate_high_volatility_scenario(
            sample_market_data, volatility_multiplier=2.0
        )
        
        assert len(high_vol_data) == len(sample_market_data)
        assert 'close' in high_vol_data.columns
        
        # Calculate volatility of both datasets
        original_returns = sample_market_data['close'].pct_change().drop_nulls()
        high_vol_returns = high_vol_data['close'].pct_change().drop_nulls()
        
        if len(original_returns) > 1 and len(high_vol_returns) > 1:
            original_vol = original_returns.std()
            high_vol_vol = high_vol_returns.std()
            assert high_vol_vol > original_vol  # Should have higher volatility
    
    @pytest.mark.asyncio
    async def test_generate_low_volatility_scenario(self, tester, sample_market_data):
        """Test low volatility scenario generation"""
        low_vol_data = await tester._generate_low_volatility_scenario(
            sample_market_data, volatility_reduction=0.5
        )
        
        assert len(low_vol_data) == len(sample_market_data)
        assert 'close' in low_vol_data.columns
    
    @pytest.mark.asyncio
    @patch('agents.enhanced_backtesting_polars.simulate_trades_vectorized')
    async def test_stress_testing(self, mock_simulate, tester, sample_strategy,
                                sample_market_data, sample_backtest_config, sample_trades):
        """Test stress testing functionality"""
        mock_simulate.return_value = sample_trades
        
        stress_scenarios = {
            'extreme_crash': {'type': 'market_crash', 'magnitude': 0.5},
            'prolonged_bear': {'type': 'bear_market', 'duration': 60},
            'extreme_volatility': {'type': 'high_volatility', 'multiplier': 3.0}
        }
        
        stress_results = await tester.run_stress_tests(
            sample_strategy, sample_market_data, sample_backtest_config, stress_scenarios
        )
        
        assert 'stress_test_results' in stress_results
        assert 'risk_assessment' in stress_results
        assert len(stress_results['stress_test_results']) == len(stress_scenarios)
    
    @pytest.mark.asyncio
    async def test_scenario_comparison(self, tester):
        """Test scenario comparison functionality"""
        # Sample scenario results
        scenario_results = {
            'normal': {'total_return': 15.0, 'max_drawdown': 0.08, 'sharpe_ratio': 1.2},
            'crash': {'total_return': -5.0, 'max_drawdown': 0.25, 'sharpe_ratio': -0.3},
            'bull': {'total_return': 25.0, 'max_drawdown': 0.05, 'sharpe_ratio': 1.8}
        }
        
        comparison = await tester._compare_scenario_performance(scenario_results)
        
        assert 'best_scenario' in comparison
        assert 'worst_scenario' in comparison
        assert 'performance_range' in comparison
        assert 'risk_range' in comparison
    
    @pytest.mark.asyncio
    async def test_regime_specific_testing(self, tester, sample_strategy, sample_market_data):
        """Test regime-specific performance testing"""
        regime_results = await tester.test_regime_specific_performance(
            sample_strategy, sample_market_data
        )
        
        assert 'bull_regime_performance' in regime_results
        assert 'bear_regime_performance' in regime_results
        assert 'sideways_regime_performance' in regime_results
        assert 'regime_adaptability_score' in regime_results
    
    @pytest.mark.asyncio
    async def test_monte_carlo_scenario_testing(self, tester, sample_strategy, sample_market_data):
        """Test Monte Carlo scenario testing"""
        mc_results = await tester.run_monte_carlo_scenarios(
            sample_strategy, sample_market_data, num_simulations=10
        )
        
        assert 'simulation_results' in mc_results
        assert 'confidence_intervals' in mc_results
        assert 'worst_case_scenario' in mc_results
        assert 'best_case_scenario' in mc_results
        assert len(mc_results['simulation_results']) == 10
    
    @pytest.mark.asyncio
    async def test_tail_risk_analysis(self, tester):
        """Test tail risk analysis"""
        # Sample returns from different scenarios
        scenario_returns = {
            'normal': [0.01, 0.02, -0.01, 0.015, 0.005],
            'stress': [-0.05, -0.08, -0.03, -0.12, -0.02],
            'extreme': [-0.15, -0.20, -0.10, -0.25, -0.08]
        }
        
        tail_risk = await tester._analyze_tail_risk(scenario_returns)
        
        assert 'var_99' in tail_risk
        assert 'cvar_99' in tail_risk
        assert 'tail_ratio' in tail_risk
        assert 'extreme_loss_probability' in tail_risk
    
    @pytest.mark.asyncio
    async def test_scenario_sensitivity_analysis(self, tester, sample_strategy, sample_market_data):
        """Test scenario sensitivity analysis"""
        sensitivity_params = {
            'crash_magnitude': [0.1, 0.2, 0.3, 0.4, 0.5],
            'volatility_multiplier': [1.5, 2.0, 2.5, 3.0],
            'trend_strength': [0.1, 0.15, 0.2, 0.25]
        }
        
        sensitivity_results = await tester.run_sensitivity_analysis(
            sample_strategy, sample_market_data, sensitivity_params
        )
        
        assert 'parameter_sensitivity' in sensitivity_results
        assert 'most_sensitive_parameter' in sensitivity_results
        assert 'sensitivity_scores' in sensitivity_results
