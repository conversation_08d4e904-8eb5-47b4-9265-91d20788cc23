#!/usr/bin/env python3
"""
Debug Signal Generation
- Check if signals are being generated correctly
- Test individual strategy expressions
"""

import polars as pl
import yaml
from pathlib import Path

def test_signal_generation():
    """Test signal generation for simple strategies"""
    
    print("🔍 Debug Signal Generation")
    print("=" * 40)
    
    # Load data
    data_file = "data/features/features_360ONE_1min.parquet"
    if not Path(data_file).exists():
        print(f"❌ Data file not found: {data_file}")
        return
    
    df = pl.read_parquet(data_file)
    print(f"📊 Data loaded: {df.shape[0]} rows, {df.shape[1]} columns")
    
    # Check required columns
    required_cols = ['rsi_14', 'close', 'ema_10', 'ema_20', 'macd', 'macd_signal', 'ema_5']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        print(f"❌ Missing columns: {missing_cols}")
        return
    else:
        print("✅ All required columns present")
    
    # Test simple expressions
    test_expressions = [
        ("RSI < 30", "rsi_14 < 30"),
        ("RSI > 70", "rsi_14 > 70"),
        ("Close > EMA10", "close > ema_10"),
        ("Close < EMA10", "close < ema_10"),
        ("MACD > Signal", "macd > macd_signal"),
        ("MACD < Signal", "macd < macd_signal"),
        ("EMA5 > EMA20", "ema_5 > ema_20"),
        ("EMA5 < EMA20", "ema_5 < ema_20")
    ]
    
    print("\n🧪 Testing Individual Expressions:")
    print("-" * 40)
    
    for name, expr in test_expressions:
        try:
            # Convert expression to Polars
            polars_expr = expr.replace('rsi_14', 'pl.col("rsi_14")') \
                             .replace('close', 'pl.col("close")') \
                             .replace('ema_10', 'pl.col("ema_10")') \
                             .replace('ema_20', 'pl.col("ema_20")') \
                             .replace('macd_signal', 'pl.col("macd_signal")') \
                             .replace('macd', 'pl.col("macd")') \
                             .replace('ema_5', 'pl.col("ema_5")')
            
            # Evaluate expression
            result = df.select(eval(polars_expr).alias("signal"))
            signal_count = result.select(pl.col("signal").sum()).item()
            total_rows = len(result)
            percentage = (signal_count / total_rows) * 100 if total_rows > 0 else 0
            
            print(f"  {name:<20}: {signal_count:>6} signals ({percentage:>5.1f}%)")
            
        except Exception as e:
            print(f"  {name:<20}: ❌ Error - {e}")
    
    # Test combined strategies
    print("\n🎯 Testing Combined Strategies:")
    print("-" * 40)
    
    strategies = [
        ("Simple RSI Long", "rsi_14 < 30 and close > ema_10"),
        ("Simple RSI Short", "rsi_14 > 70 and close < ema_10"),
        ("Simple MACD Long", "macd > macd_signal and close > ema_20"),
        ("Simple MACD Short", "macd < macd_signal and close < ema_20"),
        ("Simple EMA Long", "ema_5 > ema_20 and close > ema_5"),
        ("Simple EMA Short", "ema_5 < ema_20 and close < ema_5")
    ]
    
    for name, expr in strategies:
        try:
            # Convert to Polars expression
            polars_expr = expr.replace('rsi_14', 'pl.col("rsi_14")') \
                             .replace('close', 'pl.col("close")') \
                             .replace('ema_10', 'pl.col("ema_10")') \
                             .replace('ema_20', 'pl.col("ema_20")') \
                             .replace('macd_signal', 'pl.col("macd_signal")') \
                             .replace('macd', 'pl.col("macd")') \
                             .replace('ema_5', 'pl.col("ema_5")') \
                             .replace(' and ', ' & ')
            
            result = df.select(eval(polars_expr).alias("signal"))
            signal_count = result.select(pl.col("signal").sum()).item()
            total_rows = len(result)
            percentage = (signal_count / total_rows) * 100 if total_rows > 0 else 0
            
            print(f"  {name:<20}: {signal_count:>6} signals ({percentage:>5.1f}%)")
            
        except Exception as e:
            print(f"  {name:<20}: ❌ Error - {e}")
    
    # Check data quality
    print("\n📈 Data Quality Check:")
    print("-" * 40)
    
    # Check for null values
    null_counts = df.select([
        pl.col("rsi_14").null_count().alias("rsi_14_nulls"),
        pl.col("close").null_count().alias("close_nulls"),
        pl.col("ema_10").null_count().alias("ema_10_nulls"),
        pl.col("ema_20").null_count().alias("ema_20_nulls"),
        pl.col("macd").null_count().alias("macd_nulls"),
        pl.col("macd_signal").null_count().alias("macd_signal_nulls"),
        pl.col("ema_5").null_count().alias("ema_5_nulls")
    ]).to_pandas().iloc[0]
    
    for col, null_count in null_counts.items():
        col_name = col.replace('_nulls', '')
        print(f"  {col_name:<15}: {null_count:>6} nulls")
    
    # Show sample data
    print("\n📋 Sample Data (first 10 rows):")
    print("-" * 40)
    sample_cols = ['datetime', 'close', 'rsi_14', 'ema_10', 'ema_20', 'macd', 'macd_signal', 'ema_5']
    available_cols = [col for col in sample_cols if col in df.columns]
    print(df.select(available_cols).head(10))

if __name__ == "__main__":
    test_signal_generation()