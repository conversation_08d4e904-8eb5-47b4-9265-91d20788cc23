#!/usr/bin/env python3
"""
Test script to verify the WebSocket hanging issue is fixed
"""

import os
import sys
import asyncio
import logging
import signal
from datetime import datetime

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_market_monitoring_agent():
    """Test the market monitoring agent to ensure it doesn't hang"""
    try:
        logger.info("🧪 Starting WebSocket hanging fix test...")
        
        # Set environment variables for test mode
        os.environ['DEMO_MODE'] = 'true'
        os.environ['WORKFLOW_MODE'] = 'true'
        
        # Import the runner
        from agents.run_market_monitoring import MarketMonitoringRunner
        
        # Create runner with test timeout
        runner = MarketMonitoringRunner("config/market_monitoring_config.yaml")
        
        # Set up a timeout to prevent hanging
        timeout_duration = 60  # 60 seconds max for test
        
        logger.info(f"⏱️  Running test with {timeout_duration}s timeout...")
        
        try:
            # Run the agent with timeout
            await asyncio.wait_for(runner.run(), timeout=timeout_duration)
            logger.info("✅ Test completed successfully - no hanging detected!")
            return True
            
        except asyncio.TimeoutError:
            logger.error(f"❌ Test failed - agent hung for more than {timeout_duration}s")
            return False
            
        except Exception as e:
            logger.error(f"❌ Test failed with error: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test setup failed: {e}")
        return False

async def test_with_signal_handling():
    """Test with proper signal handling"""
    
    # Set up signal handler for graceful shutdown
    shutdown_event = asyncio.Event()
    
    def signal_handler(signum, frame):
        logger.info(f"📡 Received signal {signum}, shutting down test...")
        shutdown_event.set()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Run test with signal handling
    test_task = asyncio.create_task(test_market_monitoring_agent())
    shutdown_task = asyncio.create_task(shutdown_event.wait())
    
    done, pending = await asyncio.wait(
        [test_task, shutdown_task],
        return_when=asyncio.FIRST_COMPLETED
    )
    
    # Cancel pending tasks
    for task in pending:
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass
    
    # Check results
    if test_task in done and not test_task.cancelled():
        result = await test_task
        return result
    else:
        logger.info("🛑 Test interrupted by signal")
        return True  # Graceful shutdown is also a success

def main():
    """Main test entry point"""
    logger.info("🚀 WebSocket Hanging Fix Test")
    logger.info("=" * 50)
    
    try:
        # Run the test
        result = asyncio.run(test_with_signal_handling())
        
        if result:
            logger.info("🎉 TEST PASSED: WebSocket hanging issue appears to be fixed!")
            logger.info("✅ The agent started and ran without hanging")
            sys.exit(0)
        else:
            logger.error("💥 TEST FAILED: WebSocket hanging issue still exists")
            logger.error("❌ The agent hung during execution")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"💥 Test failed with unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
