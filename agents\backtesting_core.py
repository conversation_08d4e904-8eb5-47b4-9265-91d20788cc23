#!/usr/bin/env python3
"""
Enhanced Backtesting Core - Signal Generation and Trade Execution

This module contains the core backtesting logic for:
- Trading signal generation
- Trade execution simulation
- Performance metrics calculation
- Risk management
"""

import polars as pl
import pyarrow as pa
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
import re
import asyncio

# Try to import GPU acceleration
try:
    import cupy as cp
    import cudf
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class TradingSignal:
    """Trading signal data structure"""
    timestamp: datetime
    symbol: str
    signal_type: str  # "long" or "short"
    entry_price: float
    confidence: float = 1.0
    
    # Stop loss and take profit levels
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    # Additional signal metadata
    indicator_values: Dict[str, float] = None
    market_regime: str = "unknown"
    volume_ratio: float = 1.0

class SignalGenerator:
    """Advanced signal generation with multiple strategy support"""
    
    def __init__(self, enable_gpu: bool = False):
        self.enable_gpu = enable_gpu and GPU_AVAILABLE
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def generate_signals(self, data: pl.DataFrame, strategy: Dict[str, Any]) -> List[TradingSignal]:
        """Generate trading signals for a strategy"""
        try:
            signals = []
            
            # Get strategy conditions
            long_condition = strategy.get('long', '')
            short_condition = strategy.get('short', '')
            
            if not long_condition and not short_condition:
                self.logger.warning(f"No trading conditions found for strategy {strategy.get('name', 'unknown')}")
                return signals
            
            # Generate long signals
            if long_condition:
                long_signals = await self._evaluate_condition(data, long_condition, "long")
                signals.extend(long_signals)
            
            # Generate short signals
            if short_condition:
                short_signals = await self._evaluate_condition(data, short_condition, "short")
                signals.extend(short_signals)
            
            # Sort signals by timestamp
            signals.sort(key=lambda x: x.timestamp)
            
            self.logger.debug(f"Generated {len(signals)} signals for strategy {strategy.get('name', 'unknown')}")
            return signals
            
        except Exception as e:
            self.logger.error(f"Failed to generate signals: {e}")
            return []
    
    async def _evaluate_condition(self, data: pl.DataFrame, condition: str, signal_type: str) -> List[TradingSignal]:
        """Evaluate a trading condition and generate signals"""
        try:
            signals = []
            
            # Clean and prepare the condition
            condition = self._prepare_condition(condition)
            
            # Use GPU acceleration if available
            if self.enable_gpu:
                return await self._evaluate_condition_gpu(data, condition, signal_type)
            else:
                return await self._evaluate_condition_cpu(data, condition, signal_type)
                
        except Exception as e:
            self.logger.error(f"Failed to evaluate condition '{condition}': {e}")
            return []
    
    def _prepare_condition(self, condition: str) -> str:
        """Prepare and clean trading condition string"""
        try:
            # Replace common patterns
            replacements = {
                '&': ' & ',
                '|': ' | ',
                '==': ' == ',
                '!=': ' != ',
                '>=': ' >= ',
                '<=': ' <= ',
                '>': ' > ',
                '<': ' < ',
            }
            
            for old, new in replacements.items():
                condition = condition.replace(old, new)
            
            # Remove extra spaces
            condition = ' '.join(condition.split())
            
            return condition
            
        except Exception as e:
            self.logger.error(f"Failed to prepare condition: {e}")
            return condition
    
    async def _evaluate_condition_cpu(self, data: pl.DataFrame, condition: str, signal_type: str) -> List[TradingSignal]:
        """Evaluate condition using CPU (Polars)"""
        try:
            signals = []
            
            # Create a safe evaluation environment
            # This is a simplified version - in production, use a proper expression parser
            
            # For now, use a basic pattern matching approach
            if self._is_simple_condition(condition):
                signal_mask = self._evaluate_simple_condition(data, condition)
                
                if signal_mask is not None:
                    # Get signal timestamps and prices
                    signal_data = data.filter(signal_mask)
                    
                    for row in signal_data.iter_rows(named=True):
                        signal = TradingSignal(
                            timestamp=row['datetime'],
                            symbol=row.get('symbol', row.get('stock_name', 'UNKNOWN')),
                            signal_type=signal_type,
                            entry_price=row['close'],
                            confidence=1.0,
                            indicator_values=self._extract_indicator_values(row)
                        )
                        signals.append(signal)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Failed to evaluate condition on CPU: {e}")
            return []
    
    async def _evaluate_condition_gpu(self, data: pl.DataFrame, condition: str, signal_type: str) -> List[TradingSignal]:
        """Evaluate condition using GPU (CuDF)"""
        try:
            if not GPU_AVAILABLE:
                return await self._evaluate_condition_cpu(data, condition, signal_type)
            
            # Convert to CuDF for GPU processing
            cudf_data = cudf.from_pandas(data.to_pandas())
            
            # Evaluate condition (simplified)
            # In production, implement proper GPU-accelerated condition evaluation
            
            # For now, fall back to CPU
            return await self._evaluate_condition_cpu(data, condition, signal_type)
            
        except Exception as e:
            self.logger.error(f"Failed to evaluate condition on GPU: {e}")
            return await self._evaluate_condition_cpu(data, condition, signal_type)
    
    def _is_simple_condition(self, condition: str) -> bool:
        """Check if condition is a simple comparison that we can handle"""
        # Simple patterns we can handle
        simple_patterns = [
            r'\w+\s*[><=!]+\s*\w+',  # column > value
            r'\w+\s*[><=!]+\s*\w+\.\w+\(\)',  # column > other_column.method()
        ]
        
        for pattern in simple_patterns:
            if re.search(pattern, condition):
                return True
        
        return False
    
    def _evaluate_simple_condition(self, data: pl.DataFrame, condition: str) -> Optional[pl.Expr]:
        """Evaluate a simple condition and return a boolean mask"""
        try:
            # This is a very basic implementation
            # In production, use a proper expression parser like simpleeval or ast
            
            # Handle basic comparisons
            if '>' in condition:
                parts = condition.split('>')
                if len(parts) == 2:
                    left = parts[0].strip()
                    right = parts[1].strip()
                    
                    if left in data.columns:
                        try:
                            right_value = float(right)
                            return pl.col(left) > right_value
                        except ValueError:
                            if right in data.columns:
                                return pl.col(left) > pl.col(right)
            
            elif '<' in condition:
                parts = condition.split('<')
                if len(parts) == 2:
                    left = parts[0].strip()
                    right = parts[1].strip()
                    
                    if left in data.columns:
                        try:
                            right_value = float(right)
                            return pl.col(left) < right_value
                        except ValueError:
                            if right in data.columns:
                                return pl.col(left) < pl.col(right)
            
            # Add more condition types as needed
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to evaluate simple condition: {e}")
            return None
    
    def _extract_indicator_values(self, row: Dict[str, Any]) -> Dict[str, float]:
        """Extract indicator values from a data row"""
        try:
            indicators = {}
            
            # Common technical indicators
            indicator_columns = [
                'rsi_14', 'rsi_5', 'macd', 'macd_signal', 'macd_histogram',
                'ema_5', 'ema_10', 'ema_20', 'ema_50', 'ema_200',
                'sma_20', 'sma_50', 'sma_200',
                'bb_upper', 'bb_lower', 'bb_middle',
                'stoch_k', 'stoch_d', 'cci', 'adx', 'atr',
                'volume', 'vwap', 'supertrend'
            ]
            
            for col in indicator_columns:
                if col in row and row[col] is not None:
                    try:
                        indicators[col] = float(row[col])
                    except (ValueError, TypeError):
                        pass
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Failed to extract indicator values: {e}")
            return {}

class TradeExecutor:
    """Advanced trade execution with realistic modeling"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def execute_trades(self, data: pl.DataFrame, signals: List[TradingSignal],
                           strategy: Dict[str, Any], risk_reward_ratio: List[float]) -> List[Any]:
        """Execute trades based on signals"""
        try:
            trades = []
            
            # Sort data by timestamp
            data = data.sort('datetime')
            
            # Process each signal
            for signal in signals:
                trade = await self._execute_single_trade(data, signal, strategy, risk_reward_ratio)
                if trade:
                    trades.append(trade)
            
            self.logger.debug(f"Executed {len(trades)} trades from {len(signals)} signals")
            return trades
            
        except Exception as e:
            self.logger.error(f"Failed to execute trades: {e}")
            return []
    
    async def _execute_single_trade(self, data: pl.DataFrame, signal: TradingSignal, 
                                  strategy: Dict[str, Any], risk_reward_ratio: List[float]) -> Optional[Dict[str, Any]]:
        """Execute a single trade"""
        try:
            # Find entry point in data
            entry_data = data.filter(pl.col('datetime') >= signal.timestamp).head(1)
            
            if entry_data.height == 0:
                return None
            
            entry_row = entry_data.row(0, named=True)
            entry_time = entry_row['datetime']
            entry_price = entry_row['close']
            
            # Calculate position size
            position_size = self._calculate_position_size(entry_price, strategy)
            
            # Calculate stop loss and take profit
            stop_loss_pct = risk_reward_ratio[0] / 100  # Convert to decimal
            take_profit_pct = risk_reward_ratio[1] / 100
            
            if signal.signal_type == "long":
                stop_loss_price = entry_price * (1 - stop_loss_pct)
                take_profit_price = entry_price * (1 + take_profit_pct)
            else:  # short
                stop_loss_price = entry_price * (1 + stop_loss_pct)
                take_profit_price = entry_price * (1 - take_profit_pct)
            
            # Find exit point
            exit_data = await self._find_exit_point(
                data, entry_time, signal.signal_type, stop_loss_price, take_profit_price
            )
            
            if not exit_data:
                return None
            
            exit_time, exit_price, exit_reason = exit_data
            
            # Calculate trade results
            trade_result = self._calculate_trade_result(
                signal, entry_time, entry_price, exit_time, exit_price, 
                position_size, exit_reason, strategy
            )
            
            return trade_result
            
        except Exception as e:
            self.logger.error(f"Failed to execute single trade: {e}")
            return None
    
    def _calculate_position_size(self, entry_price: float, strategy: Dict[str, Any]) -> float:
        """Calculate position size based on risk management rules"""
        try:
            # Get capital allocation
            capital = strategy.get('capital', self.config.get('initial_capital', 100000))
            risk_per_trade_pct = self.config.get('risk_per_trade_pct', 1.0)
            
            # Calculate position size
            risk_amount = capital * (risk_per_trade_pct / 100)
            position_size = risk_amount  # Simplified - in reality, factor in stop loss distance
            
            return position_size
            
        except Exception as e:
            self.logger.error(f"Failed to calculate position size: {e}")
            return 1000.0  # Default position size
    
    async def _find_exit_point(self, data: pl.DataFrame, entry_time: datetime, 
                             signal_type: str, stop_loss_price: float, 
                             take_profit_price: float) -> Optional[Tuple[datetime, float, str]]:
        """Find the exit point for a trade"""
        try:
            # Get data after entry
            future_data = data.filter(pl.col('datetime') > entry_time)
            
            if future_data.height == 0:
                return None
            
            # Check each subsequent bar for exit conditions
            for row in future_data.iter_rows(named=True):
                timestamp = row['datetime']
                high = row['high']
                low = row['low']
                close = row['close']
                
                if signal_type == "long":
                    # Check stop loss
                    if low <= stop_loss_price:
                        return timestamp, stop_loss_price, "stop_loss"
                    
                    # Check take profit
                    if high >= take_profit_price:
                        return timestamp, take_profit_price, "take_profit"
                
                else:  # short
                    # Check stop loss
                    if high >= stop_loss_price:
                        return timestamp, stop_loss_price, "stop_loss"
                    
                    # Check take profit
                    if low <= take_profit_price:
                        return timestamp, take_profit_price, "take_profit"
                
                # Check for end of day exit (intraday trading)
                if self._is_end_of_day(timestamp):
                    return timestamp, close, "eod_exit"
            
            # If no exit found, use last available price
            last_row = future_data.tail(1).row(0, named=True)
            return last_row['datetime'], last_row['close'], "data_end"
            
        except Exception as e:
            self.logger.error(f"Failed to find exit point: {e}")
            return None
    
    def _is_end_of_day(self, timestamp: datetime) -> bool:
        """Check if timestamp is near end of trading day"""
        # For Indian markets, trading ends at 3:30 PM
        return timestamp.hour >= 15 and timestamp.minute >= 20
    
    def _calculate_trade_result(self, signal: TradingSignal, entry_time: datetime,
                              entry_price: float, exit_time: datetime, exit_price: float,
                              position_size: float, exit_reason: str,
                              strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive trade result with all required columns for AI training"""
        try:
            # Calculate basic P&L
            if signal.signal_type == "long":
                pnl_abs = (exit_price - entry_price) * (position_size / entry_price)
            else:  # short
                pnl_abs = (entry_price - exit_price) * (position_size / entry_price)

            pnl_pct = (pnl_abs / position_size) * 100

            # Calculate holding period in minutes
            holding_minutes = (exit_time - entry_time).total_seconds() / 60

            # Apply transaction costs
            transaction_cost = self._calculate_transaction_costs(position_size)
            pnl_abs_net = pnl_abs - transaction_cost

            # Extract indicator values
            indicators = signal.indicator_values or {}

            # Parse symbol for options details
            symbol_parts = self._parse_option_symbol(signal.symbol)

            # Determine market regime and volatility regime
            market_regime = self._determine_market_regime(indicators)
            volatility_regime = self._determine_volatility_regime(indicators)

            # Calculate trade score and labels
            is_profitable = 1 if pnl_abs_net > 0 else 0
            expectancy_tag = ">0" if pnl_abs_net > 0 else ("<0" if pnl_abs_net < 0 else "zero")

            # Import TradeResult class
            from .enhanced_backtesting_agent import TradeResult

            # Create comprehensive TradeResult object with all required columns
            trade_result = TradeResult(
                # ═══════════════════════════════════════════════════════════════════════════════
                # 📌 A. Trade Metadata Columns
                # ═══════════════════════════════════════════════════════════════════════════════
                trade_id=f"{signal.symbol}_{entry_time.strftime('%Y%m%d_%H%M%S')}",
                timestamp=entry_time,
                exit_timestamp=exit_time,
                symbol=signal.symbol,
                underlying=symbol_parts.get('underlying', 'NIFTY'),
                expiry_type=symbol_parts.get('expiry_type', 'weekly'),
                option_type=symbol_parts.get('option_type', 'CE'),
                lot_size=symbol_parts.get('lot_size', 50),
                direction=f"Buy {symbol_parts.get('option_type', 'Call')}" if signal.signal_type == "long" else f"Sell {symbol_parts.get('option_type', 'Call')}",
                strategy_id=strategy.get('name', 'unknown'),
                strategy_name=strategy.get('name', 'unknown'),
                timeframe=getattr(signal, 'timeframe', '5min'),
                market_regime=market_regime,
                volatility_regime=volatility_regime,
                event_tag=self._detect_event_tag(entry_time),

                # ═══════════════════════════════════════════════════════════════════════════════
                # 📌 B. Feature Snapshot (at Entry Time)
                # ═══════════════════════════════════════════════════════════════════════════════
                # Technical Indicators
                rsi=indicators.get('rsi_14', 0.0),
                rsi_5=indicators.get('rsi_5', 0.0),
                rsi_14=indicators.get('rsi_14', 0.0),
                macd=indicators.get('macd', 0.0),
                macd_signal=indicators.get('macd_signal', 0.0),
                macd_histogram=indicators.get('macd_histogram', 0.0),

                # Moving Averages
                ema_5=indicators.get('ema_5', 0.0),
                ema_10=indicators.get('ema_10', 0.0),
                ema_20=indicators.get('ema_20', 0.0),
                ema_50=indicators.get('ema_50', 0.0),
                sma_20=indicators.get('sma_20', 0.0),
                sma_50=indicators.get('sma_50', 0.0),
                sma_20_vs_price=indicators.get('sma_20', 0.0) / entry_price if entry_price > 0 else 0.0,

                # Bollinger Bands
                bb_upper=indicators.get('bb_upper', 0.0),
                bb_lower=indicators.get('bb_lower', 0.0),
                bb_middle=indicators.get('bb_middle', 0.0),

                # Other Technical Indicators
                adx=indicators.get('adx', 0.0),
                atr=indicators.get('atr', 0.0),
                stoch_k=indicators.get('stoch_k', 0.0),
                stoch_d=indicators.get('stoch_d', 0.0),
                cci=indicators.get('cci', 0.0),
                mfi=indicators.get('mfi', 0.0),

                # Volume and Price Action
                volume=indicators.get('volume', 0.0),
                volume_spike_ratio=signal.volume_ratio,
                vwap=indicators.get('vwap', 0.0),
                price_vs_vwap=((entry_price - indicators.get('vwap', entry_price)) / indicators.get('vwap', entry_price)) * 100 if indicators.get('vwap', 0) > 0 else 0.0,
                supertrend=indicators.get('supertrend', 0.0),

                # Options-specific (placeholder values - would need real options data)
                iv_rank=indicators.get('iv_rank', 0.0),
                iv_percentile=indicators.get('iv_percentile', 0.0),
                vix=indicators.get('vix', 15.0),  # Default VIX level
                delta=indicators.get('delta', 0.5),
                gamma=indicators.get('gamma', 0.0),
                theta=indicators.get('theta', 0.0),
                vega=indicators.get('vega', 0.0),
                open_interest=indicators.get('open_interest', 0.0),
                open_interest_change=indicators.get('open_interest_change', 0.0),

                # Time-based features
                hour_of_day=entry_time.hour + entry_time.minute / 60.0,
                day_of_week=entry_time.weekday() + 1,
                days_to_expiry=symbol_parts.get('days_to_expiry', 7),

                # Market Context
                nifty_level=indicators.get('nifty_level', 18000.0),
                banknifty_level=indicators.get('banknifty_level', 44000.0),

                # ═══════════════════════════════════════════════════════════════════════════════
                # 📌 C. Trade Outcome / Label Columns
                # ═══════════════════════════════════════════════════════════════════════════════
                entry_price=entry_price,
                exit_price=exit_price,
                pnl_abs=pnl_abs_net,
                pnl_pct=pnl_pct,
                holding_minutes=holding_minutes,
                is_profitable=is_profitable,
                target_hit="TP" if exit_reason == "take_profit" else ("SL" if exit_reason == "stop_loss" else "Manual"),
                exit_reason=exit_reason,
                expectancy_tag=expectancy_tag,
                sharpe_score_tag=self._calculate_sharpe_tag(pnl_pct, holding_minutes),
                confidence_score=signal.confidence,
                trade_score=self._calculate_trade_score(pnl_pct, signal.confidence, exit_reason),
                label_strategy_choice=strategy.get('name', 'unknown'),

                # Position and Risk
                position_size=position_size,
                quantity=int(position_size / (entry_price * symbol_parts.get('lot_size', 50))),
                risk_reward_ratio=abs(exit_price - entry_price) / abs(entry_price - (entry_price * 0.98)) if entry_price > 0 else 0.0,  # Simplified RR
                max_adverse_excursion=0.0,  # Would need tick-by-tick data
                max_favorable_excursion=0.0,  # Would need tick-by-tick data

                # Execution details
                slippage=position_size * (self.config.get('slippage_pct', 0.02) / 100),
                transaction_cost=transaction_cost,

                # ═══════════════════════════════════════════════════════════════════════════════
                # 📌 D. Optional Advanced Tags
                # ═══════════════════════════════════════════════════════════════════════════════
                model_version_used='v1.0',
                was_signal_live=False,
                live_vs_backtest_diff_pct=0.0,
                used_in_training=True,
                llm_summary=f"Trade {'succeeded' if is_profitable else 'failed'} with {pnl_pct:.2f}% return in {holding_minutes:.0f} minutes using {strategy.get('name', 'unknown')} strategy"
            )

            return trade_result

        except Exception as e:
            self.logger.error(f"Failed to calculate trade result: {e}")
            return {}
    
    def _calculate_transaction_costs(self, position_size: float) -> float:
        """Calculate realistic transaction costs for Indian markets"""
        try:
            # Brokerage
            brokerage_pct = self.config.get('brokerage_pct', 0.03) / 100
            brokerage_flat = self.config.get('brokerage_flat', 20.0)
            brokerage = min(position_size * brokerage_pct, brokerage_flat)
            
            # STT (Securities Transaction Tax)
            stt = position_size * (self.config.get('stt_pct', 0.025) / 100)
            
            # Other charges (simplified)
            other_charges = position_size * 0.0001  # Exchange charges, SEBI charges, etc.
            
            # Slippage
            slippage = position_size * (self.config.get('slippage_pct', 0.02) / 100)
            
            total_cost = brokerage + stt + other_charges + slippage
            return total_cost
            
        except Exception as e:
            self.logger.error(f"Failed to calculate transaction costs: {e}")
            return position_size * 0.001  # Default 0.1% total cost

    def _parse_option_symbol(self, symbol: str) -> Dict[str, Any]:
        """Parse option symbol to extract details"""
        try:
            # Example: BANKNIFTY24JUL47000CE
            # Default values
            result = {
                'underlying': 'NIFTY',
                'expiry_type': 'weekly',
                'option_type': 'CE',
                'strike': 0,
                'lot_size': 50,
                'days_to_expiry': 7
            }

            if 'BANKNIFTY' in symbol.upper():
                result['underlying'] = 'BANKNIFTY'
                result['lot_size'] = 15
                result['expiry_type'] = 'monthly'  # BANKNIFTY typically monthly
            elif 'NIFTY' in symbol.upper():
                result['underlying'] = 'NIFTY'
                result['lot_size'] = 50
                result['expiry_type'] = 'weekly'  # NIFTY typically weekly

            # Extract option type
            if symbol.upper().endswith('CE'):
                result['option_type'] = 'CE'
            elif symbol.upper().endswith('PE'):
                result['option_type'] = 'PE'

            # Extract strike price (simplified)
            import re
            strike_match = re.search(r'(\d{4,5})(CE|PE)$', symbol.upper())
            if strike_match:
                result['strike'] = int(strike_match.group(1))

            return result

        except Exception as e:
            self.logger.error(f"Failed to parse option symbol {symbol}: {e}")
            return {
                'underlying': 'NIFTY',
                'expiry_type': 'weekly',
                'option_type': 'CE',
                'strike': 0,
                'lot_size': 50,
                'days_to_expiry': 7
            }

    def _determine_market_regime(self, indicators: Dict[str, float]) -> str:
        """Determine market regime based on indicators"""
        try:
            # Get key indicators
            rsi = indicators.get('rsi_14', 50)
            adx = indicators.get('adx', 20)
            atr = indicators.get('atr', 0)

            # Simple regime classification
            if adx > 25:  # Strong trend
                if rsi > 60:
                    return "Trending Up"
                elif rsi < 40:
                    return "Trending Down"
                else:
                    return "Trending"
            elif atr > 50:  # High volatility
                return "Volatile"
            else:
                return "Sideways"

        except Exception as e:
            self.logger.error(f"Failed to determine market regime: {e}")
            return "Unknown"

    def _determine_volatility_regime(self, indicators: Dict[str, float]) -> str:
        """Determine volatility regime"""
        try:
            vix = indicators.get('vix', 15)

            if vix < 12:
                return "Low IV"
            elif vix > 20:
                return "High IV"
            else:
                return "Normal IV"

        except Exception as e:
            self.logger.error(f"Failed to determine volatility regime: {e}")
            return "Normal IV"

    def _detect_event_tag(self, timestamp: datetime) -> str:
        """Detect if trade occurred during special events"""
        try:
            # Check for expiry day (Thursday for weekly options)
            if timestamp.weekday() == 3:  # Thursday
                return "Expiry Day"

            # Check for month-end
            import calendar
            last_day = calendar.monthrange(timestamp.year, timestamp.month)[1]
            if timestamp.day >= last_day - 2:
                return "Month End"

            # Add more event detection logic as needed
            return ""

        except Exception as e:
            self.logger.error(f"Failed to detect event tag: {e}")
            return ""

    def _calculate_sharpe_tag(self, pnl_pct: float, holding_minutes: float) -> str:
        """Calculate Sharpe score tag"""
        try:
            # Simplified Sharpe calculation
            if holding_minutes <= 0:
                return "low"

            # Annualized return approximation
            annual_return = (pnl_pct / 100) * (365 * 24 * 60 / holding_minutes)

            if annual_return > 0.5:  # 50% annual return
                return "high"
            elif annual_return > 0.2:  # 20% annual return
                return "medium"
            else:
                return "low"

        except Exception as e:
            self.logger.error(f"Failed to calculate Sharpe tag: {e}")
            return "low"

    def _calculate_trade_score(self, pnl_pct: float, confidence: float, exit_reason: str) -> float:
        """Calculate weighted trade score"""
        try:
            # Base score from PnL
            base_score = pnl_pct

            # Adjust for confidence
            confidence_weight = confidence if 0 <= confidence <= 1 else 1.0

            # Adjust for exit reason
            exit_multiplier = 1.0
            if exit_reason == "take_profit":
                exit_multiplier = 1.2  # Bonus for hitting target
            elif exit_reason == "stop_loss":
                exit_multiplier = 0.8  # Penalty for hitting stop

            trade_score = base_score * confidence_weight * exit_multiplier

            return trade_score

        except Exception as e:
            self.logger.error(f"Failed to calculate trade score: {e}")
            return 0.0
