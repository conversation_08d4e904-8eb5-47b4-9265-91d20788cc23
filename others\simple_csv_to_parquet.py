#!/usr/bin/env python3
"""
Simple, reliable CSV to Parquet converter for large files
No emojis, no complex async - just works!
"""

import polars as pl
import time
import psutil
import os
import gc
from pathlib import Path
import logging

# Configure simple logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
INPUT_CSV = r"C:\Users\<USER>\Documents\Intraday-AI\smartapi_data.csv"
OUTPUT_DIR = Path("data")
OUTPUT_FILE = "historical_5min.parquet"
CHUNK_SIZE = 50_000  # Rows per chunk
WRITE_BATCH_SIZE = 5  # Write every N chunks
COMPRESSION = "brotli"
ENABLE_VERIFICATION = True  # Enable data integrity verification

def get_file_size_info(file_path):
    """Get file size information"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    size_bytes = os.path.getsize(file_path)
    size_mb = size_bytes / (1024 * 1024)
    size_gb = size_mb / 1024
    
    return {
        'bytes': size_bytes,
        'mb': size_mb,
        'gb': size_gb
    }

def safe_file_operation(operation, *args, max_retries=3, delay=0.5):
    """Safely perform file operations with retry logic for Windows"""
    import time

    for attempt in range(max_retries):
        try:
            return operation(*args)
        except (PermissionError, OSError) as e:
            if "user-mapped section" in str(e) or "being used by another process" in str(e):
                if attempt < max_retries - 1:
                    logger.warning(f"File locked, retrying in {delay} seconds... (attempt {attempt + 1}/{max_retries})")
                    time.sleep(delay)
                    gc.collect()  # Force garbage collection to release handles
                    continue
            raise e

    raise Exception(f"Failed after {max_retries} attempts")

def verify_chunk_integrity(temp_file, expected_rows):
    """Verify chunk integrity with Windows-safe file handling"""
    if not ENABLE_VERIFICATION:
        return True

    try:
        def read_and_verify():
            df = pl.read_parquet(temp_file)
            row_count = len(df)
            del df  # Explicitly release
            gc.collect()
            return row_count

        actual_rows = safe_file_operation(read_and_verify)

        if actual_rows != expected_rows:
            logger.error(f"Chunk verification failed: expected {expected_rows}, got {actual_rows} rows")
            return False
        return True
    except Exception as e:
        logger.error(f"Chunk verification error: {e}")
        return False

def append_chunks_to_output(temp_files, output_path, is_first_write=False, expected_total_rows=0):
    """Append chunk files to output parquet file with verification - Windows safe"""

    if not temp_files:
        return 0

    logger.info(f"Processing {len(temp_files)} chunk files...")

    # Read all temp files with verification
    chunk_dfs = []
    batch_row_count = 0

    for temp_file in temp_files:
        if os.path.exists(temp_file):
            df = pl.read_parquet(temp_file)
            chunk_dfs.append(df)
            batch_row_count += len(df)

    if not chunk_dfs:
        return 0

    # Combine chunks
    batch_df = pl.concat(chunk_dfs, how="vertical")

    # Verify concatenation
    if len(batch_df) != batch_row_count:
        logger.error(f"Batch concatenation failed: expected {batch_row_count}, got {len(batch_df)}")
        raise ValueError("Batch concatenation verification failed")

    if is_first_write or not os.path.exists(output_path):
        # Create new file
        batch_df.write_parquet(
            output_path,
            compression=COMPRESSION
        )
        logger.info(f"Created output file with {len(batch_df):,} rows")
        final_rows = len(batch_df)
    else:
        # Windows-safe append: use temporary file to avoid locking issues
        temp_output_path = str(output_path) + ".tmp"

        try:
            # Read existing file completely into memory
            existing_df = pl.read_parquet(output_path)
            existing_count = len(existing_df)

            # Combine with new batch
            combined_df = pl.concat([existing_df, batch_df], how="vertical")
            expected_combined = existing_count + len(batch_df)

            # Verify final concatenation
            if len(combined_df) != expected_combined:
                logger.error(f"Final concatenation failed: expected {expected_combined}, got {len(combined_df)}")
                raise ValueError("Final concatenation verification failed")

            # Write to temporary file first
            combined_df.write_parquet(
                temp_output_path,
                compression=COMPRESSION
            )

            # Force garbage collection to release file handles
            del existing_df
            del combined_df
            gc.collect()

            # Windows-safe file replacement
            import time
            time.sleep(0.1)  # Brief pause to ensure file handles are released

            # Remove original and rename temp file
            if os.path.exists(output_path):
                os.remove(output_path)
            os.rename(temp_output_path, output_path)

            logger.info(f"Appended {len(batch_df):,} rows (total: {expected_combined:,})")
            final_rows = expected_combined

        except Exception as e:
            # Cleanup temp file if it exists
            if os.path.exists(temp_output_path):
                try:
                    os.remove(temp_output_path)
                except:
                    pass
            raise e

    # Verify written file (with retry for Windows)
    if ENABLE_VERIFICATION:
        # Brief pause to ensure file is fully written
        import time
        time.sleep(0.1)

        try:
            verification_df = pl.read_parquet(output_path)
            if len(verification_df) != final_rows:
                logger.error(f"File verification failed: expected {final_rows}, file has {len(verification_df)}")
                raise ValueError("File verification failed")
            logger.info(f"File verification passed: {len(verification_df):,} rows confirmed")

            # Release verification dataframe
            del verification_df
            gc.collect()

        except Exception as e:
            logger.error(f"Verification failed: {e}")
            # Don't fail the entire process for verification issues
            logger.warning("Continuing without verification due to file access issues")

    # Clean up temp files
    for temp_file in temp_files:
        try:
            os.remove(temp_file)
        except:
            pass

    # Force garbage collection
    gc.collect()

    return final_rows

def convert_csv_to_parquet():
    """Main conversion function"""
    
    logger.info("=" * 60)
    logger.info("CSV TO PARQUET CONVERTER")
    logger.info("=" * 60)
    
    # Setup
    OUTPUT_DIR.mkdir(exist_ok=True)
    output_path = OUTPUT_DIR / OUTPUT_FILE
    
    # File info
    try:
        file_info = get_file_size_info(INPUT_CSV)
        logger.info(f"Input file: {INPUT_CSV}")
        logger.info(f"File size: {file_info['gb']:.2f}GB ({file_info['mb']:.1f}MB)")
        logger.info(f"Output: {output_path}")
        logger.info(f"Chunk size: {CHUNK_SIZE:,} rows")
        logger.info(f"Write batch size: {WRITE_BATCH_SIZE} chunks")
        logger.info(f"Compression: {COMPRESSION}")
    except Exception as e:
        logger.error(f"Error accessing input file: {e}")
        return
    
    # Memory info
    memory_gb = psutil.virtual_memory().total / (1024**3)
    logger.info(f"Available RAM: {memory_gb:.1f}GB")
    
    start_time = time.time()
    
    try:
        logger.info("\nStarting conversion...")
        
        # Process in chunks with incremental writing
        chunk_num = 0
        temp_files_batch = []
        is_first_write = True
        total_rows = 0
        
        # Create CSV reader
        try:
            reader = pl.read_csv_batched(
                INPUT_CSV,
                batch_size=CHUNK_SIZE,
                infer_schema_length=10000,
                try_parse_dates=True,
                null_values=["", "NULL", "null", "NA", "N/A"],
                ignore_errors=True
            )
        except Exception as e:
            logger.error(f"Failed to create CSV reader: {e}")
            return
        
        # Process chunks
        while True:
            try:
                # Get next batch
                batches = reader.next_batches(1)
                if batches is None or len(batches) == 0:
                    break
                
                chunk_df = batches[0]
                if len(chunk_df) == 0:
                    break
                
            except StopIteration:
                break
            except Exception as e:
                logger.error(f"Error reading chunk {chunk_num}: {e}")
                break
            
            # Write chunk to temp file with Windows-safe handling
            temp_file = OUTPUT_DIR / f"temp_chunk_{chunk_num:06d}.parquet"

            try:
                # Use safe file operation for writing
                def write_chunk():
                    chunk_df.write_parquet(
                        temp_file,
                        compression=COMPRESSION
                    )

                safe_file_operation(write_chunk)

                # Verify chunk integrity
                if not verify_chunk_integrity(str(temp_file), len(chunk_df)):
                    logger.error(f"Chunk {chunk_num} failed verification!")
                    if temp_file.exists():
                        temp_file.unlink()
                    continue  # Skip this chunk and try next

                temp_files_batch.append(str(temp_file))
                total_rows += len(chunk_df)
                chunk_num += 1
                
                # Log progress
                elapsed = time.time() - start_time
                rows_per_sec = total_rows / elapsed if elapsed > 0 else 0
                memory_pct = psutil.virtual_memory().percent
                
                logger.info(
                    f"Chunk {chunk_num}: {len(chunk_df):,} rows | "
                    f"Total: {total_rows:,} | "
                    f"Speed: {rows_per_sec:,.0f} rows/sec | "
                    f"Memory: {memory_pct:.1f}%"
                )
                
                # Write batch when we have enough chunks
                if len(temp_files_batch) >= WRITE_BATCH_SIZE:
                    logger.info(f"Writing batch of {len(temp_files_batch)} chunks...")

                    try:
                        current_total = append_chunks_to_output(temp_files_batch, output_path, is_first_write)
                        temp_files_batch = []
                        is_first_write = False

                        # Verify running total
                        if current_total != total_rows:
                            logger.warning(f"Running total mismatch: expected {total_rows}, file has {current_total}")

                    except Exception as e:
                        logger.error(f"Batch write failed: {e}")
                        # Clean up temp files on failure
                        for tf in temp_files_batch:
                            try:
                                if tf.exists():
                                    tf.unlink()
                            except:
                                pass
                        raise e

                    # Memory check
                    if psutil.virtual_memory().percent > 85:
                        logger.warning("High memory usage - forcing cleanup")
                        gc.collect()
                        
            except Exception as e:
                logger.error(f"Error processing chunk {chunk_num}: {e}")
                # Clean up temp file if it exists
                if temp_file.exists():
                    temp_file.unlink()
                continue
        
        # Write remaining chunks
        if temp_files_batch:
            logger.info(f"Writing final batch of {len(temp_files_batch)} chunks...")

            try:
                final_total = append_chunks_to_output(temp_files_batch, output_path, is_first_write)

                # Final verification
                if final_total != total_rows:
                    logger.error(f"Final total mismatch: expected {total_rows}, file has {final_total}")
                    raise ValueError("Final verification failed")

            except Exception as e:
                logger.error(f"Final batch write failed: {e}")
                # Clean up temp files
                for tf in temp_files_batch:
                    try:
                        if tf.exists():
                            tf.unlink()
                    except:
                        pass
                raise e
        
        # Summary
        elapsed_time = time.time() - start_time
        
        logger.info("\n" + "=" * 60)
        logger.info("CONVERSION COMPLETED!")
        logger.info("=" * 60)
        logger.info(f"Total rows processed: {total_rows:,}")
        logger.info(f"Total chunks: {chunk_num}")
        logger.info(f"Total time: {elapsed_time:.1f} seconds ({elapsed_time/60:.1f} minutes)")
        
        if elapsed_time > 0:
            logger.info(f"Average speed: {total_rows/elapsed_time:,.0f} rows/second")
        
        # Output file info
        if output_path.exists():
            output_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
            compression_ratio = file_info['mb'] / output_size if output_size > 0 else 0
            
            logger.info(f"Output file: {output_path}")
            logger.info(f"Output size: {output_size:.1f}MB")
            logger.info(f"Compression ratio: {compression_ratio:.1f}x")
            logger.info(f"Space saved: {file_info['mb'] - output_size:.1f}MB")
            
            # Verify the file
            try:
                verify_df = pl.read_parquet(output_path)
                logger.info(f"Verification: {len(verify_df):,} rows, {len(verify_df.columns)} columns")
                logger.info("Conversion verified successfully!")
            except Exception as e:
                logger.error(f"Verification failed: {e}")
        else:
            logger.error("Output file was not created!")
            
    except Exception as e:
        logger.error(f"Conversion failed: {e}")
        raise

def main():
    """Main entry point"""
    
    # Check input file
    if not os.path.exists(INPUT_CSV):
        logger.error(f"Input file not found: {INPUT_CSV}")
        return
    
    # Run conversion
    convert_csv_to_parquet()

if __name__ == "__main__":
    main()
