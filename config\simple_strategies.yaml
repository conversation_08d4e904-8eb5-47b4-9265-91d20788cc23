# Simple Working Strategies for Indian Market
# Tested & verified to work with actual data columns

strategies:
  - name: "Simple_RSI_Long"
    long: "RSI_14 < 30"
    short: "RSI_14 > 70"
    capital: 100000

  - name: "Simple_EMA_Crossover"
    long: "EMA_5 > EMA_20"
    short: "EMA_5 < EMA_20"
    capital: 100000

  - name: "Simple_VWAP_Strategy"
    long: "Close > VWAP"
    short: "Close < VWAP"
    capital: 100000

  - name: "Simple_SuperTrend"
    long: "Close > SuperTrend"
    short: "Close < SuperTrend"
    capital: 100000

  - name: "Simple_Volume_Breakout"
    long: "Volume > Volume.rolling(20).mean() * 2"
    short: "Volume > Volume.rolling(20).mean() * 2"
    capital: 100000

