#!/usr/bin/env python3
"""
Run Improved Backtesting System
- Executes enhanced backtesting with walk-forward analysis
- Generates comprehensive performance reports
- Includes statistical significance testing
"""

import asyncio
import logging
import sys
from pathlib import Path
import argparse
import json
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from agents.enhanced_backtesting_improved import EnhancedBacktester, BacktestConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('improved_backtesting.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

async def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Run Improved Backtesting System')
    parser.add_argument('--symbols', nargs='+', help='Specific symbols to test (optional)')
    parser.add_argument('--timeframes', nargs='+', default=['1min', '5min', '15min'], 
                       help='Timeframes to test')
    parser.add_argument('--max-files', type=int, default=10, 
                       help='Maximum number of files to process')
    parser.add_argument('--config-file', help='Custom configuration file')
    parser.add_argument('--output-dir', default='data/backtest_improved',
                       help='Output directory for results')
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting Improved Backtesting System")
    logger.info("=" * 60)
    
    # Load configuration
    if args.config_file and Path(args.config_file).exists():
        logger.info(f"Loading custom config from {args.config_file}")
        # Custom config loading logic here
        config = BacktestConfig()
    else:
        config = BacktestConfig()
        logger.info("Using default configuration")
    
    # Initialize backtester
    backtester = EnhancedBacktester(config)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Get feature files
    data_dir = Path("data/features")
    if not data_dir.exists():
        logger.error(f"Data directory not found: {data_dir}")
        return
    
    feature_files = []
    for file_path in data_dir.glob("*.parquet"):
        filename = file_path.name
        
        # Extract symbol and timeframe
        if filename.startswith("features_"):
            parts = filename.replace("features_", "").replace(".parquet", "").split("_")
            if len(parts) >= 2:
                timeframe = parts[-1]
                symbol = "_".join(parts[:-1])
                
                # Apply filters
                if args.symbols and symbol not in args.symbols:
                    continue
                if timeframe not in args.timeframes:
                    continue
                
                feature_files.append((str(file_path), symbol, timeframe))
    
    if not feature_files:
        logger.error("No matching feature files found")
        return
    
    # Limit files if specified
    if args.max_files:
        feature_files = feature_files[:args.max_files]
    
    logger.info(f"📊 Processing {len(feature_files)} files")
    logger.info(f"📈 Timeframes: {args.timeframes}")
    if args.symbols:
        logger.info(f"🎯 Symbols: {args.symbols}")
    
    # Process files
    all_results = []
    successful_tests = 0
    failed_tests = 0
    
    start_time = datetime.now()
    
    for i, (file_path, symbol, timeframe) in enumerate(feature_files, 1):
        logger.info(f"🔄 Processing {i}/{len(feature_files)}: {symbol} ({timeframe})")
        
        try:
            result = await backtester.run_enhanced_backtest(symbol, timeframe, file_path)
            
            if result and result.get('valid_results', 0) > 0:
                all_results.append(result)
                successful_tests += 1
                
                # Log key metrics
                best_strategy = None
                best_consistency = 0
                
                for strategy_result in result.get('results', []):
                    consistency = strategy_result.get('consistency_score', 0)
                    if consistency > best_consistency:
                        best_consistency = consistency
                        best_strategy = strategy_result.get('strategy_name', 'Unknown')
                
                logger.info(f"✅ {symbol}: Best strategy '{best_strategy}' (Consistency: {best_consistency:.2f})")
            else:
                failed_tests += 1
                logger.warning(f"❌ {symbol}: No valid results generated")
                
        except Exception as e:
            failed_tests += 1
            logger.error(f"❌ {symbol}: Error - {e}")
    
    # Save results
    if all_results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save detailed results
        results_file = output_dir / f"enhanced_backtest_results_{timestamp}.json"
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        # Generate summary report
        summary_file = output_dir / f"backtest_summary_{timestamp}.json"
        summary = generate_summary_report(all_results, config)
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        # Generate performance ranking
        ranking_file = output_dir / f"strategy_ranking_{timestamp}.json"
        ranking = generate_strategy_ranking(all_results)
        with open(ranking_file, 'w') as f:
            json.dump(ranking, f, indent=2, default=str)
        
        logger.info("📁 Results saved:")
        logger.info(f"   📄 Detailed: {results_file}")
        logger.info(f"   📊 Summary: {summary_file}")
        logger.info(f"   🏆 Ranking: {ranking_file}")
    
    # Final summary
    end_time = datetime.now()
    duration = end_time - start_time
    
    logger.info("=" * 60)
    logger.info("🎉 BACKTESTING COMPLETED")
    logger.info(f"⏱️  Duration: {duration}")
    logger.info(f"✅ Successful: {successful_tests}")
    logger.info(f"❌ Failed: {failed_tests}")
    logger.info(f"📈 Total Results: {len(all_results)}")
    
    if all_results:
        # Print top strategies
        top_strategies = get_top_strategies(all_results, limit=5)
        logger.info("\n🏆 TOP PERFORMING STRATEGIES:")
        for i, strategy in enumerate(top_strategies, 1):
            logger.info(f"   {i}. {strategy['name']} - Consistency: {strategy['consistency']:.2f}")

def generate_summary_report(results: list, config: BacktestConfig) -> dict:
    """Generate summary report"""
    total_strategies = sum(r.get('strategies_tested', 0) for r in results)
    valid_strategies = sum(r.get('valid_results', 0) for r in results)
    
    # Collect all strategy results
    all_strategy_results = []
    for result in results:
        all_strategy_results.extend(result.get('results', []))
    
    # Calculate aggregate metrics
    if all_strategy_results:
        avg_consistency = sum(s.get('consistency_score', 0) for s in all_strategy_results) / len(all_strategy_results)
        avg_sharpe = sum(s.get('avg_sharpe_ratio', 0) for s in all_strategy_results) / len(all_strategy_results)
        
        # Count strategies meeting criteria
        meeting_criteria = sum(1 for s in all_strategy_results 
                             if s.get('consistency_score', 0) >= 0.6 and 
                                s.get('avg_sharpe_ratio', 0) >= config.min_sharpe_ratio)
    else:
        avg_consistency = avg_sharpe = meeting_criteria = 0
    
    return {
        'timestamp': datetime.now().isoformat(),
        'configuration': {
            'initial_capital': config.initial_capital,
            'transaction_cost_pct': config.transaction_cost_pct,
            'walk_forward_steps': config.walk_forward_steps,
            'min_sharpe_ratio': config.min_sharpe_ratio
        },
        'summary': {
            'files_processed': len(results),
            'total_strategies_tested': total_strategies,
            'valid_strategy_results': valid_strategies,
            'strategies_meeting_criteria': meeting_criteria,
            'average_consistency_score': round(avg_consistency, 3),
            'average_sharpe_ratio': round(avg_sharpe, 3)
        }
    }

def generate_strategy_ranking(results: list) -> dict:
    """Generate strategy performance ranking"""
    strategy_performance = {}
    
    for result in results:
        for strategy_result in result.get('results', []):
            strategy_name = strategy_result.get('strategy_name', 'Unknown')
            
            if strategy_name not in strategy_performance:
                strategy_performance[strategy_name] = {
                    'name': strategy_name,
                    'test_count': 0,
                    'total_consistency': 0,
                    'total_sharpe': 0,
                    'successful_tests': 0
                }
            
            perf = strategy_performance[strategy_name]
            perf['test_count'] += 1
            perf['total_consistency'] += strategy_result.get('consistency_score', 0)
            perf['total_sharpe'] += strategy_result.get('avg_sharpe_ratio', 0)
            
            if strategy_result.get('consistency_score', 0) >= 0.6:
                perf['successful_tests'] += 1
    
    # Calculate averages and rank
    ranking = []
    for strategy_name, perf in strategy_performance.items():
        if perf['test_count'] > 0:
            avg_consistency = perf['total_consistency'] / perf['test_count']
            avg_sharpe = perf['total_sharpe'] / perf['test_count']
            success_rate = perf['successful_tests'] / perf['test_count']
            
            ranking.append({
                'name': strategy_name,
                'avg_consistency': round(avg_consistency, 3),
                'avg_sharpe_ratio': round(avg_sharpe, 3),
                'success_rate': round(success_rate, 3),
                'test_count': perf['test_count'],
                'composite_score': round((avg_consistency + avg_sharpe + success_rate) / 3, 3)
            })
    
    # Sort by composite score
    ranking.sort(key=lambda x: x['composite_score'], reverse=True)
    
    return {
        'timestamp': datetime.now().isoformat(),
        'ranking': ranking
    }

def get_top_strategies(results: list, limit: int = 5) -> list:
    """Get top performing strategies"""
    all_strategies = []
    
    for result in results:
        for strategy_result in result.get('results', []):
            all_strategies.append({
                'name': strategy_result.get('strategy_name', 'Unknown'),
                'consistency': strategy_result.get('consistency_score', 0),
                'sharpe': strategy_result.get('avg_sharpe_ratio', 0),
                'symbol': result.get('symbol', 'Unknown')
            })
    
    # Sort by consistency score
    all_strategies.sort(key=lambda x: x['consistency'], reverse=True)
    
    return all_strategies[:limit]

if __name__ == "__main__":
    asyncio.run(main())