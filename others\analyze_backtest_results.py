#!/usr/bin/env python3
"""
Analyze backtest results to investigate issues:
1. ETERNAL-EQ having 139 rows with different strategies but 7 trades each
2. Low profitability distribution (11.4% profitable vs 88.6% non-profitable)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter

def analyze_backtest_results():
    """Comprehensive analysis of backtest results"""
    
    try:
        # Load the parquet file
        df = pd.read_parquet('data/backtest/enhanced_strategy_results.parquet')
        
        print("=" * 80)
        print("🔍 BACKTEST RESULTS ANALYSIS")
        print("=" * 80)
        
        print(f"📊 Dataset Overview:")
        print(f"   Total rows: {len(df):,}")
        print(f"   Total columns: {len(df.columns)}")
        print(f"   Unique strategies: {df['strategy_name'].nunique()}")
        print(f"   Unique symbols: {df['symbol'].nunique()}")
        print(f"   Unique timeframes: {df['timeframe'].nunique()}")
        
        # 1. INVESTIGATE ETERNAL-EQ ISSUE
        print("\n" + "=" * 80)
        print("🔍 ISSUE 1: ETERNAL-EQ Analysis")
        print("=" * 80)
        
        eternal_eq_data = df[df['symbol'] == 'ETERNAL-EQ']
        
        if len(eternal_eq_data) > 0:
            print(f"📈 ETERNAL-EQ Results:")
            print(f"   Total rows: {len(eternal_eq_data)}")
            print(f"   Unique strategies: {eternal_eq_data['strategy_name'].nunique()}")
            print(f"   Unique timeframes: {eternal_eq_data['timeframe'].nunique()}")
            print(f"   Unique R:R combos: {eternal_eq_data['risk_reward_combo'].nunique()}")
            
            # Check n_trades distribution for ETERNAL-EQ
            trades_distribution = eternal_eq_data['n_trades'].value_counts().sort_index()
            print(f"\n📊 n_trades distribution for ETERNAL-EQ:")
            for trades, count in trades_distribution.items():
                print(f"   {trades} trades: {count} occurrences ({count/len(eternal_eq_data)*100:.1f}%)")
            
            # Check if it's exactly 7 trades for all
            if len(trades_distribution) == 1 and 7 in trades_distribution.index:
                print(f"\n🚨 BUG CONFIRMED: All ETERNAL-EQ strategies have exactly 7 trades!")
                print(f"   This suggests a data processing issue or artificial limit.")
                
                # Show sample of ETERNAL-EQ data
                print(f"\n📋 Sample ETERNAL-EQ data:")
                sample_cols = ['strategy_name', 'timeframe', 'n_trades', 'ROI', 'accuracy', 'risk_reward_combo']
                print(eternal_eq_data[sample_cols].head(10).to_string(index=False))
                
            else:
                print(f"\n✅ ETERNAL-EQ has varied trade counts - likely not a bug")
        else:
            print(f"❌ ETERNAL-EQ not found in dataset")
        
        # 2. ANALYZE TRADE COUNT PATTERNS ACROSS ALL SYMBOLS
        print("\n" + "=" * 80)
        print("🔍 TRADE COUNT PATTERNS ANALYSIS")
        print("=" * 80)
        
        # Check if other symbols also have suspicious patterns
        trade_count_by_symbol = df.groupby('symbol')['n_trades'].agg(['mean', 'std', 'min', 'max', 'nunique']).round(2)
        trade_count_by_symbol = trade_count_by_symbol.sort_values('std')
        
        print(f"📊 Trade count statistics by symbol (sorted by std dev):")
        print(f"   Symbols with std=0 (same trade count for all strategies):")
        
        suspicious_symbols = trade_count_by_symbol[trade_count_by_symbol['std'] == 0]
        if len(suspicious_symbols) > 0:
            print(suspicious_symbols.head(10).to_string())
            print(f"\n🚨 Found {len(suspicious_symbols)} symbols with identical trade counts across all strategies!")
            print(f"   This suggests a systematic issue in trade simulation.")
        else:
            print(f"   ✅ No symbols found with identical trade counts")
        
        # Show overall trade count distribution
        print(f"\n📊 Overall n_trades distribution:")
        overall_trades_dist = df['n_trades'].value_counts().sort_index()
        for trades, count in overall_trades_dist.head(10).items():
            print(f"   {trades} trades: {count:,} occurrences ({count/len(df)*100:.1f}%)")
        
        # 3. PROFITABILITY ANALYSIS
        print("\n" + "=" * 80)
        print("🔍 ISSUE 2: LOW PROFITABILITY ANALYSIS")
        print("=" * 80)
        
        if 'is_profitable' in df.columns:
            profitable_count = sum(df['is_profitable'])
            total_count = len(df)
            profitable_pct = profitable_count / total_count * 100
            
            print(f"💰 Profitability Overview:")
            print(f"   Profitable strategies: {profitable_count:,} ({profitable_pct:.1f}%)")
            print(f"   Non-profitable strategies: {total_count - profitable_count:,} ({100-profitable_pct:.1f}%)")
            
            # Analyze what makes strategies profitable
            profitable_df = df[df['is_profitable'] == 1]
            non_profitable_df = df[df['is_profitable'] == 0]
            
            print(f"\n📊 Profitable vs Non-Profitable Comparison:")
            metrics = ['ROI', 'accuracy', 'sharpe_ratio', 'profit_factor', 'n_trades']
            
            for metric in metrics:
                if metric in df.columns:
                    prof_mean = profitable_df[metric].mean()
                    non_prof_mean = non_profitable_df[metric].mean()
                    print(f"   {metric}:")
                    print(f"     Profitable: {prof_mean:.3f}")
                    print(f"     Non-profitable: {non_prof_mean:.3f}")
                    print(f"     Difference: {prof_mean - non_prof_mean:.3f}")
            
            # Check ROI distribution
            print(f"\n📊 ROI Distribution Analysis:")
            roi_bins = [-100, -10, -5, 0, 3, 5, 10, 20, 100]  # 3% is the profit threshold
            roi_labels = ['<-10%', '-10 to -5%', '-5 to 0%', '0 to 3%', '3 to 5%', '5 to 10%', '10 to 20%', '>20%']
            
            df['roi_bin'] = pd.cut(df['ROI'], bins=roi_bins, labels=roi_labels, include_lowest=True)
            roi_distribution = df['roi_bin'].value_counts().sort_index()
            
            for bin_label, count in roi_distribution.items():
                pct = count / len(df) * 100
                print(f"   {bin_label}: {count:,} ({pct:.1f}%)")
            
            # Check if profit threshold is too high
            profit_threshold = 3.0  # From the code
            above_threshold = sum(df['ROI'] > profit_threshold)
            print(f"\n🎯 Profit Threshold Analysis:")
            print(f"   Current threshold: {profit_threshold}%")
            print(f"   Strategies above threshold: {above_threshold:,} ({above_threshold/len(df)*100:.1f}%)")
            
            # Suggest better thresholds
            for threshold in [0, 1, 2, 5]:
                above_thresh = sum(df['ROI'] > threshold)
                print(f"   If threshold was {threshold}%: {above_thresh:,} ({above_thresh/len(df)*100:.1f}%) would be profitable")
        
        # 4. STRATEGY PERFORMANCE ANALYSIS
        print("\n" + "=" * 80)
        print("🔍 STRATEGY PERFORMANCE ANALYSIS")
        print("=" * 80)
        
        # Top and bottom performing strategies
        strategy_performance = df.groupby('strategy_name').agg({
            'ROI': ['mean', 'std', 'count'],
            'accuracy': 'mean',
            'is_profitable': 'sum' if 'is_profitable' in df.columns else 'count'
        }).round(3)
        
        strategy_performance.columns = ['avg_roi', 'roi_std', 'count', 'avg_accuracy', 'profitable_count']
        strategy_performance['profitable_pct'] = (strategy_performance['profitable_count'] / strategy_performance['count'] * 100).round(1)
        strategy_performance = strategy_performance.sort_values('avg_roi', ascending=False)
        
        print(f"📊 Top 10 Best Performing Strategies:")
        print(strategy_performance.head(10)[['avg_roi', 'avg_accuracy', 'profitable_pct', 'count']].to_string())
        
        print(f"\n📊 Bottom 10 Worst Performing Strategies:")
        print(strategy_performance.tail(10)[['avg_roi', 'avg_accuracy', 'profitable_pct', 'count']].to_string())
        
        # 5. POTENTIAL ISSUES IDENTIFICATION
        print("\n" + "=" * 80)
        print("🔍 POTENTIAL ISSUES IDENTIFICATION")
        print("=" * 80)
        
        issues_found = []
        
        # Check for identical trade counts
        if len(suspicious_symbols) > 0:
            issues_found.append(f"🚨 {len(suspicious_symbols)} symbols have identical trade counts across all strategies")
        
        # Check for unrealistic metrics
        if df['sharpe_ratio'].max() > 10 or df['sharpe_ratio'].min() < -10:
            issues_found.append(f"🚨 Unrealistic Sharpe ratios detected (range: {df['sharpe_ratio'].min():.2f} to {df['sharpe_ratio'].max():.2f})")
        
        # Check for too many zero ROI strategies
        zero_roi_count = sum(df['ROI'] == 0)
        if zero_roi_count > len(df) * 0.1:  # More than 10%
            issues_found.append(f"🚨 Too many strategies with 0% ROI: {zero_roi_count:,} ({zero_roi_count/len(df)*100:.1f}%)")
        
        # Check for identical accuracy across strategies
        if df['accuracy'].nunique() < 10:
            issues_found.append(f"🚨 Very few unique accuracy values: {df['accuracy'].nunique()}")
        
        if issues_found:
            print(f"⚠️ Issues Found:")
            for issue in issues_found:
                print(f"   {issue}")
        else:
            print(f"✅ No obvious issues detected")
        
        # 6. RECOMMENDATIONS
        print("\n" + "=" * 80)
        print("💡 RECOMMENDATIONS")
        print("=" * 80)
        
        print(f"1. 🔧 Fix Trade Simulation Logic:")
        print(f"   - The identical trade counts suggest the signal generation or")
        print(f"     trade simulation is not working properly for some symbols")
        print(f"   - Check if data quality issues are causing this")
        
        print(f"\n2. 📊 Adjust Profit Threshold:")
        print(f"   - Current 3% threshold may be too high for realistic trading")
        print(f"   - Consider lowering to 1-2% or using different criteria")
        
        print(f"\n3. 🎯 Improve Strategy Logic:")
        print(f"   - Current strategies may be too simplistic")
        print(f"   - Add more sophisticated entry/exit conditions")
        print(f"   - Consider market regime filtering")
        
        print(f"\n4. 🔍 Data Quality Check:")
        print(f"   - Verify input data has sufficient variety")
        print(f"   - Check for missing or corrupted price data")
        print(f"   - Ensure volume data is realistic")
        
        return df
        
    except Exception as e:
        print(f"❌ Error analyzing results: {e}")
        return None

if __name__ == "__main__":
    df = analyze_backtest_results()
