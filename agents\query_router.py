#!/usr/bin/env python3
"""
Natural Language Query Router for LLM Interface Agent

This module provides intelligent query classification and routing to appropriate
agents based on natural language understanding, intent detection, and context analysis.

Features:
[DEBUG] 1. Intent Detection and Classification
- Multi-level intent hierarchy
- Context-aware classification
- Confidence scoring for routing decisions

[TARGET] 2. Agent Routing Logic
- Dynamic agent selection based on query type
- Load balancing and availability checking
- Fallback routing for edge cases

[STATUS] 3. Query Analysis and Enhancement
- Entity extraction (symbols, strategies, dates)
- Parameter parsing and validation
- Query enrichment with context

🧠 4. Learning and Adaptation
- Performance feedback integration
- Routing optimization based on success rates
- Dynamic pattern learning

Author: AI Assistant
Date: 2025-01-16
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import yaml
from collections import defaultdict, Counter
import json

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] ROUTING DEFINITIONS
# ═══════════════════════════════════════════════════════════════════════════════

class IntentCategory(Enum):
    """High-level intent categories"""
    PERFORMANCE_ANALYSIS = "performance_analysis"
    TRADING_EXECUTION = "trading_execution"
    MARKET_MONITORING = "market_monitoring"
    RISK_MANAGEMENT = "risk_management"
    SIGNAL_GENERATION = "signal_generation"
    CODE_ASSISTANCE = "code_assistance"
    CONFIGURATION = "configuration"
    SYSTEM_STATUS = "system_status"
    GENERAL_QUERY = "general_query"

class QueryType(Enum):
    """Specific query types"""
    # Performance Analysis
    ROI_QUERY = "roi_query"
    STRATEGY_PERFORMANCE = "strategy_performance"
    DRAWDOWN_ANALYSIS = "drawdown_analysis"
    BACKTEST_RESULTS = "backtest_results"
    
    # Trading Execution
    ORDER_PLACEMENT = "order_placement"
    ORDER_STATUS = "order_status"
    TRADE_HISTORY = "trade_history"
    EXECUTION_QUALITY = "execution_quality"
    
    # Market Monitoring
    MARKET_REGIME = "market_regime"
    PRICE_QUERY = "price_query"
    INDICATOR_ANALYSIS = "indicator_analysis"
    SIGNAL_STATUS = "signal_status"
    
    # Risk Management
    PORTFOLIO_STATUS = "portfolio_status"
    RISK_LIMITS = "risk_limits"
    POSITION_SIZING = "position_sizing"
    MARGIN_STATUS = "margin_status"
    
    # Code Assistance
    CODE_GENERATION = "code_generation"
    BUG_FIXING = "bug_fixing"
    CODE_EXPLANATION = "code_explanation"
    OPTIMIZATION = "optimization"
    
    # Configuration
    STRATEGY_CONFIG = "strategy_config"
    PARAMETER_TUNING = "parameter_tuning"
    SYSTEM_CONFIG = "system_config"
    
    # System
    STATUS_CHECK = "status_check"
    HEALTH_CHECK = "health_check"
    PERFORMANCE_METRICS = "performance_metrics"

@dataclass
class RoutingDecision:
    """Routing decision with metadata"""
    agent_name: str
    confidence: float
    intent_category: IntentCategory
    query_type: QueryType
    extracted_entities: Dict[str, Any]
    routing_reason: str
    fallback_agents: List[str]
    estimated_complexity: float
    requires_real_time: bool

@dataclass
class EntityExtraction:
    """Extracted entities from query"""
    symbols: List[str]
    strategies: List[str]
    timeframes: List[str]
    dates: List[str]
    numbers: List[float]
    indicators: List[str]
    actions: List[str]

# ═══════════════════════════════════════════════════════════════════════════════
# 🧠 QUERY ROUTER CLASS
# ═══════════════════════════════════════════════════════════════════════════════

class QueryRouter:
    """
    Natural Language Query Router for intelligent agent selection
    
    Analyzes user queries and routes them to the most appropriate agent
    based on intent detection, entity extraction, and context analysis.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize query router"""
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self.config = self._load_config(config_path) if config_path else {}
        
        # Initialize routing patterns
        self.intent_patterns = self._initialize_intent_patterns()
        self.entity_patterns = self._initialize_entity_patterns()
        self.agent_capabilities = self._initialize_agent_capabilities()
        
        # Performance tracking
        self.routing_history = []
        self.agent_performance = defaultdict(list)
        self.pattern_effectiveness = defaultdict(float)
        
        # Context management
        self.session_contexts = defaultdict(dict)
        
        self.logger.info("🔀 Query Router initialized")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file"""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.warning(f"[WARN] Could not load config: {e}")
            return {}
    
    def _initialize_intent_patterns(self) -> Dict[IntentCategory, Dict[str, Any]]:
        """Initialize intent detection patterns"""
        return {
            IntentCategory.PERFORMANCE_ANALYSIS: {
                "keywords": [
                    "performance", "roi", "return", "profit", "loss", "pnl", "sharpe",
                    "drawdown", "accuracy", "win rate", "backtest", "results", "analysis"
                ],
                "patterns": [
                    r"what.*roi", r"show.*performance", r"how.*profit", r"strategy.*results",
                    r"backtest.*analysis", r"performance.*of", r"roi.*for"
                ],
                "agents": ["performance_analysis_agent"],
                "weight": 1.0
            },
            
            IntentCategory.TRADING_EXECUTION: {
                "keywords": [
                    "buy", "sell", "order", "trade", "execute", "place", "cancel",
                    "modify", "position", "quantity", "price", "fill"
                ],
                "patterns": [
                    r"buy.*shares?", r"sell.*position", r"place.*order", r"execute.*trade",
                    r"cancel.*order", r"modify.*position"
                ],
                "agents": ["execution_agent"],
                "weight": 1.0
            },
            
            IntentCategory.MARKET_MONITORING: {
                "keywords": [
                    "market", "price", "ltp", "volume", "trend", "regime", "indicator",
                    "signal", "technical", "chart", "pattern", "momentum"
                ],
                "patterns": [
                    r"market.*status", r"current.*price", r"trend.*analysis", r"signal.*active",
                    r"indicator.*value", r"market.*regime"
                ],
                "agents": ["market_monitoring_agent"],
                "weight": 1.0
            },
            
            IntentCategory.RISK_MANAGEMENT: {
                "keywords": [
                    "risk", "portfolio", "margin", "capital", "allocation", "exposure",
                    "limit", "stop loss", "position size", "drawdown", "funds"
                ],
                "patterns": [
                    r"portfolio.*status", r"risk.*limit", r"margin.*available", r"capital.*allocation",
                    r"position.*size", r"stop.*loss"
                ],
                "agents": ["risk_agent"],
                "weight": 1.0
            },
            
            IntentCategory.SIGNAL_GENERATION: {
                "keywords": [
                    "signal", "strategy", "generate", "scan", "alert", "trigger",
                    "condition", "entry", "exit", "setup"
                ],
                "patterns": [
                    r"generate.*signal", r"scan.*for", r"strategy.*trigger", r"entry.*signal",
                    r"exit.*condition"
                ],
                "agents": ["signal_generation_agent"],
                "weight": 1.0
            },
            
            IntentCategory.CODE_ASSISTANCE: {
                "keywords": [
                    "code", "function", "class", "script", "algorithm", "implement",
                    "fix", "debug", "error", "bug", "optimize", "refactor"
                ],
                "patterns": [
                    r"fix.*code", r"debug.*error", r"implement.*function", r"optimize.*algorithm",
                    r"generate.*code", r"explain.*function"
                ],
                "agents": ["code_generation"],
                "weight": 1.0
            },
            
            IntentCategory.CONFIGURATION: {
                "keywords": [
                    "config", "configuration", "setting", "parameter", "yaml", "tune",
                    "optimize", "adjust", "modify", "update"
                ],
                "patterns": [
                    r"config.*file", r"update.*setting", r"modify.*parameter", r"tune.*strategy",
                    r"yaml.*error"
                ],
                "agents": ["config_management"],
                "weight": 1.0
            },
            
            IntentCategory.SYSTEM_STATUS: {
                "keywords": [
                    "status", "health", "running", "active", "available", "online",
                    "system", "agent", "service", "uptime"
                ],
                "patterns": [
                    r"system.*status", r"agent.*running", r"health.*check", r"service.*active"
                ],
                "agents": ["system_monitor"],
                "weight": 0.8
            }
        }
    
    def _initialize_entity_patterns(self) -> Dict[str, List[str]]:
        """Initialize entity extraction patterns"""
        return {
            "symbols": [
                r"\b[A-Z]{2,10}\b",  # Stock symbols
                r"\b[A-Z]+[-][A-Z]+\b",  # Hyphenated symbols
                r"NIFTY\s*\d*", r"SENSEX", r"BANKNIFTY"  # Index symbols
            ],
            "strategies": [
                r"RSI[_\s]*\w*", r"MACD[_\s]*\w*", r"EMA[_\s]*\w*", r"SMA[_\s]*\w*",
                r"SuperTrend", r"VWAP", r"Bollinger", r"Stochastic", r"CPR",
                r"Donchian", r"Momentum", r"Breakout", r"Scalping"
            ],
            "timeframes": [
                r"\d+[mh]", r"\d+\s*min", r"\d+\s*hour", r"daily", r"weekly",
                r"1m", r"5m", r"15m", r"1h", r"4h", r"1d"
            ],
            "dates": [
                r"\d{4}-\d{2}-\d{2}", r"\d{2}/\d{2}/\d{4}", r"today", r"yesterday",
                r"last\s+week", r"this\s+month", r"last\s+\d+\s+days"
            ],
            "numbers": [
                r"\d+\.?\d*%", r"Rs.\s*\d+", r"\d+\s*lakh", r"\d+\s*crore",
                r"\d+\.?\d*"
            ],
            "indicators": [
                r"RSI", r"MACD", r"EMA", r"SMA", r"ATR", r"ADX", r"CCI",
                r"Stochastic", r"Williams", r"MFI", r"OBV"
            ],
            "actions": [
                r"buy", r"sell", r"hold", r"exit", r"enter", r"close",
                r"open", r"modify", r"cancel", r"place"
            ]
        }
    
    def _initialize_agent_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Initialize agent capability definitions"""
        return {
            "performance_analysis_agent": {
                "capabilities": [
                    "roi_calculation", "strategy_analysis", "drawdown_analysis",
                    "performance_metrics", "backtest_results", "comparison_analysis"
                ],
                "data_sources": ["trade_history", "backtest_data", "performance_db"],
                "response_time": "medium",
                "complexity_handling": "high"
            },
            "execution_agent": {
                "capabilities": [
                    "order_placement", "order_management", "trade_execution",
                    "position_tracking", "execution_quality"
                ],
                "data_sources": ["broker_api", "order_book", "trade_log"],
                "response_time": "fast",
                "complexity_handling": "medium"
            },
            "market_monitoring_agent": {
                "capabilities": [
                    "price_monitoring", "indicator_calculation", "regime_detection",
                    "signal_tracking", "market_analysis"
                ],
                "data_sources": ["market_data", "price_feeds", "indicator_cache"],
                "response_time": "fast",
                "complexity_handling": "medium"
            },
            "risk_agent": {
                "capabilities": [
                    "risk_assessment", "portfolio_analysis", "margin_calculation",
                    "position_sizing", "limit_monitoring"
                ],
                "data_sources": ["portfolio_data", "risk_metrics", "margin_info"],
                "response_time": "fast",
                "complexity_handling": "high"
            },
            "signal_generation_agent": {
                "capabilities": [
                    "signal_generation", "strategy_evaluation", "condition_monitoring",
                    "alert_management", "scan_execution"
                ],
                "data_sources": ["market_data", "strategy_config", "signal_history"],
                "response_time": "medium",
                "complexity_handling": "high"
            }
        }

    def route_query(self, query: str, session_id: Optional[str] = None,
                   context: Optional[Dict[str, Any]] = None) -> RoutingDecision:
        """
        Route query to appropriate agent

        Args:
            query: User query string
            session_id: Optional session identifier for context
            context: Optional additional context

        Returns:
            RoutingDecision with agent selection and metadata
        """
        try:
            # Extract entities from query
            entities = self._extract_entities(query)

            # Classify intent
            intent_scores = self._classify_intent(query, entities, context)

            # Determine query type
            query_type = self._determine_query_type(query, intent_scores, entities)

            # Select best agent
            agent_decision = self._select_agent(intent_scores, query_type, entities, context)

            # Estimate complexity and requirements
            complexity = self._estimate_complexity(query, entities)
            requires_real_time = self._requires_real_time(query, query_type)

            # Create routing decision
            decision = RoutingDecision(
                agent_name=agent_decision['agent'],
                confidence=agent_decision['confidence'],
                intent_category=agent_decision['intent'],
                query_type=query_type,
                extracted_entities=entities.__dict__,
                routing_reason=agent_decision['reason'],
                fallback_agents=agent_decision['fallbacks'],
                estimated_complexity=complexity,
                requires_real_time=requires_real_time
            )

            # Record routing decision
            self._record_routing(query, decision, session_id)

            self.logger.info(f"[TARGET] Routed to {decision.agent_name} (confidence: {decision.confidence:.2f})")

            return decision

        except Exception as e:
            self.logger.error(f"[ERROR] Error routing query: {e}")
            # Fallback to general agent
            return RoutingDecision(
                agent_name="general_response",
                confidence=0.3,
                intent_category=IntentCategory.GENERAL_QUERY,
                query_type=QueryType.STATUS_CHECK,
                extracted_entities={},
                routing_reason=f"Error in routing: {str(e)}",
                fallback_agents=[],
                estimated_complexity=0.5,
                requires_real_time=False
            )

    def _extract_entities(self, query: str) -> EntityExtraction:
        """Extract entities from query"""
        entities = EntityExtraction(
            symbols=[], strategies=[], timeframes=[], dates=[],
            numbers=[], indicators=[], actions=[]
        )

        try:
            for entity_type, patterns in self.entity_patterns.items():
                matches = []
                for pattern in patterns:
                    found = re.findall(pattern, query, re.IGNORECASE)
                    matches.extend(found)

                # Clean and deduplicate matches
                cleaned_matches = list(set([match.strip() for match in matches if match.strip()]))
                setattr(entities, entity_type, cleaned_matches)

            # Special processing for numbers
            number_matches = []
            for match in entities.numbers:
                try:
                    # Extract numeric value
                    numeric_part = re.search(r'\d+\.?\d*', match)
                    if numeric_part:
                        number_matches.append(float(numeric_part.group()))
                except ValueError:
                    continue
            entities.numbers = number_matches

        except Exception as e:
            self.logger.warning(f"[WARN] Error extracting entities: {e}")

        return entities

    def _classify_intent(self, query: str, entities: EntityExtraction,
                        context: Optional[Dict[str, Any]]) -> Dict[IntentCategory, float]:
        """Classify query intent with confidence scores"""
        intent_scores = {}
        query_lower = query.lower()

        for intent, config in self.intent_patterns.items():
            score = 0.0

            # Keyword matching
            keyword_matches = sum(1 for keyword in config['keywords']
                                if keyword in query_lower)
            score += keyword_matches * 0.3

            # Pattern matching
            pattern_matches = sum(1 for pattern in config.get('patterns', [])
                                if re.search(pattern, query_lower))
            score += pattern_matches * 0.5

            # Entity-based scoring
            entity_boost = self._calculate_entity_boost(intent, entities)
            score += entity_boost

            # Context-based scoring
            if context:
                context_boost = self._calculate_context_boost(intent, context)
                score += context_boost

            # Apply weight
            score *= config.get('weight', 1.0)

            if score > 0:
                intent_scores[intent] = score

        # Normalize scores
        if intent_scores:
            max_score = max(intent_scores.values())
            intent_scores = {intent: score/max_score for intent, score in intent_scores.items()}

        return intent_scores

    def _determine_query_type(self, query: str, intent_scores: Dict[IntentCategory, float],
                             entities: EntityExtraction) -> QueryType:
        """Determine specific query type"""
        query_lower = query.lower()

        # Get primary intent
        if intent_scores:
            primary_intent = max(intent_scores.items(), key=lambda x: x[1])[0]
        else:
            primary_intent = IntentCategory.GENERAL_QUERY

        # Map intent to specific query type based on keywords and entities
        if primary_intent == IntentCategory.PERFORMANCE_ANALYSIS:
            if any(word in query_lower for word in ['roi', 'return', 'profit']):
                return QueryType.ROI_QUERY
            elif any(word in query_lower for word in ['strategy', 'performance']):
                return QueryType.STRATEGY_PERFORMANCE
            elif any(word in query_lower for word in ['drawdown', 'loss']):
                return QueryType.DRAWDOWN_ANALYSIS
            else:
                return QueryType.BACKTEST_RESULTS

        elif primary_intent == IntentCategory.TRADING_EXECUTION:
            if any(word in query_lower for word in ['buy', 'sell', 'place']):
                return QueryType.ORDER_PLACEMENT
            elif any(word in query_lower for word in ['status', 'order']):
                return QueryType.ORDER_STATUS
            elif any(word in query_lower for word in ['history', 'trades']):
                return QueryType.TRADE_HISTORY
            else:
                return QueryType.EXECUTION_QUALITY

        elif primary_intent == IntentCategory.MARKET_MONITORING:
            if any(word in query_lower for word in ['regime', 'trend']):
                return QueryType.MARKET_REGIME
            elif any(word in query_lower for word in ['price', 'ltp', 'quote']):
                return QueryType.PRICE_QUERY
            elif entities.indicators:
                return QueryType.INDICATOR_ANALYSIS
            else:
                return QueryType.SIGNAL_STATUS

        elif primary_intent == IntentCategory.RISK_MANAGEMENT:
            if any(word in query_lower for word in ['portfolio', 'holdings']):
                return QueryType.PORTFOLIO_STATUS
            elif any(word in query_lower for word in ['risk', 'limit']):
                return QueryType.RISK_LIMITS
            elif any(word in query_lower for word in ['margin', 'funds']):
                return QueryType.MARGIN_STATUS
            else:
                return QueryType.POSITION_SIZING

        elif primary_intent == IntentCategory.CODE_ASSISTANCE:
            if any(word in query_lower for word in ['generate', 'create', 'implement']):
                return QueryType.CODE_GENERATION
            elif any(word in query_lower for word in ['fix', 'debug', 'error']):
                return QueryType.BUG_FIXING
            elif any(word in query_lower for word in ['explain', 'how', 'what']):
                return QueryType.CODE_EXPLANATION
            else:
                return QueryType.OPTIMIZATION

        else:
            return QueryType.STATUS_CHECK

    def _select_agent(self, intent_scores: Dict[IntentCategory, float], query_type: QueryType,
                     entities: EntityExtraction, context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Select the best agent for the query"""

        # Get primary intent
        if intent_scores:
            primary_intent = max(intent_scores.items(), key=lambda x: x[1])[0]
            confidence = max(intent_scores.values())
        else:
            primary_intent = IntentCategory.GENERAL_QUERY
            confidence = 0.3

        # Map intent to agent
        intent_agent_map = {
            IntentCategory.PERFORMANCE_ANALYSIS: "performance_analysis_agent",
            IntentCategory.TRADING_EXECUTION: "execution_agent",
            IntentCategory.MARKET_MONITORING: "market_monitoring_agent",
            IntentCategory.RISK_MANAGEMENT: "risk_agent",
            IntentCategory.SIGNAL_GENERATION: "signal_generation_agent",
            IntentCategory.CODE_ASSISTANCE: "code_generation",
            IntentCategory.CONFIGURATION: "config_management",
            IntentCategory.SYSTEM_STATUS: "system_monitor"
        }

        primary_agent = intent_agent_map.get(primary_intent, "general_response")

        # Determine fallback agents
        fallbacks = []
        for intent, score in sorted(intent_scores.items(), key=lambda x: x[1], reverse=True)[1:3]:
            fallback_agent = intent_agent_map.get(intent)
            if fallback_agent and fallback_agent != primary_agent:
                fallbacks.append(fallback_agent)

        # Create reasoning
        reason = f"Primary intent: {primary_intent.value} (confidence: {confidence:.2f})"
        if entities.symbols:
            reason += f", Symbols: {entities.symbols[:3]}"
        if entities.strategies:
            reason += f", Strategies: {entities.strategies[:2]}"

        return {
            'agent': primary_agent,
            'confidence': confidence,
            'intent': primary_intent,
            'reason': reason,
            'fallbacks': fallbacks
        }

    def _calculate_entity_boost(self, intent: IntentCategory, entities: EntityExtraction) -> float:
        """Calculate entity-based score boost"""
        boost = 0.0

        if intent == IntentCategory.PERFORMANCE_ANALYSIS:
            boost += len(entities.strategies) * 0.2
            boost += len(entities.symbols) * 0.1

        elif intent == IntentCategory.TRADING_EXECUTION:
            boost += len(entities.actions) * 0.3
            boost += len(entities.symbols) * 0.2

        elif intent == IntentCategory.MARKET_MONITORING:
            boost += len(entities.indicators) * 0.3
            boost += len(entities.symbols) * 0.2

        elif intent == IntentCategory.RISK_MANAGEMENT:
            boost += len(entities.numbers) * 0.2

        elif intent == IntentCategory.CODE_ASSISTANCE:
            # Code assistance doesn't rely heavily on trading entities
            boost += 0.1 if entities.strategies else 0.0

        return min(boost, 1.0)  # Cap at 1.0

    def _calculate_context_boost(self, intent: IntentCategory, context: Dict[str, Any]) -> float:
        """Calculate context-based score boost"""
        boost = 0.0

        # Previous query context
        if 'previous_intent' in context:
            if context['previous_intent'] == intent.value:
                boost += 0.2  # Boost for intent continuity

        # Session context
        if 'session_focus' in context:
            if context['session_focus'] == intent.value:
                boost += 0.3

        # Time-based context
        if 'market_hours' in context:
            if context['market_hours'] and intent in [
                IntentCategory.TRADING_EXECUTION,
                IntentCategory.MARKET_MONITORING
            ]:
                boost += 0.2

        return min(boost, 0.5)  # Cap at 0.5

    def _estimate_complexity(self, query: str, entities: EntityExtraction) -> float:
        """Estimate query complexity"""
        complexity = 0.0

        # Base complexity from query length
        complexity += min(len(query.split()) / 50, 0.3)

        # Entity complexity
        complexity += len(entities.symbols) * 0.05
        complexity += len(entities.strategies) * 0.1
        complexity += len(entities.indicators) * 0.08

        # Keyword complexity indicators
        complex_keywords = [
            'analyze', 'compare', 'optimize', 'calculate', 'generate',
            'comprehensive', 'detailed', 'advanced', 'complex'
        ]

        query_lower = query.lower()
        complexity += sum(0.1 for keyword in complex_keywords if keyword in query_lower)

        return min(complexity, 1.0)

    def _requires_real_time(self, query: str, query_type: QueryType) -> bool:
        """Determine if query requires real-time data"""
        real_time_indicators = [
            'current', 'now', 'live', 'real-time', 'latest', 'today'
        ]

        real_time_query_types = [
            QueryType.PRICE_QUERY, QueryType.MARKET_REGIME, QueryType.SIGNAL_STATUS,
            QueryType.ORDER_STATUS, QueryType.PORTFOLIO_STATUS, QueryType.MARGIN_STATUS
        ]

        query_lower = query.lower()

        return (any(indicator in query_lower for indicator in real_time_indicators) or
                query_type in real_time_query_types)

    def _record_routing(self, query: str, decision: RoutingDecision, session_id: Optional[str]):
        """Record routing decision for analysis"""
        record = {
            'timestamp': datetime.now().isoformat(),
            'query': query[:100],  # Truncate for privacy
            'agent': decision.agent_name,
            'confidence': decision.confidence,
            'intent': decision.intent_category.value,
            'query_type': decision.query_type.value,
            'complexity': decision.estimated_complexity,
            'real_time': decision.requires_real_time,
            'session_id': session_id
        }

        self.routing_history.append(record)

        # Keep only last 1000 records
        if len(self.routing_history) > 1000:
            self.routing_history = self.routing_history[-1000:]

    def record_agent_performance(self, agent_name: str, performance_score: float):
        """Record agent performance feedback"""
        if 0.0 <= performance_score <= 1.0:
            self.agent_performance[agent_name].append(performance_score)

            # Keep only last 100 performance records per agent
            if len(self.agent_performance[agent_name]) > 100:
                self.agent_performance[agent_name] = self.agent_performance[agent_name][-100:]

    def get_routing_stats(self) -> Dict[str, Any]:
        """Get routing statistics"""
        if not self.routing_history:
            return {"message": "No routing history available"}

        agent_usage = Counter(record['agent'] for record in self.routing_history)
        intent_distribution = Counter(record['intent'] for record in self.routing_history)
        avg_confidence = sum(record['confidence'] for record in self.routing_history) / len(self.routing_history)

        return {
            'total_queries': len(self.routing_history),
            'agent_usage': dict(agent_usage),
            'intent_distribution': dict(intent_distribution),
            'average_confidence': avg_confidence,
            'real_time_queries': sum(1 for record in self.routing_history if record['real_time']),
            'complex_queries': sum(1 for record in self.routing_history if record['complexity'] > 0.7),
            'agent_performance': {
                agent: {
                    'count': len(scores),
                    'average': sum(scores) / len(scores) if scores else 0
                }
                for agent, scores in self.agent_performance.items()
            }
        }

    def get_agent_recommendations(self, query: str) -> List[Dict[str, Any]]:
        """Get agent recommendations with confidence scores"""
        entities = self._extract_entities(query)
        intent_scores = self._classify_intent(query, entities, None)

        recommendations = []
        for intent, score in sorted(intent_scores.items(), key=lambda x: x[1], reverse=True):
            agent_config = self.intent_patterns[intent]
            agents = agent_config.get('agents', [])

            for agent in agents:
                if agent in self.agent_capabilities:
                    capabilities = self.agent_capabilities[agent]
                    recommendations.append({
                        'agent': agent,
                        'confidence': score,
                        'intent': intent.value,
                        'capabilities': capabilities['capabilities'],
                        'response_time': capabilities['response_time'],
                        'complexity_handling': capabilities['complexity_handling']
                    })

        return recommendations[:5]  # Top 5 recommendations

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TESTING AND DEMO FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def demo_query_routing():
    """Demo function to test query routing"""
    router = QueryRouter()

    test_queries = [
        "What's the ROI of my RSI strategy on RELIANCE last week?",
        "Buy 100 shares of ADANIPORTS at market price",
        "Show me the current market regime and trend analysis",
        "What's my portfolio status and available margin?",
        "Generate signals for momentum breakout strategies",
        "Fix the error in my MACD strategy code",
        "Update the RSI threshold in strategy configuration",
        "Is the system running and all agents active?",
        "Explain how the SuperTrend indicator calculation works",
        "Calculate position size for 1% risk on TATASTEEL trade"
    ]

    print("🔀 Query Routing Demo")
    print("=" * 60)

    for query in test_queries:
        print(f"\n📝 Query: {query}")
        decision = router.route_query(query)

        print(f"[TARGET] Agent: {decision.agent_name}")
        print(f"[TARGET] Confidence: {decision.confidence:.2f}")
        print(f"🧠 Intent: {decision.intent_category.value}")
        print(f"[STATUS] Query Type: {decision.query_type.value}")
        print(f"[CONFIG] Complexity: {decision.estimated_complexity:.2f}")
        print(f"[FAST] Real-time: {decision.requires_real_time}")
        print(f"💭 Reason: {decision.routing_reason}")

        if decision.extracted_entities:
            entities = decision.extracted_entities
            if entities.get('symbols'):
                print(f"[METRICS] Symbols: {entities['symbols']}")
            if entities.get('strategies'):
                print(f"[TARGET] Strategies: {entities['strategies']}")
            if entities.get('indicators'):
                print(f"[STATUS] Indicators: {entities['indicators']}")

        if decision.fallback_agents:
            print(f"[WORKFLOW] Fallbacks: {decision.fallback_agents}")

    print("\n[STATUS] Routing Statistics:")
    stats = router.get_routing_stats()
    print(f"Total Queries: {stats['total_queries']}")
    print(f"Agent Usage: {stats['agent_usage']}")
    print(f"Average Confidence: {stats['average_confidence']:.2f}")
    print(f"Real-time Queries: {stats['real_time_queries']}")
    print(f"Complex Queries: {stats['complex_queries']}")

if __name__ == "__main__":
    demo_query_routing()
