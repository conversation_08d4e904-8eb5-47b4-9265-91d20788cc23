---
description: Repository Information Overview
alwaysApply: true
---

# Intraday AI Trading System Information

## Summary
A comprehensive AI-powered trading system for intraday equity trading, featuring multiple agents for market monitoring, signal generation, risk management, and execution. The system supports both paper trading and live trading modes with a focus on high-performance data processing and machine learning.

## Structure
- **agents/**: Trading system agents (AI training, signal generation, execution, etc.)
- **config/**: Configuration files for different system components
- **data/**: Market data storage and model data
- **logs/**: System and agent logs
- **reports/**: Trading reports and performance metrics
- **scripts/**: Utility scripts for data downloading and processing
- **test/**: Test suite for system components
- **utils/**: Utility modules for data processing, websocket management, etc.

## Language & Runtime
**Language**: Python
**Version**: 3.11 (based on Dockerfile)
**Build System**: Docker multi-stage build
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- **ML/AI**: lightgbm (4.3.0+), scikit-learn (1.4.0+), xgboost (2.0.0+), catboost (1.2.0+), torch (2.2.0+)
- **Data Processing**: polars (0.20.0+), pyarrow (15.0.0+), pandas (2.2.0+)
- **Technical Analysis**: polars-talib (0.1.0+), ta (0.10.2+)
- **Broker Integration**: smartapi-python (1.4.8+), websocket-client (1.7.0+)
- **Web Framework**: fastapi (0.109.0+), uvicorn (0.27.0+)
- **LLM Integration**: langchain (0.1.0+)

**Development Dependencies**:
- pytest (8.0.0+), pytest-asyncio (0.23.0+)
- black (24.0.0+), flake8 (7.0.0+), mypy (1.8.0+)

## Build & Installation
```bash
# CPU-only installation
pip install -r requirements.txt

# GPU acceleration (NVIDIA GPU with CUDA 11.8+)
pip install -r requirements.txt  # with GPU sections uncommented

# Run the system
python main.py --agent execution --trading-mode paper
```

## Docker
**Dockerfile**: Multi-stage build with base, development, and production stages
**Image**: Python 3.11-slim base
**Configuration**: Docker Compose with multiple services:
- execution-agent: Main execution agent
- market-monitoring: Market data monitoring
- signal-generation: Trading signal generation
- risk-management: Risk management and position sizing
- dev: Development environment with mounted volumes

**Run Command**:
```bash
docker-compose up -d execution-agent
```

## Main Components
**Entry Point**: main.py (TradingSystemOrchestrator)
**Trading Agents**:
- AI Training Agent: Machine learning model training
- Signal Generation Agent: Trading signal generation
- Execution Agent: Order execution and management
- Market Monitoring Agent: Real-time market data processing
- Risk Management Agent: Position sizing and risk control

**Workflows**:
- full_pipeline: Complete trading system workflow
- training_pipeline: Model training workflow
- live_trading: Real-time trading workflow
- data_pipeline: Data processing workflow

## Testing
**Framework**: pytest with custom fixtures
**Test Location**: test/ directory
**Configuration**: conftest.py with fixtures for backtesting
**Key Test Areas**:
- Backtesting performance
- Strategy generation
- Market data processing
- Risk management
- Paper trading simulation

**Run Command**:
```bash
pytest test/
```

## Trading Modes
**Paper Trading**: Simulated trading with configurable parameters
- Initial balance, commission rates, position sizing
- Risk limits and margin requirements

**Live Trading**: Real trading with broker API integration
- Angel One SmartAPI integration
- Websocket data streaming
- Order execution and management