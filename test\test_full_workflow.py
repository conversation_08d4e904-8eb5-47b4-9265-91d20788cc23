#!/usr/bin/env python3
"""
Test script for full workflow mode
Tests the fixes for demo vs full mode separation
"""

import asyncio
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from run_paper_trading_workflow import PaperTradingWorkflow

async def test_full_workflow():
    """Test the full workflow mode"""
    print("Testing Full Workflow Mode...")
    print("=" * 50)
    
    # Create workflow instance
    workflow = PaperTradingWorkflow()
    
    # Test full mode (no demo simulation)
    print("\n[TEST] Testing Pre-market Preparation (Full Mode)")
    success = await workflow.run_pre_market_preparation(demo_mode=False)
    print(f"Pre-market Result: {'SUCCESS' if success else 'FAILED'}")
    
    print("\n[TEST] Testing Live Trading (Full Mode)")
    success = await workflow.run_live_trading(demo_mode=False)
    print(f"Live Trading Result: {'SUCCESS' if success else 'FAILED'}")
    
    print("\n[TEST] Testing Post-market Analysis (Full Mode)")
    success = await workflow.run_post_market_analysis(demo_mode=False)
    print(f"Post-market Result: {'SUCCESS' if success else 'FAILED'}")
    
    # Print final status
    workflow.print_status_report()

async def test_demo_workflow():
    """Test the demo workflow mode"""
    print("\n\nTesting Demo Workflow Mode...")
    print("=" * 50)
    
    # Create workflow instance
    workflow = PaperTradingWorkflow()
    
    # Test demo mode (with simulation)
    print("\n[TEST] Testing Pre-market Preparation (Demo Mode)")
    success = await workflow.run_pre_market_preparation(demo_mode=True)
    print(f"Pre-market Result: {'SUCCESS' if success else 'FAILED'}")
    
    print("\n[TEST] Testing Live Trading (Demo Mode)")
    success = await workflow.run_live_trading(demo_mode=True)
    print(f"Live Trading Result: {'SUCCESS' if success else 'FAILED'}")
    
    print("\n[TEST] Testing Post-market Analysis (Demo Mode)")
    success = await workflow.run_post_market_analysis(demo_mode=True)
    print(f"Post-market Result: {'SUCCESS' if success else 'FAILED'}")
    
    # Print final status
    workflow.print_status_report()

if __name__ == "__main__":
    print("Testing Enhanced Paper Trading Workflow")
    print("=" * 60)
    
    # Test both modes
    asyncio.run(test_full_workflow())
    asyncio.run(test_demo_workflow())
    
    print("\n" + "=" * 60)
    print("Test completed!")
