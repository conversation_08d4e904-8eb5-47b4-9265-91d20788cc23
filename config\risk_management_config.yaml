# Risk Management Agent Configuration
# Comprehensive risk management for intraday trading with Angel One integration

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 SHARED CONFIGURATION REFERENCE
# ═══════════════════════════════════════════════════════════════════════════════
# This agent uses centralized environment configuration
# See: config/environment_config.yaml and .env file

# ═══════════════════════════════════════════════════════════════════════════════
# 🔐 ANGEL ONE API CONFIGURATION (Uses centralized environment variables)
# ═══════════════════════════════════════════════════════════════════════════════
angel_one_api:
  # SmartAPI Credentials from environment variables (defined in .env file)
  api_key: "${SMARTAPI_API_KEY}"
  username: "${SMARTAPI_USERNAME}"
  password: "${SMARTAPI_PASSWORD}"
  totp_token: "${SMARTAPI_TOTP_TOKEN}"
  
  # API Settings
  timeout: 10  # seconds
  max_retries: 3
  retry_delay: 2  # seconds
  
  # Rate Limiting
  requests_per_second: 10
  burst_limit: 50

# ═══════════════════════════════════════════════════════════════════════════════
# 💰 CAPITAL ALLOCATION CONTROL
# ═══════════════════════════════════════════════════════════════════════════════
capital_allocation:
  # Base Capital Settings
  total_capital: 100000  # Rs 1,00,000
  max_risk_per_trade_percent: 1.0  # 1% risk per trade
  max_daily_risk_percent: 5.0  # 5% daily risk limit
  max_portfolio_risk_percent: 10.0  # 10% total portfolio risk
  
  # Position Sizing Methods
  position_sizing:
    default_method: "fixed_fraction"  # "kelly", "fixed_fraction", "volatility_scaled", "adaptive"
    
    # Fixed Fraction Settings
    fixed_fraction:
      default_percent: 2.0  # 2% of capital per position
      max_percent: 5.0  # Maximum 5% per position
    
    # Kelly Criterion Settings
    kelly_criterion:
      enable: true
      max_kelly_fraction: 0.25  # Cap at 25%
      min_trades_for_kelly: 30  # Minimum trades needed
      lookback_period_days: 90
      safety_factor: 0.5  # Use 50% of Kelly for safety
    
    # Volatility Scaling
    volatility_scaling:
      enable: true
      atr_multiplier: 2.0
      volatility_lookback_period: 20
      min_position_size: 0.5  # Minimum 0.5% position
      max_position_size: 3.0  # Maximum 3% position
    
    # Adaptive Sizing (based on drawdown)
    adaptive_sizing:
      enable: true
      drawdown_thresholds:
        - drawdown: 2.0  # 2% drawdown
          size_multiplier: 0.8  # Reduce size by 20%
        - drawdown: 5.0  # 5% drawdown
          size_multiplier: 0.5  # Reduce size by 50%
        - drawdown: 10.0  # 10% drawdown
          size_multiplier: 0.2  # Reduce size by 80%
  
  # Intraday Margin Settings
  intraday_margin:
    multiplier: 3.5  # 3.5x intraday margin available
    max_order_value_multiplier: 3.5  # Maximum order value as multiple of capital
    margin_buffer_percent: 10.0  # Keep 10% margin buffer
  
  # Portfolio Allocation Limits
  portfolio_limits:
    max_positions_per_symbol: 2
    max_total_positions: 10
    max_strategy_allocation_percent: 20.0
    max_sector_allocation_percent: 30.0

# ═══════════════════════════════════════════════════════════════════════════════
# 🛑 PRE-TRADE RISK FILTERS
# ═══════════════════════════════════════════════════════════════════════════════
pre_trade_filters:
  # Concurrent Trade Limits
  concurrent_trades:
    max_total_trades: 5  # Maximum 5 concurrent trades
    max_trades_per_symbol: 2  # Maximum 2 trades per symbol
    max_trades_per_strategy: 3  # Maximum 3 trades per strategy
  
  # Risk-Reward Requirements
  risk_reward:
    min_rr_ratio: 1.5  # Minimum 1.5:1 RR ratio
    preferred_rr_ratio: 2.0  # Preferred 2:1 RR ratio
    max_stop_loss_percent: 3.0  # Maximum 3% stop loss
  
  # Time-Based Filters
  time_filters:
    market_hours_only: true
    market_open_time: "09:20"
    market_close_time: "15:00"
    avoid_first_minutes: 10  # Avoid first 10 minutes
    avoid_last_minutes: 30  # Avoid last 30 minutes
    no_trades_after: "14:30"  # No new trades after 2:30 PM
  
  # Volatility Guards
  volatility_guards:
    max_atr_percent: 5.0  # Maximum 5% ATR
    max_volatility_percentile: 90  # Maximum 90th percentile volatility
    min_liquidity_volume: 100000  # Minimum daily volume
    max_bid_ask_spread_percent: 0.5  # Maximum 0.5% bid-ask spread
  
  # Blacklisted Symbols
  blacklisted_symbols:
    - "SYMBOL1"  # Add symbols to avoid
    - "SYMBOL2"
  
  # Market Regime Filters
  market_regime_filters:
    enable: true
    bear_market_max_trades: 3  # Reduce trades in bear market
    high_volatility_max_trades: 2  # Reduce trades in high volatility
    low_liquidity_avoid: true  # Avoid trades in low liquidity

# ═══════════════════════════════════════════════════════════════════════════════
# 🔁 LIVE TRADE SUPERVISION
# ═══════════════════════════════════════════════════════════════════════════════
live_supervision:
  # Drawdown Controls
  drawdown_controls:
    max_daily_drawdown_percent: 10.0  # Stop all trades at 10% daily drawdown
    max_total_drawdown_percent: 15.0  # Stop all trades at 15% total drawdown
    drawdown_calculation_method: "peak_to_trough"  # "peak_to_trough" or "high_water_mark"
  
  # Dynamic Stop Loss
  dynamic_stop_loss:
    enable: true
    trailing_stop_enable: true
    trailing_stop_percent: 1.0  # 1% trailing stop
    time_based_exit_enable: true
    max_holding_time_minutes: 240  # 4 hours maximum holding
    
    # VWAP-based stops
    vwap_based_stops:
      enable: true
      vwap_deviation_percent: 2.0  # Exit if price deviates 2% from VWAP
  
  # Re-entry Prevention
  re_entry_prevention:
    enable: true
    cooldown_period_minutes: 30  # 30-minute cooldown after SL hit
    max_re_entries_per_symbol: 2  # Maximum 2 re-entries per symbol per day
  
  # Slippage Monitoring
  slippage_monitoring:
    enable: true
    max_slippage_percent: 0.2  # Maximum 0.2% slippage
    slippage_threshold_for_rejection: 0.5  # Reject if slippage > 0.5%
  
  # Position Monitoring
  position_monitoring:
    check_interval_seconds: 30  # Check positions every 30 seconds
    margin_check_interval_seconds: 60  # Check margin every minute
    auto_square_off_time: "15:20"  # Auto square-off time

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 POST-TRADE RISK LOGGING & REPORTING
# ═══════════════════════════════════════════════════════════════════════════════
logging_reporting:
  # Trade Risk Logging
  trade_logging:
    enable: true
    log_level: "INFO"
    log_file_path: "logs/risk_management"
    max_log_file_size_mb: 50
    backup_count: 10
    
    # Log Details
    log_entry_details: true
    log_exit_details: true
    log_sl_hits: true
    log_profit_targets: true
    log_slippage: true
    log_execution_delays: true
  
  # Daily Risk Reports
  daily_reports:
    enable: true
    report_time: "15:30"  # Generate report at 3:30 PM
    report_path: "reports/daily_risk"
    
    # Report Contents
    include_pnl_summary: true
    include_risk_metrics: true
    include_position_analysis: true
    include_slippage_analysis: true
    include_execution_analysis: true
  
  # Model Feedback
  model_feedback:
    enable: true
    failed_trade_threshold: -2.0  # Flag trades with >2% loss
    feedback_file_path: "data/model_feedback"
    
    # Feedback Categories
    flag_high_slippage: true
    flag_poor_execution: true
    flag_unexpected_moves: true
  
  # Compliance Tracking
  compliance_tracking:
    enable: true
    track_margin_usage: true
    track_position_limits: true
    track_risk_limits: true
    compliance_report_path: "reports/compliance"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 INTEGRATION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
integrations:
  # Signal Generation Agent
  signal_agent:
    enable: true
    validation_required: true
    auto_reject_high_risk: true
  
  # Market Monitoring Agent
  market_monitoring:
    enable: true
    use_market_regime: true
    use_volatility_data: true
  
  # Execution Agent (Future)
  execution_agent:
    enable: false
    pre_execution_validation: true
    post_execution_monitoring: true
  
  # Notifications (Uses centralized environment variables)
  notifications:
    telegram:
      enable: false
      bot_token: "${TELEGRAM_BOT_TOKEN}"
      chat_id: "${TELEGRAM_CHAT_ID}"

    email:
      enable: false
      smtp_server: "${EMAIL_SMTP_SERVER}"
      smtp_port: "${EMAIL_SMTP_PORT}"
      username: "${EMAIL_USERNAME}"
      password: "${EMAIL_PASSWORD}"
      to_email: "${EMAIL_TO}"

# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ SYSTEM SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
system:
  # Performance Settings
  performance:
    max_concurrent_api_calls: 5
    api_call_timeout: 10
    cache_duration_seconds: 30
  
  # Data Storage
  data_storage:
    risk_data_path: "data/risk_management"
    position_data_path: "data/positions"
    trade_data_path: "data/trades"
    backup_enabled: true
    backup_interval_hours: 6
  
  # Error Handling
  error_handling:
    max_api_errors: 5  # Maximum API errors before stopping
    error_cooldown_minutes: 5  # Cooldown after errors
    fallback_to_conservative_mode: true
