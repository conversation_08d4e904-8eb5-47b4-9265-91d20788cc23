import pandas as pd
from datetime import datetime

INPUT_FILE = r"C:\Users\<USER>\Documents\Intraday-AI\tests-2\filtered_nifty50_data.csv"
OUTPUT_FILE = r"C:\Users\<USER>\Documents\Intraday-AI\tests-2\nifty50_15min_data.csv"

def process_chunk(chunk):
    # Combine date and time into a single datetime column
    chunk["Datetime"] = pd.to_datetime(chunk["Date"] + " " + chunk["Time"])
    chunk.drop(columns=["Date", "Time"], inplace=True)

    # Set datetime index for resampling
    chunk.set_index("Datetime", inplace=True)

    # Resample to 15-minute candles per stock
    ohlc_dict = {
        "Open": "first",
        "High": "max",
        "Low": "min",
        "Close": "last",
        "Volume": "sum"
    }

    resampled = (
        chunk.groupby("Stock_Name")
             .resample("15min")
             .agg(ohlc_dict)
             .dropna(subset=["Open", "Close"])  # Drop incomplete candles
             .reset_index()
    )

    return resampled

def resample_to_15min_chunks(chunksize=500_000):
    reader = pd.read_csv(INPUT_FILE, chunksize=chunksize)
    first = True

    for i, chunk in enumerate(reader):
        chunk.columns = chunk.columns.str.strip()  # Clean headers
        result = process_chunk(chunk)

        mode = "w" if first else "a"
        header = first
        result.to_csv(OUTPUT_FILE, mode=mode, index=False, header=header)
        first = False
        print(f"Processed chunk {i + 1}")

def main():
    print("Starting 15-minute resampling...")
    resample_to_15min_chunks()
    print("Done. Output saved to:", OUTPUT_FILE)

if __name__ == "__main__":
    main()
