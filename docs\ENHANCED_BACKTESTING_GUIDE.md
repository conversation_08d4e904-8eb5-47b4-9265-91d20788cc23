# Enhanced Backtesting System - Complete Guide

## 🎯 **Your Questions Answered**

### ✅ **1. Multiple Timeframes**: YES - Runs on ALL timeframes automatically
### ✅ **2. All 35+ Strategies**: YES - Processes ALL strategies from config/strategies.yaml  
### ✅ **3. File Locations**: Configurable at the top of the script
### ✅ **4. Append Mode**: YES - Appends results (doesn't overwrite)

---

## 🔧 **Configuration Section**

All settings are at the top of `backtesting_enhanced.py`:

```python
# Data Configuration
DATA_DIR = "data/features"  # Directory containing feature files
TIMEFRAMES = ["5min", "15min", "30min", "1h"]  # All timeframes to process
STRATEGIES_FILE = "config/strategies.yaml"  # Strategy configuration file

# Output Configuration
OUTPUT_DIR = "data/backtest"
OUTPUT_FILE = "comprehensive_strategy_results.parquet"
APPEND_RESULTS = True      # True = append, False = overwrite

# Processing Configuration
MAX_SYMBOLS = None         # None = all symbols, or set number for testing
MAX_STRATEGIES = None      # None = all strategies, or set number for testing
```

---

## 🚀 **How to Run**

### **Option 1: Run Everything (Recommended)**
```bash
# Process ALL timeframes, ALL 35+ strategies, ALL symbols
python backtesting_enhanced.py
```

### **Option 2: Testing Mode**
```bash
# Test with limited data
python backtesting_enhanced.py --max-symbols 5 --max-strategies 10

# Test specific timeframes only
python backtesting_enhanced.py --timeframes 15min 30min

# Overwrite instead of append
python backtesting_enhanced.py --no-append
```

---

## 📁 **File Location Configuration**

### **Current Setup (Auto-detected)**:
- **5min data**: `data/features/features_5min.csv`
- **15min data**: `data/features/features_15min.csv`  
- **30min data**: `data/features/features_30min.csv`
- **1h data**: `data/features/features_1h.csv`
- **Strategies**: `config/strategies.yaml`

### **To Change File Locations**:
Edit these lines in `backtesting_enhanced.py`:
```python
DATA_DIR = "your/custom/path"           # Change data directory
STRATEGIES_FILE = "your/strategies.yaml" # Change strategy file
OUTPUT_DIR = "your/output/path"         # Change output directory
```

---

## 📊 **What It Will Process**

### **Timeframes**: 
- ✅ **5min** (if file exists)
- ✅ **15min** (if file exists)  
- ✅ **30min** (if file exists)
- ✅ **1h** (if file exists)

### **Strategies**: 
- ✅ **ALL 35 strategies** from `config/strategies.yaml`
- Including: CPR_Breakout, VWAP_Pullback, SuperTrend_EMA, Opening_Range_Breakout, etc.

### **Risk-Reward Ratios**:
- ✅ **1:2** (1% risk, 2% reward)
- ✅ **1:3** (1% risk, 3% reward)  
- ✅ **2:3** (2% risk, 3% reward)

### **Symbols**:
- ✅ **ALL symbols** found in your data files
- Or limited number if you specify `--max-symbols`

---

## 💾 **Output Details**

### **File Format**: 
- **Parquet with Brotli compression** (70-80% space savings)
- **Appends results** (doesn't overwrite existing data)
- **Location**: `data/backtest/comprehensive_strategy_results.parquet`

### **Expected Output Size**:
For your 110M row dataset with 35 strategies:
- **CSV would be**: ~500MB - 1GB
- **Parquet will be**: ~100MB - 200MB (80% savings!)

---

## 🔄 **Processing Flow**

```
1. Load ALL 35+ strategies from config/strategies.yaml
2. Detect available timeframe files (5min, 15min, 30min, 1h)
3. For each timeframe:
   └── For each symbol batch (3 symbols at a time):
       └── For each symbol:
           └── For each strategy (ALL 35+):
               └── For each R:R ratio (1:2, 1:3, 2:3):
                   └── Run backtest and calculate metrics
                   └── Append results to Parquet file
4. Final compressed output with ALL results
```

---

## 📈 **Expected Results**

### **Total Combinations**:
- **4 timeframes** × **35 strategies** × **~100 symbols** × **3 R:R ratios**
- **= ~42,000 backtesting results**

### **Performance Metrics**:
Each result includes:
- ROI, Accuracy, Sharpe Ratio, Max Drawdown
- Profit Factor, Risk-Reward Ratio, Number of Trades
- Strategy name, Symbol, Timeframe, Profitability flag

---

## ⚡ **Memory & Performance**

### **Memory Optimization**:
- ✅ **Chunked processing** (50K rows at a time)
- ✅ **Batch processing** (3 symbols at a time)
- ✅ **Immediate writing** (results saved after each symbol)
- ✅ **Garbage collection** (memory cleanup after each batch)

### **Expected Runtime**:
- **Small dataset** (10 symbols): ~10-30 minutes
- **Medium dataset** (50 symbols): ~1-3 hours  
- **Large dataset** (100+ symbols): ~3-8 hours

---

## 🛠️ **Customization Options**

### **Change Processing Limits**:
```python
CHUNK_SIZE = 50000         # Increase for more memory, decrease for less
BATCH_SIZE = 3             # Symbols processed together
MAX_SYMBOLS = 20           # Limit symbols for testing
MAX_STRATEGIES = 10        # Limit strategies for testing
```

### **Change Risk Management**:
```python
RISK_REWARD_RATIOS = [[1, 2], [1, 3], [2, 3]]  # Add more R:R ratios
POSITION_SIZE_PCT = 2.0    # Change position sizing
PROFIT_THRESHOLD = 5.0     # Change profitability threshold
```

### **Change Output Format**:
```python
OUTPUT_FORMAT = "csv"      # Change to CSV if needed
COMPRESSION = "snappy"     # Change compression algorithm
APPEND_RESULTS = False     # Overwrite instead of append
```

---

## 🚨 **Important Notes**

### **File Requirements**:
- ✅ **Data files must exist**: `data/features/features_[timeframe].csv`
- ✅ **Strategy file must exist**: `config/strategies.yaml`
- ✅ **Columns required**: datetime, open, high, low, close, volume, stock_name

### **Append Behavior**:
- ✅ **First run**: Creates new file
- ✅ **Subsequent runs**: Appends to existing file
- ✅ **No duplicates**: Each run adds new results
- ✅ **To restart**: Delete output file or use `--no-append`

### **Error Handling**:
- ✅ **Missing files**: Skips gracefully with warnings
- ✅ **Bad data**: Continues processing other symbols/strategies
- ✅ **Memory issues**: Automatic garbage collection
- ✅ **Crashes**: Results saved incrementally (no data loss)

---

## 🎯 **Quick Start Commands**

```bash
# Full production run (ALL timeframes, ALL strategies, ALL symbols)
python backtesting_enhanced.py

# Quick test (5 symbols, 10 strategies)
python backtesting_enhanced.py --max-symbols 5 --max-strategies 10

# Specific timeframes only
python backtesting_enhanced.py --timeframes 15min 30min

# Fresh start (overwrite existing results)
python backtesting_enhanced.py --no-append
```

---

## 📊 **Verify Results**

```bash
# Check the output
python verify_results.py

# Or manually check
python -c "import pandas as pd; df = pd.read_parquet('data/backtest/comprehensive_strategy_results.parquet'); print(f'Total results: {len(df)}'); print(f'Strategies: {df.strategy_name.nunique()}'); print(f'Timeframes: {df.timeframe.unique()}')"
```

**This enhanced version addresses ALL your requirements! 🎉**
