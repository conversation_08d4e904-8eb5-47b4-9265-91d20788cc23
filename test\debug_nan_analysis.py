#!/usr/bin/env python3
"""
Debug script to analyze NaN patterns in the data
"""

import polars as pl
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_nan_patterns():
    """Analyze NaN patterns in the data"""
    
    # Load the 360ONE 15min data
    data_file = "c:/Users/<USER>/Documents/Equity/data/features/features_360ONE_15min.parquet"
    
    try:
        df = pl.read_parquet(data_file)
        df = df.sort("datetime")
        logger.info(f"Total rows: {len(df)}")
        
        # Check NaN patterns for key columns
        key_cols = ['rsi_14', 'ema_10', 'close']
        
        for col in key_cols:
            if col in df.columns:
                null_count = df[col].is_null().sum()
                logger.info(f"\n{col}:")
                logger.info(f"  Total NaN values: {null_count}")
                logger.info(f"  Percentage NaN: {null_count/len(df)*100:.2f}%")
                
                # Find first non-null index
                non_null_mask = df[col].is_not_null()
                if non_null_mask.any():
                    first_valid_idx = non_null_mask.arg_max()
                    logger.info(f"  First valid value at index: {first_valid_idx}")
                    logger.info(f"  First valid value: {df[col][first_valid_idx]}")
                    logger.info(f"  DateTime of first valid: {df['datetime'][first_valid_idx]}")
                    
                    # Show pattern around first valid value
                    start_idx = max(0, first_valid_idx - 5)
                    end_idx = min(len(df), first_valid_idx + 10)
                    sample_data = df[start_idx:end_idx].select(['datetime', col])
                    logger.info(f"  Pattern around first valid value:")
                    for i, row in enumerate(sample_data.iter_rows(named=True)):
                        marker = " <-- FIRST VALID" if start_idx + i == first_valid_idx else ""
                        logger.info(f"    {row['datetime']}: {row[col]}{marker}")
                else:
                    logger.info(f"  No valid values found!")
        
        # Check if there are any rows where both RSI and EMA are valid
        both_valid = df.filter((pl.col('rsi_14').is_not_null()) & (pl.col('ema_10').is_not_null()))
        logger.info(f"\nRows with both RSI_14 and EMA_10 valid: {len(both_valid)}")
        
        if len(both_valid) > 0:
            # Test conditions on valid data only
            long_condition = (both_valid['rsi_14'] < 30) & (both_valid['close'] > both_valid['ema_10'])
            short_condition = (both_valid['rsi_14'] > 70) & (both_valid['close'] < both_valid['ema_10'])
            
            logger.info(f"Long signals on valid data: {long_condition.sum()}")
            logger.info(f"Short signals on valid data: {short_condition.sum()}")
            
            if long_condition.sum() > 0:
                logger.info("Sample long signals:")
                long_samples = both_valid.filter(long_condition).head(3)
                for i, row in enumerate(long_samples.iter_rows(named=True)):
                    logger.info(f"  {i+1}. {row['datetime']}: RSI={row['rsi_14']:.2f}, Close={row['close']:.2f}, EMA10={row['ema_10']:.2f}")
            
            if short_condition.sum() > 0:
                logger.info("Sample short signals:")
                short_samples = both_valid.filter(short_condition).head(3)
                for i, row in enumerate(short_samples.iter_rows(named=True)):
                    logger.info(f"  {i+1}. {row['datetime']}: RSI={row['rsi_14']:.2f}, Close={row['close']:.2f}, EMA10={row['ema_10']:.2f}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    logger.info("Starting NaN pattern analysis...")
    analyze_nan_patterns()
    logger.info("Analysis completed.")