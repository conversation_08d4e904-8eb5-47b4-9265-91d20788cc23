Here are some important commands for developing and managing this project:

**Installation:**
- `pip install -r requirements.txt`: Install all project dependencies.

**Testing:**
- `python test_main.py`: Run the system's test suite.

**Running Entrypoints/Workflows:**
- `python main.py --health_check`: Perform a system health check.
- `python main.py --status`: Check the current system status.
- `python main.py --optimize_gpu`: Optimize GPU settings.
- `python main.py --agent [agent_name]`: Run a specific trading agent (e.g., `python main.py --agent data_ingestion`).
- `python main.py --workflow [workflow_name]`: Run a complete workflow (e.g., `python main.py --workflow full_pipeline`).

**Troubleshooting/Diagnostics:**
- `python main.py --health_check`: Comprehensive health check.
- `python main.py --status`: Real-time status.
- `python main.py --optimize_gpu`: Force GPU optimization.
- `python test_main.py`: Test all requirements.
- `pip install -r requirements.txt --force-reinstall`: Reinstall dependencies to resolve package issues.