#!/usr/bin/env python3
"""
Comprehensive Test for All Fixes
Tests the symbol loading, timeframe population, and library usage fixes
"""

import os
import sys
import asyncio
import logging
import json
import polars as pl
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_symbol_loading():
    """Test that all 500 symbols are loaded correctly"""
    logger.info("🔍 Testing symbol loading...")
    
    try:
        # Check symbols.json
        symbols_file = "config/symbols.json"
        if Path(symbols_file).exists():
            with open(symbols_file, 'r') as f:
                symbols = json.load(f)
            logger.info(f"✅ symbols.json contains {len(symbols)} symbols")
            
            if len(symbols) == 500:
                logger.info("✅ All 500 symbols are available")
                return True
            else:
                logger.error(f"❌ Expected 500 symbols, found {len(symbols)}")
                return False
        else:
            logger.error("❌ symbols.json not found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing symbol loading: {e}")
        return False

async def test_historical_data_timeframes():
    """Test that all timeframes are available in historical data"""
    logger.info("🔍 Testing historical data timeframes...")
    
    try:
        timeframes = ['5min', '15min', '30min', '1hr']
        results = {}
        
        for tf in timeframes:
            file_path = f"data/historical/historical_{tf}.parquet"
            if Path(file_path).exists():
                df = pl.read_parquet(file_path)
                symbol_count = df[' Stock_Name'].n_unique()
                results[tf] = {
                    'exists': True,
                    'records': len(df),
                    'symbols': symbol_count
                }
                logger.info(f"✅ {tf}: {len(df):,} records, {symbol_count} symbols")
            else:
                results[tf] = {'exists': False}
                logger.error(f"❌ {tf}: File not found")
        
        # Check if all timeframes have 500 symbols
        all_good = True
        for tf, data in results.items():
            if not data.get('exists', False) or data.get('symbols', 0) != 500:
                all_good = False
                
        if all_good:
            logger.info("✅ All timeframes have complete 500 symbol data")
            return True
        else:
            logger.error("❌ Some timeframes are missing or incomplete")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing timeframes: {e}")
        return False

async def test_market_monitoring_agent_config():
    """Test market monitoring agent configuration"""
    logger.info("🔍 Testing market monitoring agent configuration...")
    
    try:
        # Import the agent
        sys.path.append('agents')
        from market_monitoring_agent import MarketMonitoringAgent
        
        # Create agent instance
        agent = MarketMonitoringAgent("config/market_monitoring_config.yaml")
        
        # Check timeframes configuration
        timeframes = agent.config.market_data_config.get('timeframes', [])
        expected_timeframes = ['5min', '15min', '30min', '1hr']
        
        if set(timeframes) == set(expected_timeframes):
            logger.info(f"✅ Timeframes configured correctly: {timeframes}")
        else:
            logger.error(f"❌ Timeframes mismatch. Expected: {expected_timeframes}, Got: {timeframes}")
            return False
        
        # Test symbol loading
        symbols = await agent._get_symbol_list(500)
        if len(symbols) == 500:
            logger.info(f"✅ Agent loads all 500 symbols correctly")
            return True
        else:
            logger.error(f"❌ Agent loads only {len(symbols)} symbols instead of 500")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing agent configuration: {e}")
        return False

async def test_library_usage():
    """Test that agents are using correct libraries"""
    logger.info("🔍 Testing library usage in agents...")
    
    try:
        # Check key agent files for correct imports
        agent_files = [
            'agents/market_monitoring_agent.py',
            'agents/signal_generation_agent.py',
            'agents/execution_agent.py'
        ]
        
        issues = []
        
        for agent_file in agent_files:
            if Path(agent_file).exists():
                with open(agent_file, 'r') as f:
                    content = f.read()
                
                # Check for problematic imports
                if 'import pandas' in content and 'import polars as pl' not in content:
                    issues.append(f"{agent_file}: Using pandas instead of polars")
                
                if 'import numpy' in content and 'import pyarrow' not in content:
                    issues.append(f"{agent_file}: Using numpy without pyarrow")
                
                if 'import talib' in content and 'polars_talib' not in content:
                    issues.append(f"{agent_file}: Using talib instead of polars-talib")
                
                # Check for good imports
                if 'import polars as pl' in content:
                    logger.info(f"✅ {agent_file}: Uses polars")
                
                if 'import pyarrow' in content:
                    logger.info(f"✅ {agent_file}: Uses pyarrow")
                
                if 'polars_talib' in content:
                    logger.info(f"✅ {agent_file}: Uses polars-talib")
        
        if issues:
            for issue in issues:
                logger.error(f"❌ {issue}")
            return False
        else:
            logger.info("✅ All agents use correct high-performance libraries")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error testing library usage: {e}")
        return False

async def test_websocket_fix():
    """Test that WebSocket hanging issue is fixed"""
    logger.info("🔍 Testing WebSocket hanging fix...")
    
    try:
        # Check if the WebSocket fix methods are present
        sys.path.append('agents')
        from market_monitoring_agent import MarketMonitoringAgent
        
        agent = MarketMonitoringAgent("config/market_monitoring_config.yaml")
        
        # Check if the new thread-safe methods exist
        required_methods = [
            '_schedule_subscription',
            '_schedule_tick_processing', 
            '_schedule_reconnection',
            '_process_pending_operations'
        ]
        
        for method in required_methods:
            if hasattr(agent, method):
                logger.info(f"✅ WebSocket fix method {method} exists")
            else:
                logger.error(f"❌ WebSocket fix method {method} missing")
                return False
        
        logger.info("✅ WebSocket hanging fix is properly implemented")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing WebSocket fix: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("🚀 Starting Comprehensive Fix Testing")
    logger.info("=" * 60)
    
    tests = [
        ("Symbol Loading", test_symbol_loading),
        ("Historical Data Timeframes", test_historical_data_timeframes),
        ("Market Monitoring Agent Config", test_market_monitoring_agent_config),
        ("Library Usage", test_library_usage),
        ("WebSocket Fix", test_websocket_fix)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name} test...")
        try:
            result = await test_func()
            results[test_name] = result
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"💥 {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name:.<40} {status}")
    
    logger.info("-" * 60)
    logger.info(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! The fixes are working correctly.")
        return True
    else:
        logger.error(f"💥 {total - passed} tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
