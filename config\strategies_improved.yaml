# Improved Trading Strategies with Enhanced Logic and Risk Management
# Fixed common flaws: added volume confirmation, trend filters, and multi-timeframe validation
# Updated to match actual feature columns: stock_name, regime, sma_20, stoch_k, stoch_d, cci, mfi, etc.

strategies:
  # Enhanced RSI Reversal with Volume and Trend Confirmation
  - name: Enhanced_RSI_Reversal
    long: rsi_14 < 30 and close > ema_10 and volume > sma_20_volume * 1.5 and adx > 20
    short: rsi_14 > 70 and close < ema_10 and volume > sma_20_volume * 1.5 and adx > 20
    description: "RSI reversal with volume surge and trend strength confirmation"
    risk_level: "medium"
    market_conditions: ["trending", "volatile"]

  # Enhanced MACD with Volume Confirmation (removed histogram as it's calculated)
  - name: Enhanced_MACD_Crossover
    long: macd > macd_signal and close > ema_20 and volume > sma_20_volume * 1.2
    short: macd < macd_signal and close < ema_20 and volume > sma_20_volume * 1.2
    description: "MACD crossover with volume filter"
    risk_level: "medium"
    market_conditions: ["trending"]

  # Multi-Timeframe EMA Strategy with Momentum Filter
  - name: Multi_EMA_Momentum
    long: ema_5 > ema_13 and ema_13 > ema_21 and ema_21 > ema_50 and close > ema_5 and rsi_14 > 50 and volume > sma_20_volume
    short: ema_5 < ema_13 and ema_13 < ema_21 and ema_21 < ema_50 and close < ema_5 and rsi_14 < 50 and volume > sma_20_volume
    description: "Multi-EMA alignment with momentum and volume confirmation"
    risk_level: "low"
    market_conditions: ["trending"]

  # Enhanced Bollinger Bands with RSI Divergence
  - name: Enhanced_Bollinger_Bounce
    long: close < bb_lower and rsi_5 < 30 and close > close_lag_1 and volume > sma_20_volume * 1.3
    short: close > bb_upper and rsi_5 > 70 and close < close_lag_1 and volume > sma_20_volume * 1.3
    description: "Bollinger bounce with price action and volume confirmation"
    risk_level: "medium"
    market_conditions: ["ranging", "volatile"]

  # VWAP Mean Reversion with Trend Filter
  - name: Enhanced_VWAP_Strategy
    long: close < vwap * 0.995 and rsi_14 < 40 and ema_20 > ema_50 and volume > sma_20_volume * 1.2
    short: close > vwap * 1.005 and rsi_14 > 60 and ema_20 < ema_50 and volume > sma_20_volume * 1.2
    description: "VWAP mean reversion with trend direction filter"
    risk_level: "medium"
    market_conditions: ["trending", "ranging"]

  # Enhanced Donchian Breakout with Volume Filter
  - name: Enhanced_Donchian_Break
    long: close >= donchian_high and volume > sma_20_volume * 2.0 and atr > atr_sma_14 * 1.1
    short: close <= donchian_low and volume > sma_20_volume * 2.0 and atr > atr_sma_14 * 1.1
    description: "Donchian breakout with volume surge and volatility confirmation"
    risk_level: "high"
    market_conditions: ["breakout", "volatile"]

  # Supertrend with Multiple Confirmations
  - name: Enhanced_Supertrend
    long: close > supertrend and rsi_14 > 55 and macd > macd_signal and volume > sma_20_volume
    short: close < supertrend and rsi_14 < 45 and macd < macd_signal and volume > sma_20_volume
    description: "Supertrend with momentum and volume confirmation"
    risk_level: "medium"
    market_conditions: ["trending"]

  # CPR Strategy with Time-based Filter
  - name: Enhanced_CPR_Strategy
    long: close > cpr_top and rsi_14 > 50 and volume > sma_20_volume * 1.2 and hour >= 10 and hour <= 14
    short: close < cpr_bottom and rsi_14 < 50 and volume > sma_20_volume * 1.2 and hour >= 10 and hour <= 14
    description: "CPR breakout with time filter for active trading hours"
    risk_level: "medium"
    market_conditions: ["breakout"]

  # ADX Trend Following with Momentum
  - name: Enhanced_ADX_Trend
    long: adx > 25 and close > ema_20 and rsi_14 > 50 and macd > 0 and volume > sma_20_volume
    short: adx > 25 and close < ema_20 and rsi_14 < 50 and macd < 0 and volume > sma_20_volume
    description: "ADX trend following with momentum confirmation"
    risk_level: "medium"
    market_conditions: ["trending"]

  # Enhanced VCP Pattern with Strict Filters
  - name: Enhanced_VCP_Breakout
    long: vcp_pattern == 1 and upward_candle == 1 and volume > sma_20_volume * 2.0 and rsi_14 > 60 and close > ema_20
    short: vcp_pattern == 1 and downward_candle == 1 and volume > sma_20_volume * 2.0 and rsi_14 < 40 and close < ema_20
    description: "VCP pattern with volume surge and momentum confirmation"
    risk_level: "high"
    market_conditions: ["breakout"]

  # Mean Reversion Strategy with Multiple Filters (using bb_position calculated from bb_upper/bb_lower)
  - name: Mean_Reversion_Multi
    long: rsi_14 < 25 and close < bb_lower * 1.02 and close < ema_20 * 0.98 and volume > sma_20_volume * 1.5
    short: rsi_14 > 75 and close > bb_upper * 0.98 and close > ema_20 * 1.02 and volume > sma_20_volume * 1.5
    description: "Multi-indicator mean reversion strategy"
    risk_level: "medium"
    market_conditions: ["ranging", "oversold", "overbought"]

  # Momentum Breakout with Volume Confirmation (using rolling high/low)
  - name: Momentum_Breakout
    long: close > donchian_high and rsi_14 > 60 and volume > sma_20_volume * 2.5 and atr > atr_sma_14
    short: close < donchian_low and rsi_14 < 40 and volume > sma_20_volume * 2.5 and atr > atr_sma_14
    description: "Momentum breakout with volume and volatility confirmation"
    risk_level: "high"
    market_conditions: ["breakout", "trending"]

  # Stochastic RSI Strategy
  - name: Stochastic_RSI_Strategy
    long: stoch_k < 20 and stoch_d < 20 and stoch_k > stoch_d and rsi_14 < 40 and volume > sma_20_volume
    short: stoch_k > 80 and stoch_d > 80 and stoch_k < stoch_d and rsi_14 > 60 and volume > sma_20_volume
    description: "Stochastic oscillator with RSI confirmation"
    risk_level: "medium"
    market_conditions: ["ranging", "oversold", "overbought"]

  # CCI Mean Reversion Strategy
  - name: CCI_Mean_Reversion
    long: cci < -100 and close > close_lag_1 and rsi_14 < 40 and volume > sma_20_volume
    short: cci > 100 and close < close_lag_1 and rsi_14 > 60 and volume > sma_20_volume
    description: "CCI mean reversion with momentum confirmation"
    risk_level: "medium"
    market_conditions: ["ranging", "oversold", "overbought"]

  # MFI Volume Strategy
  - name: MFI_Volume_Strategy
    long: mfi < 30 and rsi_14 < 40 and close > ema_10 and volume > sma_20_volume * 1.2
    short: mfi > 70 and rsi_14 > 60 and close < ema_10 and volume > sma_20_volume * 1.2
    description: "Money Flow Index with volume confirmation"
    risk_level: "medium"
    market_conditions: ["trending", "ranging"]

# Risk Management Parameters
risk_management:
  max_position_size: 0.05  # 5% of portfolio per trade
  stop_loss_atr_multiplier: 2.0
  take_profit_atr_multiplier: 3.0
  max_daily_loss: 0.02  # 2% of portfolio per day
  max_correlation: 0.7  # Maximum correlation between positions

# Market Condition Filters
market_filters:
  volatility_threshold: 0.02  # Minimum daily volatility
  volume_threshold: 1000  # Minimum volume
  spread_threshold: 0.001  # Maximum bid-ask spread
  market_hours_only: true
  avoid_news_events: true
