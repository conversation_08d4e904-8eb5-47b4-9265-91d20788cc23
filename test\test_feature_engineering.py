#!/usr/bin/env python3
"""
Test script for the optimized feature engineering
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import asyncio
import polars as pl
from pathlib import Path
from scripts.optimized_feature_engineering import OptimizedFeatureEngineering

async def test_feature_engineering():
    """Test the feature engineering with a small sample"""
    
    print("🧪 Testing Optimized Feature Engineering...")
    
    # Configuration for testing
    input_dir = "data/historical"
    output_dir = "data/features"
    chunk_size = 5000  # Very small chunk size for testing
    
    # Check if input directory exists
    if not Path(input_dir).exists():
        print(f"❌ Input directory not found: {input_dir}")
        return
    
    # Check if there are parquet files
    input_files = list(Path(input_dir).glob("*.parquet"))
    if not input_files:
        print(f"❌ No parquet files found in {input_dir}")
        return
    
    print(f"📁 Found {len(input_files)} files: {[f.name for f in input_files]}")
    
    # Test with just the 5min file first
    test_file = None
    for f in input_files:
        if "5min" in f.name:
            test_file = f
            break
    
    if not test_file:
        test_file = input_files[0]  # Use first file if no 5min file found
    
    print(f"🎯 Testing with file: {test_file.name}")
    
    # Read a small sample to check structure
    print("📊 Checking data structure...")
    df_sample = pl.read_parquet(test_file).head(1000)
    print(f"Sample shape: {df_sample.shape}")
    print(f"Columns: {df_sample.columns}")
    print(f"Data types: {df_sample.dtypes}")

    # Create a small test file for faster testing
    test_sample_file = Path("data/test_sample.parquet")
    df_sample.write_parquet(test_sample_file)
    print(f"📝 Created test sample file: {test_sample_file}")

    # Create processor with small chunk size for testing
    processor = OptimizedFeatureEngineering("data", output_dir, chunk_size)

    # Process just the test sample
    try:
        print(f"🚀 Starting feature engineering test...")
        await processor.process_file(test_sample_file)
        print("✅ Feature engineering test completed successfully!")
        
        # Check output
        output_file = Path(output_dir) / f"features_{test_sample_file.stem}.parquet"
        if output_file.exists():
            result_df = pl.read_parquet(output_file)
            print(f"📈 Output shape: {result_df.shape}")
            print(f"📋 Output columns: {result_df.columns}")
            print(f"🔍 Sample output:")
            print(result_df.head(3))
        else:
            print(f"❌ Output file not found: {output_file}")
            
    except Exception as e:
        print(f"❌ Error during feature engineering: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_feature_engineering())
