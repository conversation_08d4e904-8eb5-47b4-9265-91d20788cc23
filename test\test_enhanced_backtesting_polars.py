#!/usr/bin/env python3
"""
Test file for Enhanced Backtesting System with Polars, PyArrow, and AsyncIO
Tests the core functionality with sample data
"""

import os
import sys
import pytest
import polars as pl
import pyarrow as pa
import asyncio
import tempfile
import yaml
from pathlib import Path
from datetime import datetime, timedelta

# Add the parent directory to the path to import the module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.enhanced_backtesting_polars import (
    load_strategies,
    get_available_feature_files,
    generate_strategy_signals,
    simulate_trades_vectorized,
    calculate_performance_metrics,
    backtest_strategy_rr_async,
    process_strategy_async,
    write_results_async
)

class TestEnhancedBacktesting:
    """Test class for enhanced backtesting system"""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample market data for testing"""
        # Generate sample data with realistic price movements
        n_rows = 1000
        dates = [datetime.now() - timedelta(minutes=5*i) for i in range(n_rows)]
        dates.reverse()  # Chronological order
        
        # Generate realistic OHLCV data
        base_price = 100.0
        prices = []
        volumes = []
        
        for i in range(n_rows):
            # Random walk with some trend
            change = (i % 100 - 50) * 0.001 + (0.5 - abs((i % 200) - 100) / 100) * 0.01
            base_price *= (1 + change)
            
            # OHLC around the base price
            open_price = base_price * (1 + (i % 7 - 3) * 0.001)
            high_price = open_price * (1 + abs(i % 5) * 0.002)
            low_price = open_price * (1 - abs(i % 3) * 0.002)
            close_price = open_price + (high_price - low_price) * ((i % 10) / 10 - 0.5)
            
            prices.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price
            })
            
            # Volume with some pattern
            volume = 10000 + (i % 50) * 1000 + abs((i % 100) - 50) * 500
            volumes.append(volume)
        
        # Create technical indicators
        close_prices = [p['close'] for p in prices]
        
        # Simple EMA calculation
        ema_5 = []
        ema_20 = []
        alpha_5 = 2 / (5 + 1)
        alpha_20 = 2 / (20 + 1)
        
        for i, close in enumerate(close_prices):
            if i == 0:
                ema_5.append(close)
                ema_20.append(close)
            else:
                ema_5.append(alpha_5 * close + (1 - alpha_5) * ema_5[-1])
                ema_20.append(alpha_20 * close + (1 - alpha_20) * ema_20[-1])
        
        # Simple RSI calculation (simplified)
        rsi_14 = []
        for i in range(len(close_prices)):
            if i < 14:
                rsi_14.append(50.0)  # Default RSI
            else:
                gains = sum(max(0, close_prices[j] - close_prices[j-1]) for j in range(i-13, i+1))
                losses = sum(max(0, close_prices[j-1] - close_prices[j]) for j in range(i-13, i+1))
                rs = gains / (losses + 1e-8)
                rsi = 100 - (100 / (1 + rs))
                rsi_14.append(rsi)
        
        # Create DataFrame
        df = pl.DataFrame({
            'datetime': dates,
            'stock_name': ['TEST_STOCK'] * n_rows,
            'open': [p['open'] for p in prices],
            'high': [p['high'] for p in prices],
            'low': [p['low'] for p in prices],
            'close': [p['close'] for p in prices],
            'volume': volumes,
            'ema_5': ema_5,
            'ema_20': ema_20,
            'rsi_14': rsi_14
        })
        
        return df
    
    @pytest.fixture
    def sample_strategy(self):
        """Create sample strategy for testing"""
        return {
            'name': 'Test_EMA_Strategy',
            'long': 'ema_5 > ema_20 & volume > volume.rolling(20).mean() & rsi_14 > 50',
            'short': 'ema_5 < ema_20 & volume > volume.rolling(20).mean() & rsi_14 < 50',
            'capital': 100000
        }
    
    @pytest.fixture
    def temp_config_file(self, sample_strategy):
        """Create temporary config file for testing"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump({'strategies': [sample_strategy]}, f)
            return f.name
    
    def test_generate_strategy_signals(self, sample_data, sample_strategy):
        """Test strategy signal generation"""
        # Test long signals
        long_signals = generate_strategy_signals(sample_data, "long", sample_strategy['name'])
        assert isinstance(long_signals, pl.Series)
        assert len(long_signals) == len(sample_data)
        assert long_signals.dtype == pl.Boolean
        
        # Test short signals
        short_signals = generate_strategy_signals(sample_data, "short", sample_strategy['name'])
        assert isinstance(short_signals, pl.Series)
        assert len(short_signals) == len(sample_data)
        assert short_signals.dtype == pl.Boolean
        
        # Signals should not be all True or all False
        assert 0 < long_signals.sum() < len(sample_data)
        assert 0 < short_signals.sum() < len(sample_data)
    
    @pytest.mark.asyncio
    async def test_simulate_trades_async(self, sample_data, sample_strategy):
        """Test async trade simulation"""
        rr = [1, 2]  # 1:2 risk-reward ratio
        timeframe = "5min"

        trades = await simulate_trades_vectorized(sample_data, sample_strategy, rr, timeframe)
        
        assert trades is not None
        assert isinstance(trades, list)
        assert len(trades) > 0
        
        # Check trade structure
        for trade in trades:
            assert 'entry_time' in trade
            assert 'entry_price' in trade
            assert 'exit_price' in trade
            assert 'signal' in trade
            assert 'pnl' in trade
            assert 'pnl_pct' in trade
            assert 'position_size' in trade
            assert 'holding_period' in trade
            assert 'exit_reason' in trade
            
            # Validate trade values
            assert trade['signal'] in [1, -1]
            assert trade['holding_period'] > 0
            assert trade['position_size'] > 0
    
    def test_calculate_performance_metrics(self, sample_data, sample_strategy):
        """Test performance metrics calculation"""
        # Create sample trades
        trades = [
            {'pnl': 100, 'pnl_pct': 1.0, 'holding_period': 5},
            {'pnl': -50, 'pnl_pct': -0.5, 'holding_period': 3},
            {'pnl': 200, 'pnl_pct': 2.0, 'holding_period': 7},
            {'pnl': -25, 'pnl_pct': -0.25, 'holding_period': 2}
        ]
        
        symbol = "TEST_STOCK"
        strategy_name = sample_strategy['name']
        timeframe = "5min"
        rr_combo = [1, 2]
        
        metrics = calculate_performance_metrics(trades, symbol, strategy_name, timeframe, rr_combo)
        
        assert metrics is not None
        assert isinstance(metrics, dict)
        
        # Check all required columns are present
        required_columns = [
            'strategy_name', 'symbol', 'timeframe', 'n_trades', 'ROI', 'accuracy',
            'expectancy', 'sharpe_ratio', 'max_drawdown', 'profit_factor',
            'avg_holding_period', 'risk_reward_ratio', 'capital_at_risk',
            'liquidity', 'volatility', 'market_regime', 'correlation_index',
            'drawdown_duration', 'winning_trades', 'losing_trades', 'avg_win',
            'avg_loss', 'total_pnl', 'position_size_pct', 'risk_reward_combo',
            'is_profitable'
        ]
        
        for col in required_columns:
            assert col in metrics, f"Missing column: {col}"
        
        # Validate specific metrics
        assert metrics['n_trades'] == 4
        assert metrics['winning_trades'] == 2
        assert metrics['losing_trades'] == 2
        assert metrics['accuracy'] == 0.5
        assert metrics['total_pnl'] == 225  # 100 - 50 + 200 - 25
        assert metrics['symbol'] == symbol
        assert metrics['strategy_name'] == strategy_name
        assert metrics['timeframe'] == timeframe
    
    @pytest.mark.asyncio
    async def test_backtest_strategy_rr_async(self, sample_data, sample_strategy):
        """Test async strategy backtesting with R:R ratio"""
        symbol = "TEST_STOCK"
        timeframe = "5min"
        rr = [1, 2]
        
        result = await backtest_strategy_rr_async(sample_data, sample_strategy, timeframe, symbol, rr)
        
        if result is not None:  # May be None if no trades generated
            assert isinstance(result, dict)
            assert 'strategy_name' in result
            assert 'symbol' in result
            assert 'timeframe' in result
            assert 'risk_reward_combo' in result
            assert result['strategy_name'] == sample_strategy['name']
            assert result['symbol'] == symbol
            assert result['timeframe'] == timeframe
    
    @pytest.mark.asyncio
    async def test_process_strategy_async(self, sample_data, sample_strategy):
        """Test async strategy processing with multiple R:R ratios"""
        symbol = "TEST_STOCK"
        timeframe = "5min"
        
        results = await process_strategy_async(sample_data, sample_strategy, timeframe, symbol)
        
        assert isinstance(results, list)
        # Should have results for each R:R ratio (or empty if no trades)
        assert len(results) <= 4  # Maximum number of R:R ratios
        
        for result in results:
            assert isinstance(result, dict)
            assert 'strategy_name' in result
            assert 'risk_reward_combo' in result
    
    @pytest.mark.asyncio
    async def test_write_results_async(self, sample_data, sample_strategy):
        """Test async results writing"""
        # Create sample results
        results = [
            {
                'strategy_name': 'Test_Strategy',
                'symbol': 'TEST_STOCK',
                'timeframe': '5min',
                'n_trades': 10,
                'ROI': 5.5,
                'accuracy': 0.6,
                'expectancy': 50.0,
                'sharpe_ratio': 1.2,
                'max_drawdown': 2.5,
                'profit_factor': 1.5,
                'avg_holding_period': 5.0,
                'risk_reward_ratio': 2.0,
                'capital_at_risk': 2.0,
                'liquidity': 'High',
                'volatility': 0.15,
                'market_regime': 'bull',
                'correlation_index': 0.1,
                'drawdown_duration': 2.0,
                'winning_trades': 6,
                'losing_trades': 4,
                'avg_win': 100.0,
                'avg_loss': -50.0,
                'total_pnl': 300.0,
                'position_size_pct': 2.0,
                'risk_reward_combo': '1_2',
                'is_profitable': True
            }
        ]
        
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as f:
            output_path = f.name
        
        try:
            # Test writing
            await write_results_async(results, output_path, append=False)
            
            # Verify file was created and contains data
            assert os.path.exists(output_path)
            
            # Read back and verify
            df_read = pl.read_parquet(output_path)
            assert len(df_read) == 1
            assert df_read['strategy_name'][0] == 'Test_Strategy'
            assert df_read['symbol'][0] == 'TEST_STOCK'
            assert df_read['ROI'][0] == 5.5
            
        finally:
            # Cleanup
            if os.path.exists(output_path):
                os.unlink(output_path)

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
