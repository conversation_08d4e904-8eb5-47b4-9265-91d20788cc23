#!/usr/bin/env python3
"""
Data Deduplication Utility
Prevents duplicate data in training pipeline
"""

import polars as pl
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Tuple, List

logger = logging.getLogger(__name__)

class DataDeduplicator:
    """Handles data deduplication for the training pipeline"""
    
    def __init__(self, training_cutoff_date: str = "2025-07-04"):
        """
        Initialize deduplicator
        
        Args:
            training_cutoff_date: Last date used in model training (YYYY-MM-DD)
        """
        self.training_cutoff_date = training_cutoff_date
        self.logger = logger
    
    def filter_new_data_only(self, df: pl.DataFrame, date_column: str = "date") -> pl.DataFrame:
        """
        Filter dataframe to only include data after training cutoff date
        
        Args:
            df: Input dataframe
            date_column: Name of date column
            
        Returns:
            Filtered dataframe with only new data
        """
        try:
            original_rows = len(df)
            
            # Convert training cutoff to datetime for comparison
            if date_column == "date":
                # Handle DD-MM-YYYY format
                cutoff_filter = f"date > '{self.training_cutoff_date}'"
                # Convert to comparable format
                df_filtered = df.filter(
                    pl.col(date_column).str.strptime(pl.Date, "%d-%m-%Y") > 
                    pl.lit(self.training_cutoff_date).str.strptime(pl.Date, "%Y-%m-%d")
                )
            else:
                # Handle datetime column
                df_filtered = df.filter(pl.col(date_column) > self.training_cutoff_date)
            
            removed_rows = original_rows - len(df_filtered)
            self.logger.info(f"[DEBUG] Filtered out {removed_rows:,} rows before {self.training_cutoff_date}")
            self.logger.info(f"[SUCCESS] Keeping {len(df_filtered):,} new rows for training")
            
            return df_filtered
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error filtering data: {e}")
            return df
    
    def remove_duplicates(self, df: pl.DataFrame, 
                         key_columns: List[str] = ["date", "time", "stock_name"]) -> pl.DataFrame:
        """
        Remove duplicate rows based on key columns
        
        Args:
            df: Input dataframe
            key_columns: Columns to use for duplicate detection
            
        Returns:
            Deduplicated dataframe
        """
        try:
            original_rows = len(df)
            
            # Remove duplicates based on key columns
            df_dedup = df.unique(subset=key_columns)
            
            removed_rows = original_rows - len(df_dedup)
            self.logger.info(f"🧹 Removed {removed_rows:,} duplicate rows")
            
            return df_dedup
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error removing duplicates: {e}")
            return df
    
    def validate_date_range(self, df: pl.DataFrame, 
                           date_column: str = "date") -> Tuple[bool, str]:
        """
        Validate that dataframe doesn't contain data before training cutoff
        
        Args:
            df: Input dataframe
            date_column: Name of date column
            
        Returns:
            Tuple of (is_valid, message)
        """
        try:
            if len(df) == 0:
                return True, "Empty dataframe"
            
            # Get date range
            if date_column == "date":
                # Convert DD-MM-YYYY to comparable format
                min_date = df[date_column].min()
                max_date = df[date_column].max()
            else:
                min_date = df[date_column].min()
                max_date = df[date_column].max()
            
            message = f"Data range: {min_date} to {max_date}"
            
            # Check if any data is before cutoff
            if date_column == "date":
                has_old_data = df.filter(
                    pl.col(date_column).str.strptime(pl.Date, "%d-%m-%Y") <= 
                    pl.lit(self.training_cutoff_date).str.strptime(pl.Date, "%Y-%m-%d")
                ).height > 0
            else:
                has_old_data = df.filter(pl.col(date_column) <= self.training_cutoff_date).height > 0
            
            if has_old_data:
                return False, f"[WARN] {message} - Contains data before training cutoff {self.training_cutoff_date}"
            else:
                return True, f"[SUCCESS] {message} - All data is after training cutoff"
                
        except Exception as e:
            return False, f"[ERROR] Error validating date range: {e}"
    
    def process_historical_data(self, file_path: str, 
                               output_path: Optional[str] = None) -> pl.DataFrame:
        """
        Process historical data file to remove duplicates and old data
        
        Args:
            file_path: Path to input data file
            output_path: Optional path to save processed data
            
        Returns:
            Processed dataframe
        """
        try:
            self.logger.info(f"📂 Processing historical data: {file_path}")
            
            # Load data
            if file_path.endswith('.parquet'):
                df = pl.read_parquet(file_path)
            elif file_path.endswith('.csv'):
                df = pl.read_csv(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_path}")
            
            self.logger.info(f"[STATUS] Loaded {len(df):,} rows")
            
            # Step 1: Remove duplicates
            df = self.remove_duplicates(df)
            
            # Step 2: Filter to new data only
            df = self.filter_new_data_only(df)
            
            # Step 3: Validate result
            is_valid, message = self.validate_date_range(df)
            self.logger.info(message)
            
            if not is_valid:
                self.logger.warning("[WARN] Data validation failed - proceeding with caution")
            
            # Step 4: Save if output path provided
            if output_path:
                if output_path.endswith('.parquet'):
                    df.write_parquet(output_path)
                elif output_path.endswith('.csv'):
                    df.write_csv(output_path)
                self.logger.info(f"💾 Saved processed data to {output_path}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error processing historical data: {e}")
            raise

def deduplicate_feature_data(training_cutoff_date: str = "2025-07-04"):
    """
    Convenience function to deduplicate all feature files
    
    Args:
        training_cutoff_date: Last date used in model training
    """
    deduplicator = DataDeduplicator(training_cutoff_date)
    
    feature_dir = Path("data/features")
    if not feature_dir.exists():
        logger.warning("Features directory not found")
        return
    
    for file_path in feature_dir.glob("*.parquet"):
        logger.info(f"[WORKFLOW] Processing {file_path.name}")
        
        # Create backup
        backup_path = file_path.with_suffix('.backup.parquet')
        if not backup_path.exists():
            df_original = pl.read_parquet(file_path)
            df_original.write_parquet(backup_path)
            logger.info(f"[LIST] Created backup: {backup_path.name}")
        
        # Process and overwrite
        df_processed = deduplicator.process_historical_data(str(file_path))
        df_processed.write_parquet(file_path)
        
        logger.info(f"[SUCCESS] Processed {file_path.name}")

if __name__ == "__main__":
    # Example usage
    logging.basicConfig(level=logging.INFO)
    deduplicate_feature_data()
