#!/usr/bin/env python3
"""
Test Suite for Strategy Evolution Agent

This test suite validates the functionality of the Strategy Evolution Agent,
including genetic algorithms, performance tracking, and market regime adaptation.

Author: AI Assistant
Date: 2025-07-16
"""

import pytest
import asyncio
import tempfile
import json
import yaml
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import numpy as np

# Import the agent
import sys
sys.path.append(str(Path(__file__).parent.parent))

from agents.strategy_evolution_agent import (
    StrategyEvolutionAgent,
    StrategyChromosome,
    StrategyGene,
    PerformanceMetrics,
    EvolutionConfig,
    EvolutionState,
    MarketRegime,
    StrategyStatus
)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TEST FIXTURES
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.fixture
def temp_config_dir():
    """Create temporary configuration directory"""
    with tempfile.TemporaryDirectory() as temp_dir:
        config_dir = Path(temp_dir) / "config"
        config_dir.mkdir(parents=True, exist_ok=True)
        yield config_dir

@pytest.fixture
def temp_data_dir():
    """Create temporary data directory"""
    with tempfile.TemporaryDirectory() as temp_dir:
        data_dir = Path(temp_dir) / "data"
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        (data_dir / "evolved_strategies").mkdir(exist_ok=True)
        (data_dir / "evolution_performance").mkdir(exist_ok=True)
        (data_dir / "evolution_backups").mkdir(exist_ok=True)
        
        yield data_dir

@pytest.fixture
def sample_config(temp_config_dir, temp_data_dir):
    """Create sample configuration"""
    config = {
        'evolution': {
            'population_size': 10,
            'elite_size': 3,
            'mutation_rate': 0.1,
            'crossover_rate': 0.8,
            'max_generations': 5
        },
        'performance': {
            'evaluation_period_days': 7,
            'min_trades_threshold': 5,
            'fitness_weights': {
                'roi': 0.3,
                'sharpe_ratio': 0.2,
                'max_drawdown': -0.2,
                'profit_factor': 0.2,
                'win_rate': 0.1
            }
        },
        'agents': {
            'performance_analysis_agent': {'enabled': False},
            'market_monitoring_agent': {'enabled': False},
            'ai_training_agent': {'enabled': False}
        },
        'storage': {
            'strategies_dir': str(temp_data_dir / "evolved_strategies"),
            'performance_dir': str(temp_data_dir / "evolution_performance"),
            'backup_dir': str(temp_data_dir / "evolution_backups")
        },
        'timing': {
            'evolution_interval': 10,
            'monitoring_interval': 5,
            'regime_adaptation_interval': 10,
            'management_interval': 15
        }
    }
    
    config_file = temp_config_dir / "strategy_evolution_config.yaml"
    with open(config_file, 'w') as f:
        yaml.dump(config, f)
    
    return str(config_file)

@pytest.fixture
def sample_strategy_chromosome():
    """Create sample strategy chromosome"""
    genes = {
        'rsi_period': StrategyGene('rsi_period', 14, 5, 50, 0.1, 'numeric'),
        'rsi_oversold': StrategyGene('rsi_oversold', 30, 10, 40, 0.1, 'numeric'),
        'rsi_overbought': StrategyGene('rsi_overbought', 70, 60, 90, 0.1, 'numeric'),
        'stop_loss_pct': StrategyGene('stop_loss_pct', 0.02, 0.005, 0.05, 0.1, 'numeric'),
        'take_profit_pct': StrategyGene('take_profit_pct', 0.04, 0.01, 0.10, 0.1, 'numeric'),
        'position_size_pct': StrategyGene('position_size_pct', 0.05, 0.01, 0.15, 0.1, 'numeric')
    }
    
    return StrategyChromosome(
        strategy_id="test_strategy_001",
        strategy_name="Test_RSI_Strategy",
        genes=genes,
        fitness_score=0.5,
        generation=0
    )

@pytest.fixture
def sample_performance_metrics():
    """Create sample performance metrics"""
    return PerformanceMetrics(
        strategy_id="test_strategy_001",
        roi=0.15,
        sharpe_ratio=1.2,
        max_drawdown=0.08,
        profit_factor=1.5,
        win_rate=0.6,
        expectancy=0.02,
        total_trades=50,
        avg_holding_period=2.5,
        volatility=0.12,
        calmar_ratio=1.8,
        sortino_ratio=1.6
    )

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 UNIT TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestStrategyEvolutionAgent:
    """Test cases for Strategy Evolution Agent"""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, sample_config):
        """Test agent initialization"""
        agent = StrategyEvolutionAgent(sample_config)
        
        assert agent.config_path == sample_config
        assert isinstance(agent.evolution_config, EvolutionConfig)
        assert isinstance(agent.evolution_state, EvolutionState)
        assert agent.evolution_config.population_size == 10
        assert agent.evolution_config.elite_size == 3
        assert not agent.is_running
        assert agent.evolution_enabled
    
    @pytest.mark.asyncio
    async def test_agent_setup(self, sample_config):
        """Test agent setup"""
        agent = StrategyEvolutionAgent(sample_config)
        
        # Mock agent connections to avoid actual setup
        with patch.object(agent, '_setup_agent_connections', new_callable=AsyncMock):
            with patch.object(agent, '_load_existing_strategies', new_callable=AsyncMock):
                with patch.object(agent, '_initialize_evolution_state', new_callable=AsyncMock):
                    await agent.setup()
                    
                    # Verify setup was called
                    agent._setup_agent_connections.assert_called_once()
                    agent._load_existing_strategies.assert_called_once()
                    agent._initialize_evolution_state.assert_called_once()

class TestStrategyChromosome:
    """Test cases for Strategy Chromosome"""
    
    def test_chromosome_creation(self, sample_strategy_chromosome):
        """Test chromosome creation"""
        chromosome = sample_strategy_chromosome
        
        assert chromosome.strategy_id == "test_strategy_001"
        assert chromosome.strategy_name == "Test_RSI_Strategy"
        assert len(chromosome.genes) == 6
        assert chromosome.fitness_score == 0.5
        assert chromosome.generation == 0
    
    def test_chromosome_to_dict(self, sample_strategy_chromosome):
        """Test chromosome serialization"""
        chromosome = sample_strategy_chromosome
        data = chromosome.to_dict()
        
        assert data['strategy_id'] == chromosome.strategy_id
        assert data['strategy_name'] == chromosome.strategy_name
        assert 'genes' in data
        assert 'fitness_score' in data
        assert 'generation' in data
        assert 'creation_timestamp' in data

class TestPerformanceMetrics:
    """Test cases for Performance Metrics"""
    
    def test_performance_metrics_creation(self, sample_performance_metrics):
        """Test performance metrics creation"""
        metrics = sample_performance_metrics
        
        assert metrics.strategy_id == "test_strategy_001"
        assert metrics.roi == 0.15
        assert metrics.sharpe_ratio == 1.2
        assert metrics.total_trades == 50
    
    def test_fitness_score_calculation(self, sample_performance_metrics):
        """Test fitness score calculation"""
        metrics = sample_performance_metrics
        
        # Test with default weights
        fitness = metrics.calculate_fitness_score()
        assert isinstance(fitness, float)
        assert fitness > 0  # Should be positive for good metrics
        
        # Test with custom weights
        custom_weights = {
            'roi': 0.5,
            'sharpe_ratio': 0.3,
            'max_drawdown': -0.2
        }
        custom_fitness = metrics.calculate_fitness_score(custom_weights)
        assert isinstance(custom_fitness, float)

class TestGeneticAlgorithms:
    """Test cases for genetic algorithm operations"""
    
    @pytest.mark.asyncio
    async def test_mutation(self, sample_config, sample_strategy_chromosome):
        """Test chromosome mutation"""
        agent = StrategyEvolutionAgent(sample_config)
        original_chromosome = sample_strategy_chromosome
        
        # Test mutation
        mutated_chromosome = agent._mutate_chromosome(original_chromosome, mutation_rate=1.0)
        
        assert mutated_chromosome.strategy_id != original_chromosome.strategy_id
        assert mutated_chromosome.generation == agent.generation_counter
        assert len(mutated_chromosome.parent_ids) == 1
        assert mutated_chromosome.parent_ids[0] == original_chromosome.strategy_id
        
        # Check that at least some genes were mutated
        genes_changed = 0
        for gene_name in original_chromosome.genes:
            if gene_name in mutated_chromosome.genes:
                original_value = original_chromosome.genes[gene_name].value
                mutated_value = mutated_chromosome.genes[gene_name].value
                if original_value != mutated_value:
                    genes_changed += 1
        
        assert genes_changed > 0  # At least some genes should change with 100% mutation rate
    
    @pytest.mark.asyncio
    async def test_crossover(self, sample_config, sample_strategy_chromosome):
        """Test chromosome crossover"""
        agent = StrategyEvolutionAgent(sample_config)
        
        # Create two parent chromosomes
        parent1 = sample_strategy_chromosome
        parent2 = sample_strategy_chromosome
        parent2.strategy_id = "test_strategy_002"
        parent2.strategy_name = "Test_RSI_Strategy_2"
        
        # Test crossover
        offspring1, offspring2 = agent._crossover_chromosomes(parent1, parent2)
        
        assert offspring1.strategy_id != parent1.strategy_id
        assert offspring2.strategy_id != parent2.strategy_id
        assert len(offspring1.parent_ids) == 2
        assert len(offspring2.parent_ids) == 2
        assert parent1.strategy_id in offspring1.parent_ids
        assert parent2.strategy_id in offspring1.parent_ids
    
    @pytest.mark.asyncio
    async def test_tournament_selection(self, sample_config):
        """Test tournament selection"""
        agent = StrategyEvolutionAgent(sample_config)
        
        # Create population with different fitness scores
        population = []
        for i in range(10):
            chromosome = StrategyChromosome(
                strategy_id=f"strategy_{i}",
                strategy_name=f"Strategy_{i}",
                genes={},
                fitness_score=i * 0.1  # Fitness from 0.0 to 0.9
            )
            population.append(chromosome)
        
        # Test tournament selection
        selected = agent._tournament_selection(population, tournament_size=3)
        
        assert selected in population
        # Selected strategy should have relatively high fitness
        assert selected.fitness_score >= 0.5  # Should select from top performers

class TestPerformanceTracking:
    """Test cases for performance tracking"""
    
    @pytest.mark.asyncio
    async def test_performance_trend_calculation(self, sample_config):
        """Test performance trend calculation"""
        agent = StrategyEvolutionAgent(sample_config)
        
        # Create performance history with improving trend
        performance_history = []
        for i in range(5):
            metrics = PerformanceMetrics(
                strategy_id="test_strategy",
                roi=0.1 + i * 0.02,  # Improving ROI
                sharpe_ratio=1.0 + i * 0.1,
                max_drawdown=0.05,
                profit_factor=1.2,
                win_rate=0.6,
                expectancy=0.01,
                total_trades=20,
                avg_holding_period=2.0,
                volatility=0.1,
                calmar_ratio=1.5,
                sortino_ratio=1.3
            )
            performance_history.append(metrics)
        
        # Calculate trend
        trend = agent._calculate_performance_trend(performance_history)
        
        assert isinstance(trend, float)
        assert trend > 0  # Should be positive for improving performance
    
    @pytest.mark.asyncio
    async def test_strategy_fitness_simulation(self, sample_config, sample_strategy_chromosome):
        """Test strategy fitness simulation"""
        agent = StrategyEvolutionAgent(sample_config)
        
        # Test fitness simulation
        fitness = await agent._simulate_strategy_performance(sample_strategy_chromosome)
        
        assert isinstance(fitness, float)
        assert fitness >= 0.0  # Fitness should be non-negative

class TestMarketRegimeAdaptation:
    """Test cases for market regime adaptation"""
    
    @pytest.mark.asyncio
    async def test_regime_strategy_adaptation(self, sample_config, sample_strategy_chromosome):
        """Test strategy adaptation for market regimes"""
        agent = StrategyEvolutionAgent(sample_config)
        
        # Test adaptation for different regimes
        regimes = [MarketRegime.BULL, MarketRegime.BEAR, MarketRegime.SIDEWAYS, 
                  MarketRegime.HIGH_VOLATILITY, MarketRegime.LOW_VOLATILITY]
        
        for regime in regimes:
            adapted_strategy = agent._adapt_strategy_for_regime(sample_strategy_chromosome, regime)
            
            assert adapted_strategy.strategy_id != sample_strategy_chromosome.strategy_id
            assert regime.value.upper() in adapted_strategy.strategy_name
            assert len(adapted_strategy.parent_ids) == 1
            assert adapted_strategy.parent_ids[0] == sample_strategy_chromosome.strategy_id

class TestUtilityMethods:
    """Test cases for utility methods"""
    
    @pytest.mark.asyncio
    async def test_get_best_strategies(self, sample_config):
        """Test getting best strategies"""
        agent = StrategyEvolutionAgent(sample_config)
        
        # Add strategies with different fitness scores
        for i in range(5):
            chromosome = StrategyChromosome(
                strategy_id=f"strategy_{i}",
                strategy_name=f"Strategy_{i}",
                genes={},
                fitness_score=i * 0.2
            )
            agent.active_strategies[chromosome.strategy_id] = chromosome
        
        # Get best strategies
        best_strategies = agent.get_best_strategies(count=3)
        
        assert len(best_strategies) == 3
        # Should be sorted by fitness score (descending)
        for i in range(len(best_strategies) - 1):
            assert best_strategies[i].fitness_score >= best_strategies[i + 1].fitness_score
    
    @pytest.mark.asyncio
    async def test_evolution_statistics(self, sample_config):
        """Test evolution statistics"""
        agent = StrategyEvolutionAgent(sample_config)
        
        # Set some test data
        agent.evolution_state.current_generation = 5
        agent.evolution_state.best_fitness = 0.8
        agent.evolution_state.average_fitness = 0.6
        
        stats = agent.get_evolution_statistics()
        
        assert stats['current_generation'] == 5
        assert stats['best_fitness'] == 0.8
        assert stats['average_fitness'] == 0.6
        assert 'population_size' in stats
        assert 'total_strategies' in stats

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 INTEGRATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestEnhancedFeatures:
    """Test cases for enhanced Strategy Evolution Agent features"""

    @pytest.mark.asyncio
    async def test_hyperparameter_optimization(self, sample_config, sample_strategy_chromosome):
        """Test hyperparameter optimization"""
        agent = StrategyEvolutionAgent(sample_config)

        # Add optimization space to genes
        for gene in sample_strategy_chromosome.genes.values():
            gene.optimization_space = {
                'type': 'float',
                'low': gene.min_value,
                'high': gene.max_value
            }

        # Test optimization
        optimized_chromosome = await agent.optimize_strategy_hyperparameters(sample_strategy_chromosome)

        assert optimized_chromosome is not None
        assert optimized_chromosome.strategy_id != sample_strategy_chromosome.strategy_id
        assert len(optimized_chromosome.optimization_history) >= 0

    @pytest.mark.asyncio
    async def test_risk_reward_optimization(self, sample_config, sample_strategy_chromosome):
        """Test risk-reward combination optimization"""
        agent = StrategyEvolutionAgent(sample_config)

        # Test RR optimization
        optimized_chromosome = await agent.optimize_risk_reward_combinations(sample_strategy_chromosome)

        assert optimized_chromosome is not None
        assert len(optimized_chromosome.rr_combinations) > 0
        assert optimized_chromosome.best_rr_combo is not None

    @pytest.mark.asyncio
    async def test_time_aware_optimization(self, sample_config, sample_strategy_chromosome):
        """Test time-aware optimization"""
        agent = StrategyEvolutionAgent(sample_config)

        # Test time window optimization
        optimized_chromosome = await agent.optimize_time_windows(sample_strategy_chromosome)

        assert optimized_chromosome is not None
        assert len(optimized_chromosome.time_window_performance) >= 0

    @pytest.mark.asyncio
    async def test_rule_mutations(self, sample_config, sample_strategy_chromosome):
        """Test rule-based mutations"""
        agent = StrategyEvolutionAgent(sample_config)

        # Add some rules to the chromosome
        from agents.strategy_evolution_agent import StrategyRule
        sample_strategy_chromosome.rules = [
            StrategyRule(
                rule_id="test_rule_1",
                condition="RSI_14 < 30 and Volume > Volume.rolling(20).mean()",
                rule_type="entry_long",
                indicators=["RSI_14", "Volume"],
                parameters={}
            )
        ]

        # Test mutation
        mutated_chromosome = agent._mutate_chromosome(sample_strategy_chromosome)

        assert mutated_chromosome.strategy_id != sample_strategy_chromosome.strategy_id
        assert len(mutated_chromosome.rules) > 0

    @pytest.mark.asyncio
    async def test_performance_driven_selection(self, sample_config):
        """Test performance-driven selection"""
        agent = StrategyEvolutionAgent(sample_config)

        # Create population with different performance levels
        population = []
        for i in range(10):
            chromosome = StrategyChromosome(
                strategy_id=f"strategy_{i}",
                strategy_name=f"Strategy_{i}",
                genes={},
                fitness_score=i * 0.1,  # Varying fitness scores
                symbol_performance={"RELIANCE": i * 0.05, "TCS": i * 0.03}
            )
            population.append(chromosome)

        # Test performance-driven selection
        selected_population = await agent.apply_performance_driven_selection(population)

        assert len(selected_population) <= len(population)  # Some may be pruned
        assert all(isinstance(chromo, StrategyChromosome) for chromo in selected_population)

    @pytest.mark.asyncio
    async def test_autonomous_strategy_discovery(self, sample_config):
        """Test autonomous strategy discovery"""
        agent = StrategyEvolutionAgent(sample_config)

        # Test strategy discovery
        discovered_strategies = await agent.discover_new_strategies()

        assert isinstance(discovered_strategies, list)
        # Should discover some strategies even without external libraries
        assert len(discovered_strategies) >= 0

    @pytest.mark.asyncio
    async def test_strategy_yaml_generation(self, sample_config, sample_strategy_chromosome):
        """Test YAML generation for strategies"""
        agent = StrategyEvolutionAgent(sample_config)

        # Add some rules and performance data
        from agents.strategy_evolution_agent import StrategyRule
        sample_strategy_chromosome.rules = [
            StrategyRule(
                rule_id="test_rule",
                condition="RSI_14 < 30",
                rule_type="entry_long",
                indicators=["RSI_14"],
                parameters={}
            )
        ]
        sample_strategy_chromosome.regime_performance = {"bull": 0.15, "bear": -0.05}
        sample_strategy_chromosome.time_window_performance = {"morning": 0.12, "afternoon": 0.08}

        # Test YAML conversion
        yaml_content = agent._convert_strategy_to_yaml(sample_strategy_chromosome)

        assert isinstance(yaml_content, str)
        assert sample_strategy_chromosome.strategy_name in yaml_content
        assert "long:" in yaml_content
        assert "capital:" in yaml_content
        assert "fitness_score:" in yaml_content

class TestIntegration:
    """Integration tests for Strategy Evolution Agent"""

    @pytest.mark.asyncio
    async def test_full_evolution_cycle(self, sample_config):
        """Test a complete evolution cycle"""
        agent = StrategyEvolutionAgent(sample_config)

        # Mock agent connections
        with patch.object(agent, '_setup_agent_connections', new_callable=AsyncMock):
            with patch.object(agent, '_load_existing_strategies', new_callable=AsyncMock):
                await agent.setup()

        # Create initial population
        await agent._create_initial_population()

        assert len(agent.evolution_state.population) > 0
        assert len(agent.active_strategies) > 0

        # Test evolution
        with patch.object(agent, '_evaluate_population_fitness', new_callable=AsyncMock):
            new_population = await agent._evolve_generation()

            assert len(new_population) == agent.evolution_config.population_size
            assert agent.generation_counter == 1

    @pytest.mark.asyncio
    async def test_enhanced_evolution_cycle(self, sample_config):
        """Test enhanced evolution cycle with new features"""
        agent = StrategyEvolutionAgent(sample_config)

        # Enable enhanced features
        agent.evolution_config.enable_hyperparameter_optimization = True
        agent.evolution_config.enable_time_aware_optimization = True
        agent.evolution_config.enable_autonomous_discovery = True

        # Mock agent connections
        with patch.object(agent, '_setup_agent_connections', new_callable=AsyncMock):
            with patch.object(agent, '_load_existing_strategies', new_callable=AsyncMock):
                await agent.setup()

        # Create initial population
        await agent._create_initial_population()

        # Test enhanced evolution
        with patch.object(agent, '_evaluate_population_fitness', new_callable=AsyncMock):
            with patch.object(agent, '_optimize_top_performers', new_callable=AsyncMock):
                with patch.object(agent, 'discover_new_strategies', new_callable=AsyncMock) as mock_discover:
                    mock_discover.return_value = []  # Return empty list for testing

                    new_population = await agent._evolve_generation()

                    assert len(new_population) == agent.evolution_config.population_size
                    assert agent.generation_counter == 1

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 TEST RUNNER
# ═══════════════════════════════════════════════════════════════════════════════

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
