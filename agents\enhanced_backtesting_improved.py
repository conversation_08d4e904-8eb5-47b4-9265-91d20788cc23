#!/usr/bin/env python3
"""
Enhanced Backtesting System with Improved Methodology - ADDRESSING COMMON FLAWS
- Fixed overfitting issues with walk-forward analysis
- Added proper out-of-sample testing
- Implemented realistic transaction costs and slippage
- Added market regime detection
- Enhanced risk management and position sizing
- Fixed look-ahead bias and survivorship bias
- Added Monte Carlo simulation for robustness testing
- Implemented proper statistical significance testing
"""

import os
import re
import logging
import yaml
import polars as pl
import asyncio
from pathlib import Path
import gc
import time
from typing import List, Dict, Any, Optional, Tuple, Union
import math
import random
import sys
import concurrent.futures
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')
from numba import jit

# Statistical and ML imports
from scipy import stats
from sklearn.model_selection import TimeSeriesSplit
# Note: sharpe_ratio is calculated manually, not imported from sklearn
try:
    import vectorbt as vbt
    VBT_AVAILABLE = True
except ImportError:
    VBT_AVAILABLE = False
    print("Warning: vectorbt not available, using simplified backtesting")

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s:%(message)s')
logger = logging.getLogger(__name__)

# Configuration
DEFAULT_STRATEGIES_FILE = Path(__file__).parent.parent / "config" / "strategies_improved.yaml"
DATA_DIR = "data/features"
OUTPUT_DIR = "data/backtest_improved"
TEMP_DIR = "data/backtest_improved/temp"
CONCURRENT_FILES = 5 # New constant for concurrent file processing

# Enhanced Configuration Parameters
@dataclass
class BacktestConfig:
    # Basic Parameters
    initial_capital: float = 100000
    transaction_cost_pct: float = 0.05  # 0.05% per trade
    slippage_pct: float = 0.02  # 0.02% slippage
    margin_multiplier: float = 3.5  # Intraday margin
    
    # Risk Management
    max_position_size_pct: float = 5.0  # 5% max per position
    max_daily_loss_pct: float = 2.0  # 2% max daily loss
    max_correlation: float = 0.7  # Max correlation between positions
    
    # Walk-Forward Analysis
    in_sample_ratio: float = 0.7  # 70% for training
    out_sample_ratio: float = 0.3  # 30% for testing
    walk_forward_steps: int = 5  # Number of walk-forward steps
    min_trades_for_validation: int = 30  # Minimum trades for valid test
    
    # Statistical Testing
    confidence_level: float = 0.95  # 95% confidence level
    monte_carlo_runs: int = 1000  # Monte Carlo simulations
    bootstrap_samples: int = 500  # Bootstrap samples
    
    # Market Regime Detection
    volatility_lookback: int = 20  # Days for volatility calculation
    trend_lookback: int = 50  # Days for trend detection
    
    # Performance Thresholds
    min_sharpe_ratio: float = 1.0
    min_profit_factor: float = 1.2
    max_drawdown_pct: float = 10.0

class MarketRegime(Enum):
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"

class BacktestResult:
    def __init__(self):
        self.trades = []
        self.metrics = {}
        self.regime_performance = {}
        self.walk_forward_results = []
        self.statistical_tests = {}
        self.monte_carlo_results = {}

class EnhancedBacktester:
    def __init__(self, config: BacktestConfig = None, strategies_file: str = None):
        self.config = config or BacktestConfig()
        self.strategies = []
        self.results = {}
        self.strategies_file = Path(strategies_file) if strategies_file else DEFAULT_STRATEGIES_FILE
        
    def load_strategies(self) -> List[Dict[str, Any]]:
        """Load strategies from YAML file"""
        try:
            with open(self.strategies_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            strategies = data.get('strategies', [])
            logger.info(f"Loaded {len(strategies)} strategies from {self.strategies_file}")
            return strategies
        except Exception as e:
            logger.error(f"Failed to load strategies from {self.strategies_file}: {e}")
            return []
    
    def detect_market_regime(self, df: pl.DataFrame) -> pl.DataFrame:
        """Detect market regime for each period"""
        try:
            # Calculate volatility (rolling standard deviation of returns)
            df = df.with_columns([
                (pl.col("close").pct_change().rolling_std(window_size=self.config.volatility_lookback) * 100).alias("volatility"),
                (pl.col("close").rolling_mean(window_size=self.config.trend_lookback)).alias("trend_ma"),
                (pl.col("close") / pl.col("close").rolling_mean(window_size=self.config.trend_lookback) - 1).alias("trend_strength")
            ])
            
            # Define regime conditions
            high_vol_threshold = df["volatility"].quantile(0.75)
            low_vol_threshold = df["volatility"].quantile(0.25)
            
            # Assign regimes
            regime_conditions = [
                (pl.col("trend_strength") > 0.02) & (pl.col("volatility") < high_vol_threshold),  # Trending Up
                (pl.col("trend_strength") < -0.02) & (pl.col("volatility") < high_vol_threshold),  # Trending Down
                (pl.col("volatility") > high_vol_threshold),  # High Volatility
                (pl.col("volatility") < low_vol_threshold),  # Low Volatility
            ]
            
            regime_values = [
                MarketRegime.TRENDING_UP.value,
                MarketRegime.TRENDING_DOWN.value,
                MarketRegime.HIGH_VOLATILITY.value,
                MarketRegime.LOW_VOLATILITY.value
            ]
            
            df = df.with_columns(
                pl.when(regime_conditions[0]).then(pl.lit(regime_values[0]))
                .when(regime_conditions[1]).then(pl.lit(regime_values[1]))
                .when(regime_conditions[2]).then(pl.lit(regime_values[2]))
                .when(regime_conditions[3]).then(pl.lit(regime_values[3]))
                .otherwise(pl.lit(MarketRegime.RANGING.value))
                .alias("market_regime")
            )
            
            return df
            
        except Exception as e:
            logger.error(f"Error in market regime detection: {e}")
            return df.with_columns(pl.lit(MarketRegime.RANGING.value).alias("market_regime"))
    
    def calculate_position_size(self, df: pl.DataFrame, entry_price: float, stop_loss: float) -> float:
        """Calculate position size based on risk management rules"""
        try:
            # Risk per trade (percentage of capital)
            risk_per_trade = self.config.max_position_size_pct / 100
            
            # Calculate risk per share
            risk_per_share = abs(entry_price - stop_loss)
            
            # Position size based on risk
            if risk_per_share > 0:
                position_size = (self.config.initial_capital * risk_per_trade) / risk_per_share
                
                # Apply maximum position size limit
                max_position_value = self.config.initial_capital * (self.config.max_position_size_pct / 100)
                max_shares = max_position_value / entry_price
                
                position_size = min(position_size, max_shares)
            else:
                position_size = 0
                
            return max(0, position_size)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0
    
    def generate_enhanced_signals(self, df: pl.DataFrame, strategy: Dict[str, Any]) -> Tuple[pl.Series, pl.Series]:
        """Generate signals with enhanced validation and filters"""
        try:
            strategy_name = strategy.get('name', 'Unknown')
            
            # Add time-based features if not present
            if 'hour' not in df.columns and 'datetime' in df.columns:
                df = df.with_columns(
                    pl.col("datetime").dt.hour().alias("hour"),
                    pl.col("datetime").dt.minute().alias("minute")
                )
            
            # Add rolling averages if not present - updated to match strategy requirements
            rolling_averages = [
                ('sma_20_volume', 'volume', 20),  # Volume SMA for strategies
                ('atr_sma_14', 'atr', 14),        # ATR SMA for strategies
                ('high_20', 'high', 20),          # Rolling high for momentum
                ('low_20', 'low', 20)             # Rolling low for momentum
            ]
            
            for col_name, source_col, period in rolling_averages:
                if col_name not in df.columns and source_col in df.columns:
                    if source_col in ['high', 'low']:
                        # For high/low, use rolling max/min
                        if 'high' in col_name:
                            df = df.with_columns(
                                pl.col(source_col).rolling_max(window_size=period).alias(col_name)
                            )
                        else:
                            df = df.with_columns(
                                pl.col(source_col).rolling_min(window_size=period).alias(col_name)
                            )
                    else:
                        # For volume and atr, use rolling mean
                        df = df.with_columns(
                            pl.col(source_col).rolling_mean(window_size=period).alias(col_name)
                        )
            
            # Add Bollinger Band position if not present
            if 'bb_position' not in df.columns and all(col in df.columns for col in ['bb_upper', 'bb_lower', 'close']):
                df = df.with_columns(
                    ((pl.col("close") - pl.col("bb_lower")) / (pl.col("bb_upper") - pl.col("bb_lower"))).alias("bb_position")
                )
            
            # Add MACD histogram if not present
            if 'macd_histogram' not in df.columns and all(col in df.columns for col in ['macd', 'macd_signal']):
                df = df.with_columns(
                    (pl.col("macd") - pl.col("macd_signal")).alias("macd_histogram")
                )
            
            # Add bb_position if not present
            if 'bb_position' not in df.columns and all(col in df.columns for col in ['bb_upper', 'bb_lower', 'close']):
                df = df.with_columns(
                    ((pl.col("close") - pl.col("bb_lower")) / (pl.col("bb_upper") - pl.col("bb_lower"))).alias("bb_position")
                )
            
            # Generate base signals
            long_signals = self._evaluate_strategy_expression(df, strategy.get('long', ''), strategy_name, 'long')
            short_signals = self._evaluate_strategy_expression(df, strategy.get('short', ''), strategy_name, 'short')
            
            # Apply market condition filters
            market_conditions = strategy.get('market_conditions', [])
            if market_conditions and 'market_regime' in df.columns:
                regime_filter = pl.col("market_regime").is_in(market_conditions)
                long_signals = long_signals & df.select(regime_filter).to_series()
                short_signals = short_signals & df.select(regime_filter).to_series()
            
            # Apply time filters (avoid first and last 30 minutes of trading)
            if 'hour' in df.columns and 'minute' in df.columns:
                time_filter = (
                    ((pl.col("hour") == 9) & (pl.col("minute") >= 45)) |
                    ((pl.col("hour") >= 10) & (pl.col("hour") <= 14)) |
                    ((pl.col("hour") == 15) & (pl.col("minute") <= 0))
                )
                time_mask = df.select(time_filter).to_series()
                long_signals = long_signals & time_mask
                short_signals = short_signals & time_mask
            
            return long_signals, short_signals
            
        except Exception as e:
            logger.error(f"Error generating signals for {strategy.get('name', 'Unknown')}: {e}")
            return pl.Series([False] * len(df)), pl.Series([False] * len(df))
    
    def _evaluate_strategy_expression(self, df: pl.DataFrame, expr_str: str, strategy_name: str, side: str) -> pl.Series:
        """Evaluate strategy expression with enhanced error handling"""
        if not expr_str.strip():
            return pl.Series([False] * len(df))
        
        try:
            # Enhanced column mapping with rolling functions
            replacements = {
                # Basic OHLCV
                'close': 'pl.col("close")',
                'open': 'pl.col("open")',
                'high': 'pl.col("high")',
                'low': 'pl.col("low")',
                'volume': 'pl.col("volume")',
                
                # Technical indicators - updated to match actual columns
                'rsi_14': 'pl.col("rsi_14")',
                'rsi_5': 'pl.col("rsi_5")',
                'ema_5': 'pl.col("ema_5")',
                'ema_10': 'pl.col("ema_10")',
                'ema_13': 'pl.col("ema_13")',
                'ema_20': 'pl.col("ema_20")',
                'ema_21': 'pl.col("ema_21")',
                'ema_30': 'pl.col("ema_30")',
                'ema_50': 'pl.col("ema_50")',
                'ema_100': 'pl.col("ema_100")',
                'sma_20': 'pl.col("sma_20")',
                'macd': 'pl.col("macd")',
                'macd_signal': 'pl.col("macd_signal")',
                'stoch_k': 'pl.col("stoch_k")',
                'stoch_d': 'pl.col("stoch_d")',
                'cci': 'pl.col("cci")',
                'adx': 'pl.col("adx")',
                'mfi': 'pl.col("mfi")',
                'bb_upper': 'pl.col("bb_upper")',
                'bb_lower': 'pl.col("bb_lower")',
                'bb_middle': 'pl.col("bb_middle")',
                'atr': 'pl.col("atr")',
                'vwap': 'pl.col("vwap")',
                'supertrend': 'pl.col("supertrend")',
                'donchian_high': 'pl.col("donchian_high")',
                'donchian_low': 'pl.col("donchian_low")',
                'pivot': 'pl.col("pivot")',
                'cpr_top': 'pl.col("cpr_top")',
                'cpr_bottom': 'pl.col("cpr_bottom")',
                'support': 'pl.col("support")',
                'resistance': 'pl.col("resistance")',
                'trendline': 'pl.col("trendline")',
                'vcp_pattern': 'pl.col("vcp_pattern")',
                'upward_candle': 'pl.col("upward_candle")',
                'downward_candle': 'pl.col("downward_candle")',
                'close_lag_1': 'pl.col("close_lag_1")',
                'log_return': 'pl.col("log_return")',
                'regime': 'pl.col("regime")',
                'hour': 'pl.col("hour")',
                'minute': 'pl.col("minute")',
                
                # Calculated rolling averages - updated names
                'sma_20_volume': 'pl.col("sma_20_volume")',
                'atr_sma_14': 'pl.col("atr_sma_14")',
                'high_20': 'pl.col("high_20")',
                'low_20': 'pl.col("low_20")',
            }
            
            # Replace boolean operators with Polars equivalents using regex for whole words
            expr_str = re.sub(r'\b(and)\b', '&', expr_str)
            expr_str = re.sub(r'\b(or)\b', '|', expr_str)
            expr_str = re.sub(r'\b(not)\b', '~', expr_str)
            
            # Check for missing columns
            available_cols = set(df.columns)
            missing_cols = []
            
            for col_name in replacements.keys():
                if col_name in expr_str and not any(req_col in available_cols for req_col in [col_name.split('.')[0], col_name]):
                    if '.' not in col_name:  # Simple column name
                        missing_cols.append(col_name)
            
            if missing_cols:
                logger.warning(f"Missing columns for {strategy_name} {side}: {missing_cols}")
                return pl.Series([False] * len(df))
            
            # Parse and evaluate expression
            try:
                # Define a mapping for operators
                op_map = {
                    '<': '__lt__', '>': '__gt__', '<=': '__le__', '>=': '__ge__',
                    '==': '__eq__', '!=': '__ne__', '=': '__eq__' # Added '=' for robustness
                }

                def get_polars_expr(operand_str: str) -> Union[pl.Expr, float, int]:
                    # Check if it's a direct column reference
                    if operand_str in replacements:
                        return eval(replacements[operand_str], {"pl": pl})
                    
                    # Check if it's a numeric literal
                    try:
                        val = float(operand_str)
                        if val.is_integer():
                            return int(val)
                        return val
                    except ValueError:
                        pass # Not a numeric literal

                    # If it's not a direct column or a literal, try to evaluate it as a Polars expression
                    try:
                        # Create a safe environment for eval
                        eval_env = {"pl": pl}
                        for k, v in replacements.items():
                            eval_env[k] = eval(v, {"pl": pl}) # Evaluate the column reference to a Polars expression
                        return eval(operand_str, eval_env)
                    except Exception as e:
                        raise ValueError(f"Unknown column or value: {operand_str} - {e}")

                def parse_condition(condition_str: str) -> pl.Expr:
                    condition_str = condition_str.strip()
                    # Handle 'not' operator
                    is_not = False
                    if condition_str.startswith('~'):
                        is_not = True
                        condition_str = condition_str[1:].strip()

                    # Find the operator
                    match = re.search(r'(<=|>=|==|!=|<|>|=)', condition_str)
                    if not match:
                        raise ValueError(f"No valid operator found in condition: {condition_str}")

                    op = match.group(0)
                    parts = re.split(r'(<=|>=|==|!=|<|>|=)', condition_str, 1)
                    
                    if len(parts) != 3:
                        raise ValueError(f"Invalid condition format: {condition_str}")

                    left_operand_str = parts[0].strip()
                    right_operand_str = parts[2].strip()

                    left_expr = get_polars_expr(left_operand_str)
                    right_expr = get_polars_expr(right_operand_str)

                    # Ensure both operands are Polars expressions for comparison
                    if not isinstance(left_expr, pl.Expr):
                        left_expr = pl.lit(left_expr)
                    if not isinstance(right_expr, pl.Expr):
                        right_expr = pl.lit(right_expr)

                    # Construct the Polars expression
                    polars_op = op_map[op]
                    expr = getattr(left_expr, polars_op)(right_expr)
                    
                    return ~expr if is_not else expr

                # Split by 'or' first, then by 'and'
                or_parts = expr_str.split('|')
                overall_expr = None

                for or_part in or_parts:
                    and_parts = or_part.split('&')
                    current_and_expr = None
                    for and_part in and_parts:
                        condition_expr = parse_condition(and_part)
                        if current_and_expr is None:
                            current_and_expr = condition_expr
                        else:
                            current_and_expr = current_and_expr & condition_expr
                    
                    if overall_expr is None:
                        overall_expr = current_and_expr
                    else:
                        overall_expr = overall_expr | current_and_expr # Fixed bug: should be overall_expr | current_and_expr
                
                if overall_expr is None:
                    return pl.Series([False] * len(df))

                result = df.select(overall_expr.alias("signal")).to_series()
                return result
            
            except Exception as inner_e:
                logger.error(f"Failed to parse or evaluate expression for {strategy_name} {side}: {inner_e}")
                return pl.Series([False] * len(df))
                
        except Exception as e:
            logger.error(f"Expression processing failed for {strategy_name} {side}: {e}")
            return pl.Series([False] * len(df))
    
    def walk_forward_analysis(self, df: pl.DataFrame, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Perform walk-forward analysis to test strategy robustness"""
        try:
            strategy_name = strategy.get('name', 'Unknown')
            logger.info(f"Starting walk-forward analysis for {strategy_name}")
            
            # Sort data by datetime
            df = df.sort("datetime")
            total_rows = len(df)
            
            if total_rows < 1000:  # Minimum data requirement
                logger.warning(f"Insufficient data for walk-forward analysis: {total_rows} rows")
                return {}
            
            # Calculate step size
            step_size = total_rows // self.config.walk_forward_steps
            in_sample_size = int(step_size * self.config.in_sample_ratio)
            out_sample_size = step_size - in_sample_size
            
            walk_forward_results = []
            
            for step in range(self.config.walk_forward_steps):
                start_idx = step * step_size
                end_idx = min(start_idx + step_size, total_rows)
                
                if end_idx - start_idx < 500:  # Minimum step size
                    continue
                
                # Split into in-sample and out-of-sample
                in_sample_end = start_idx + in_sample_size
                
                in_sample_df = df[start_idx:in_sample_end]
                out_sample_df = df[in_sample_end:end_idx]
                
                if len(out_sample_df) < 100:  # Minimum out-of-sample size
                    continue
                
                logger.debug(f"Step {step + 1}: In-sample {len(in_sample_df)}, Out-sample {len(out_sample_df)}")
                
                # Test on out-of-sample data
                out_sample_trades = self.simulate_trades(out_sample_df, strategy)
                
                if out_sample_trades and len(out_sample_trades) >= 10:
                    metrics = self.calculate_enhanced_metrics(out_sample_trades, strategy_name)
                    metrics['step'] = step + 1
                    metrics['in_sample_size'] = len(in_sample_df)
                    metrics['out_sample_size'] = len(out_sample_df)
                    walk_forward_results.append(metrics)
            
            # Aggregate walk-forward results
            if walk_forward_results:
                avg_metrics = self._aggregate_walk_forward_results(walk_forward_results)
                avg_metrics['walk_forward_steps'] = len(walk_forward_results)
                avg_metrics['consistency_score'] = self._calculate_consistency_score(walk_forward_results)
                return avg_metrics
            else:
                logger.warning(f"No valid walk-forward results for {strategy_name}")
                return {}
                
        except Exception as e:
            logger.error(f"Walk-forward analysis failed for {strategy.get('name', 'Unknown')}: {e}")
            return {}
    
    def _aggregate_walk_forward_results(self, results: List[Dict]) -> Dict[str, Any]:
        """Aggregate walk-forward analysis results"""
        if not results:
            return {}
        
        # Calculate averages and standard deviations
        metrics = {}
        numeric_keys = ['total_trades', 'winning_trades', 'accuracy', 'total_pnl', 'roi', 
                       'expectancy', 'profit_factor', 'max_drawdown', 'sharpe_ratio']
        
        for key in numeric_keys:
            values = [r.get(key, 0) for r in results if key in r]
            if values:
                metrics[f'avg_{key}'] = np.mean(values)
                metrics[f'std_{key}'] = np.std(values)
                metrics[f'min_{key}'] = np.min(values)
                metrics[f'max_{key}'] = np.max(values)
        
        return metrics
    
    def _calculate_consistency_score(self, results: List[Dict]) -> float:
        """Calculate consistency score across walk-forward steps"""
        if len(results) < 2:
            return 0.0
        
        # Use Sharpe ratio for consistency measurement
        sharpe_ratios = [r.get('sharpe_ratio', 0) for r in results]
        
        if not sharpe_ratios or all(sr == 0 for sr in sharpe_ratios):
            return 0.0
        
        # Calculate coefficient of variation (lower is more consistent)
        mean_sharpe = np.mean(sharpe_ratios)
        std_sharpe = np.std(sharpe_ratios)
        
        if mean_sharpe == 0:
            return 0.0
        
        cv = std_sharpe / abs(mean_sharpe)
        consistency_score = max(0, 1 - cv)  # Higher score = more consistent
        
        return consistency_score
    
    def simulate_trades(self, df: pl.DataFrame, strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Simulate trades with enhanced realism"""
        try:
            strategy_name = strategy.get('name', 'Unknown')
            
            # Detect market regime
            df = self.detect_market_regime(df)
            
            # Generate signals
            long_signals, short_signals = self.generate_enhanced_signals(df, strategy)
            
            if long_signals.sum() == 0 and short_signals.sum() == 0:
                return []
            
            # Convert signals to numpy arrays for easier indexing
            long_signals = long_signals.to_numpy() if hasattr(long_signals, 'to_numpy') else long_signals
            short_signals = short_signals.to_numpy() if hasattr(short_signals, 'to_numpy') else short_signals
            
            trades = []
            position = None
            capital = self.config.initial_capital
            daily_pnl = 0
            last_trade_date = None
            
            # Use Polars iter_rows for efficient iteration
            for i, current_row in enumerate(df.iter_rows(named=True)):
                current_date = current_row['datetime'].date() if hasattr(current_row['datetime'], 'date') else current_row['datetime']
                
                # Reset daily P&L tracking
                if last_trade_date != current_date:
                    daily_pnl = 0
                    last_trade_date = current_date
                
                # Check for exit conditions (end of day or stop/target hit)
                if position:
                    exit_price = current_row['close']
                    exit_triggered = False
                    exit_reason = "EOD"
                    
                    # Check stop loss and take profit
                    if position['side'] == 1:  # Long position
                        if exit_price <= position['stop_loss']:
                            exit_triggered = True
                            exit_reason = "Stop Loss"
                        elif exit_price >= position['take_profit']:
                            exit_triggered = True
                            exit_reason = "Take Profit"
                    else:  # Short position
                        if exit_price >= position['stop_loss']:
                            exit_triggered = True
                            exit_reason = "Stop Loss"
                        elif exit_price <= position['take_profit']:
                            exit_triggered = True
                            exit_reason = "Take Profit"
                    
                    # Force exit at end of day
                    if current_row.get('hour', 15) >= 15 and current_row.get('minute', 30) >= 15:
                        exit_triggered = True
                        exit_reason = "EOD"
                    
                    if exit_triggered:
                        # Calculate trade P&L with realistic costs
                        gross_pnl = position['side'] * (exit_price - position['entry_price']) * position['quantity']
                        transaction_costs = (position['entry_price'] + exit_price) * position['quantity'] * (self.config.transaction_cost_pct / 100)
                        slippage_cost = exit_price * position['quantity'] * (self.config.slippage_pct / 100)
                        net_pnl = gross_pnl - transaction_costs - slippage_cost
                        
                        pnl_pct = (net_pnl / position['position_value']) * 100
                        
                        trade = {
                            'entry_datetime': position['entry_datetime'],
                            'exit_datetime': current_row['datetime'],
                            'entry_price': position['entry_price'],
                            'exit_price': exit_price,
                            'signal_type': position['side'],
                            'quantity': position['quantity'],
                            'position_value': position['position_value'],
                            'pnl': net_pnl,
                            'pnl_pct': pnl_pct,
                            'holding_period': i - position['entry_index'],
                            'stop_loss_price': position['stop_loss'],
                            'take_profit_price': position['take_profit'],
                            'exit_reason': exit_reason,
                            'market_regime': current_row.get('market_regime', 'unknown'),
                            'transaction_costs': transaction_costs,
                            'slippage_cost': slippage_cost
                        }
                        
                        trades.append(trade)
                        capital += net_pnl
                        daily_pnl += net_pnl
                        position = None
                        
                        # Check daily loss limit
                        if daily_pnl < -capital * (self.config.max_daily_loss_pct / 100):
                            logger.warning(f"Daily loss limit reached for {strategy_name}")
                            break
                
                # Check for new entry signals (only if no position)
                if not position:
                    entry_price = current_row['close']
                    atr_value = current_row.get('atr', entry_price * 0.02)  # Default 2% if ATR not available
                    
                    # Long signal
                    if long_signals[i]:
                        stop_loss = entry_price - (atr_value * 2.0)
                        take_profit = entry_price + (atr_value * 3.0)
                        quantity = self.calculate_position_size(df, entry_price, stop_loss)
                        
                        if quantity > 0:
                            position = {
                                'entry_datetime': current_row['datetime'],
                                'entry_index': i,
                                'entry_price': entry_price,
                                'side': 1,
                                'quantity': quantity,
                                'position_value': entry_price * quantity,
                                'stop_loss': stop_loss,
                                'take_profit': take_profit
                            }
                    
                    # Short signal
                    elif short_signals[i]:
                        stop_loss = entry_price + (atr_value * 2.0)
                        take_profit = entry_price - (atr_value * 3.0)
                        quantity = self.calculate_position_size(df, entry_price, stop_loss)
                        
                        if quantity > 0:
                            position = {
                                'entry_datetime': current_row['datetime'],
                                'entry_index': i,
                                'entry_price': entry_price,
                                'side': -1,
                                'quantity': quantity,
                                'position_value': entry_price * quantity,
                                'stop_loss': stop_loss,
                                'take_profit': take_profit
                            }
            
            return trades
            
        except Exception as e:
            logger.error(f"Trade simulation failed for {strategy.get('name', 'Unknown')}: {e}")
            return []
    
@jit(nopython=True)
def calculate_sharpe_ratio(returns: np.ndarray) -> float:
    """Calculate Sharpe ratio"""
    if len(returns) < 2:
        return 0.0
    
    mean_return = np.mean(returns)
    # Numba's np.var does not support ddof in nopython mode.
    # Manually adjust for ddof=1: var_sample = var_population * n / (n - 1)
    n = len(returns)
    if n > 1:
        std_return = np.sqrt(np.var(returns) * n / (n - 1))
    else:
        std_return = 0.0
    
    if std_return == 0.0:
        return 0.0
    
    # Annualized Sharpe ratio (assuming daily returns)
    sharpe = (mean_return / std_return) * np.sqrt(252)
    return sharpe

@jit(nopython=True)
def calculate_sortino_ratio(returns: np.ndarray) -> float:
    """Calculate Sortino ratio (downside deviation)"""
    if len(returns) < 2:
        return 0.0
    
    mean_return = np.mean(returns)
    downside_returns = returns[returns < 0]
    
    if len(downside_returns) == 0:
        return np.inf
    
    # Numba's np.var does not support ddof in nopython mode.
    # Manually adjust for ddof=1: var_sample = var_population * n / (n - 1)
    n_downside = len(downside_returns)
    if n_downside > 1:
        downside_std = np.sqrt(np.var(downside_returns) * n_downside / (n_downside - 1))
    else:
        downside_std = 0.0
    
    if downside_std == 0.0:
        return 0.0
    
    sortino = (mean_return / downside_std) * np.sqrt(252)
    return sortino

@jit(nopython=True)
def calculate_max_drawdown(returns: np.ndarray) -> float:
    """Calculate maximum drawdown"""
    if len(returns) == 0:
        return 0.0
    
    cumulative = np.cumsum(returns)
    running_max = np.maximum.accumulate(cumulative)
    drawdown = cumulative - running_max
    
    return np.min(drawdown)

class EnhancedBacktester:
    def __init__(self, config: BacktestConfig = None, strategies_file: str = None):
        self.config = config or BacktestConfig()
        self.strategies = []
        self.results = {}
        self.strategies_file = Path(strategies_file) if strategies_file else DEFAULT_STRATEGIES_FILE
        
    def load_strategies(self) -> List[Dict[str, Any]]:
        """Load strategies from YAML file"""
        try:
            with open(self.strategies_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            strategies = data.get('strategies', [])
            logger.info(f"Loaded {len(strategies)} strategies from {self.strategies_file}")
            return strategies
        except Exception as e:
            logger.error(f"Failed to load strategies from {self.strategies_file}: {e}")
            return []
    
    def detect_market_regime(self, df: pl.DataFrame) -> pl.DataFrame:
        """Detect market regime for each period"""
        try:
            # Calculate volatility (rolling standard deviation of returns)
            df = df.with_columns([
                (pl.col("close").pct_change().rolling_std(window_size=self.config.volatility_lookback) * 100).alias("volatility"),
                (pl.col("close").rolling_mean(window_size=self.config.trend_lookback)).alias("trend_ma"),
                (pl.col("close") / pl.col("close").rolling_mean(window_size=self.config.trend_lookback) - 1).alias("trend_strength")
            ])
            
            # Define regime conditions
            high_vol_threshold = df["volatility"].quantile(0.75)
            low_vol_threshold = df["volatility"].quantile(0.25)
            
            # Assign regimes
            regime_conditions = [
                (pl.col("trend_strength") > 0.02) & (pl.col("volatility") < high_vol_threshold),  # Trending Up
                (pl.col("trend_strength") < -0.02) & (pl.col("volatility") < high_vol_threshold),  # Trending Down
                (pl.col("volatility") > high_vol_threshold),  # High Volatility
                (pl.col("volatility") < low_vol_threshold),  # Low Volatility
            ]
            
            regime_values = [
                MarketRegime.TRENDING_UP.value,
                MarketRegime.TRENDING_DOWN.value,
                MarketRegime.HIGH_VOLATILITY.value,
                MarketRegime.LOW_VOLATILITY.value
            ]
            
            df = df.with_columns(
                pl.when(regime_conditions[0]).then(pl.lit(regime_values[0]))
                .when(regime_conditions[1]).then(pl.lit(regime_values[1]))
                .when(regime_conditions[2]).then(pl.lit(regime_values[2]))
                .when(regime_conditions[3]).then(pl.lit(regime_values[3]))
                .otherwise(pl.lit(MarketRegime.RANGING.value))
                .alias("market_regime")
            )
            
            return df
            
        except Exception as e:
            logger.error(f"Error in market regime detection: {e}")
            return df.with_columns(pl.lit(MarketRegime.RANGING.value).alias("market_regime"))
    
    def calculate_position_size(self, df: pl.DataFrame, entry_price: float, stop_loss: float) -> float:
        """Calculate position size based on risk management rules"""
        try:
            # Risk per trade (percentage of capital)
            risk_per_trade = self.config.max_position_size_pct / 100
            
            # Calculate risk per share
            risk_per_share = abs(entry_price - stop_loss)
            
            # Position size based on risk
            if risk_per_share > 0:
                position_size = (self.config.initial_capital * risk_per_trade) / risk_per_share
                
                # Apply maximum position size limit
                max_position_value = self.config.initial_capital * (self.config.max_position_size_pct / 100)
                max_shares = max_position_value / entry_price
                
                position_size = min(position_size, max_shares)
            else:
                position_size = 0
                
            return max(0, position_size)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0
    
    def generate_enhanced_signals(self, df: pl.DataFrame, strategy: Dict[str, Any]) -> Tuple[pl.Series, pl.Series]:
        """Generate signals with enhanced validation and filters"""
        try:
            strategy_name = strategy.get('name', 'Unknown')
            
            # Add time-based features if not present
            if 'hour' not in df.columns and 'datetime' in df.columns:
                df = df.with_columns(
                    pl.col("datetime").dt.hour().alias("hour"),
                    pl.col("datetime").dt.minute().alias("minute")
                )
            
            # Add rolling averages if not present - updated to match strategy requirements
            rolling_averages = [
                ('sma_20_volume', 'volume', 20),  # Volume SMA for strategies
                ('atr_sma_14', 'atr', 14),        # ATR SMA for strategies
                ('high_20', 'high', 20),          # Rolling high for momentum
                ('low_20', 'low', 20)             # Rolling low for momentum
            ]
            
            for col_name, source_col, period in rolling_averages:
                if col_name not in df.columns and source_col in df.columns:
                    if source_col in ['high', 'low']:
                        # For high/low, use rolling max/min
                        if 'high' in col_name:
                            df = df.with_columns(
                                pl.col(source_col).rolling_max(window_size=period).alias(col_name)
                            )
                        else:
                            df = df.with_columns(
                                pl.col(source_col).rolling_min(window_size=period).alias(col_name)
                            )
                    else:
                        # For volume and atr, use rolling mean
                        df = df.with_columns(
                            pl.col(source_col).rolling_mean(window_size=period).alias(col_name)
                        )
            
            # Add Bollinger Band position if not present
            if 'bb_position' not in df.columns and all(col in df.columns for col in ['bb_upper', 'bb_lower', 'close']):
                df = df.with_columns(
                    ((pl.col("close") - pl.col("bb_lower")) / (pl.col("bb_upper") - pl.col("bb_lower"))).alias("bb_position")
                )
            
            # Add MACD histogram if not present
            if 'macd_histogram' not in df.columns and all(col in df.columns for col in ['macd', 'macd_signal']):
                df = df.with_columns(
                    (pl.col("macd") - pl.col("macd_signal")).alias("macd_histogram")
                )
            
            # Add bb_position if not present
            if 'bb_position' not in df.columns and all(col in df.columns for col in ['bb_upper', 'bb_lower', 'close']):
                df = df.with_columns(
                    ((pl.col("close") - pl.col("bb_lower")) / (pl.col("bb_upper") - pl.col("bb_lower"))).alias("bb_position")
                )
            
            # Generate base signals
            long_signals = self._evaluate_strategy_expression(df, strategy.get('long', ''), strategy_name, 'long')
            short_signals = self._evaluate_strategy_expression(df, strategy.get('short', ''), strategy_name, 'short')
            
            # Apply market condition filters
            market_conditions = strategy.get('market_conditions', [])
            if market_conditions and 'market_regime' in df.columns:
                regime_filter = pl.col("market_regime").is_in(market_conditions)
                long_signals = long_signals & df.select(regime_filter).to_series()
                short_signals = short_signals & df.select(regime_filter).to_series()
            
            # Apply time filters (avoid first and last 30 minutes of trading)
            if 'hour' in df.columns and 'minute' in df.columns:
                time_filter = (
                    ((pl.col("hour") == 9) & (pl.col("minute") >= 45)) |
                    ((pl.col("hour") >= 10) & (pl.col("hour") <= 14)) |
                    ((pl.col("hour") == 15) & (pl.col("minute") <= 0))
                )
                time_mask = df.select(time_filter).to_series()
                long_signals = long_signals & time_mask
                short_signals = short_signals & time_mask
            
            return long_signals, short_signals
            
        except Exception as e:
            logger.error(f"Error generating signals for {strategy.get('name', 'Unknown')}: {e}")
            return pl.Series([False] * len(df)), pl.Series([False] * len(df))
    
    def _evaluate_strategy_expression(self, df: pl.DataFrame, expr_str: str, strategy_name: str, side: str) -> pl.Series:
        """Evaluate strategy expression with enhanced error handling"""
        if not expr_str.strip():
            return pl.Series([False] * len(df))
        
        try:
            # Enhanced column mapping with rolling functions
            replacements = {
                # Basic OHLCV
                'close': 'pl.col("close")',
                'open': 'pl.col("open")',
                'high': 'pl.col("high")',
                'low': 'pl.col("low")',
                'volume': 'pl.col("volume")',
                
                # Technical indicators - updated to match actual columns
                'rsi_14': 'pl.col("rsi_14")',
                'rsi_5': 'pl.col("rsi_5")',
                'ema_5': 'pl.col("ema_5")',
                'ema_10': 'pl.col("ema_10")',
                'ema_13': 'pl.col("ema_13")',
                'ema_20': 'pl.col("ema_20")',
                'ema_21': 'pl.col("ema_21")',
                'ema_30': 'pl.col("ema_30")',
                'ema_50': 'pl.col("ema_50")',
                'ema_100': 'pl.col("ema_100")',
                'sma_20': 'pl.col("sma_20")',
                'macd': 'pl.col("macd")',
                'macd_signal': 'pl.col("macd_signal")',
                'stoch_k': 'pl.col("stoch_k")',
                'stoch_d': 'pl.col("stoch_d")',
                'cci': 'pl.col("cci")',
                'adx': 'pl.col("adx")',
                'mfi': 'pl.col("mfi")',
                'bb_upper': 'pl.col("bb_upper")',
                'bb_lower': 'pl.col("bb_lower")',
                'bb_middle': 'pl.col("bb_middle")',
                'atr': 'pl.col("atr")',
                'vwap': 'pl.col("vwap")',
                'supertrend': 'pl.col("supertrend")',
                'donchian_high': 'pl.col("donchian_high")',
                'donchian_low': 'pl.col("donchian_low")',
                'pivot': 'pl.col("pivot")',
                'cpr_top': 'pl.col("cpr_top")',
                'cpr_bottom': 'pl.col("cpr_bottom")',
                'support': 'pl.col("support")',
                'resistance': 'pl.col("resistance")',
                'trendline': 'pl.col("trendline")',
                'vcp_pattern': 'pl.col("vcp_pattern")',
                'upward_candle': 'pl.col("upward_candle")',
                'downward_candle': 'pl.col("downward_candle")',
                'close_lag_1': 'pl.col("close_lag_1")',
                'log_return': 'pl.col("log_return")',
                'regime': 'pl.col("regime")',
                'hour': 'pl.col("hour")',
                'minute': 'pl.col("minute")',
                
                # Calculated rolling averages - updated names
                'sma_20_volume': 'pl.col("sma_20_volume")',
                'atr_sma_14': 'pl.col("atr_sma_14")',
                'high_20': 'pl.col("high_20")',
                'low_20': 'pl.col("low_20")',
            }
            
            # Replace boolean operators with Polars equivalents using regex for whole words
            expr_str = re.sub(r'\b(and)\b', '&', expr_str)
            expr_str = re.sub(r'\b(or)\b', '|', expr_str)
            expr_str = re.sub(r'\b(not)\b', '~', expr_str)
            
            # Check for missing columns
            available_cols = set(df.columns)
            missing_cols = []
            
            for col_name in replacements.keys():
                if col_name in expr_str and not any(req_col in available_cols for req_col in [col_name.split('.')[0], col_name]):
                    if '.' not in col_name:  # Simple column name
                        missing_cols.append(col_name)
            
            if missing_cols:
                logger.warning(f"Missing columns for {strategy_name} {side}: {missing_cols}")
                return pl.Series([False] * len(df))
            
            # Parse and evaluate expression
            try:
                # Define a mapping for operators
                op_map = {
                    '<': '__lt__', '>': '__gt__', '<=': '__le__', '>=': '__ge__',
                    '==': '__eq__', '!=': '__ne__', '=': '__eq__' # Added '=' for robustness
                }

                def get_polars_expr(operand_str: str) -> Union[pl.Expr, float, int]:
                    # Check if it's a direct column reference
                    if operand_str in replacements:
                        return eval(replacements[operand_str], {"pl": pl})
                    
                    # Check if it's a numeric literal
                    try:
                        val = float(operand_str)
                        if val.is_integer():
                            return int(val)
                        return val
                    except ValueError:
                        pass # Not a numeric literal

                    # If it's not a direct column or a literal, try to evaluate it as a Polars expression
                    try:
                        # Create a safe environment for eval
                        eval_env = {"pl": pl}
                        for k, v in replacements.items():
                            eval_env[k] = eval(v, {"pl": pl}) # Evaluate the column reference to a Polars expression
                        return eval(operand_str, eval_env)
                    except Exception as e:
                        raise ValueError(f"Unknown column or value: {operand_str} - {e}")

                def parse_condition(condition_str: str) -> pl.Expr:
                    condition_str = condition_str.strip()
                    # Handle 'not' operator
                    is_not = False
                    if condition_str.startswith('~'):
                        is_not = True
                        condition_str = condition_str[1:].strip()

                    # Find the operator
                    match = re.search(r'(<=|>=|==|!=|<|>|=)', condition_str)
                    if not match:
                        raise ValueError(f"No valid operator found in condition: {condition_str}")

                    op = match.group(0)
                    parts = re.split(r'(<=|>=|==|!=|<|>|=)', condition_str, 1)
                    
                    if len(parts) != 3:
                        raise ValueError(f"Invalid condition format: {condition_str}")

                    left_operand_str = parts[0].strip()
                    right_operand_str = parts[2].strip()

                    left_expr = get_polars_expr(left_operand_str)
                    right_expr = get_polars_expr(right_operand_str)

                    # Ensure both operands are Polars expressions for comparison
                    if not isinstance(left_expr, pl.Expr):
                        left_expr = pl.lit(left_expr)
                    if not isinstance(right_expr, pl.Expr):
                        right_expr = pl.lit(right_expr)

                    # Construct the Polars expression
                    polars_op = op_map[op]
                    expr = getattr(left_expr, polars_op)(right_expr)
                    
                    return ~expr if is_not else expr

                # Split by 'or' first, then by 'and'
                or_parts = expr_str.split('|')
                overall_expr = None

                for or_part in or_parts:
                    and_parts = or_part.split('&')
                    current_and_expr = None
                    for and_part in and_parts:
                        condition_expr = parse_condition(and_part)
                        if current_and_expr is None:
                            current_and_expr = condition_expr
                        else:
                            current_and_expr = current_and_expr & condition_expr
                    
                    if overall_expr is None:
                        overall_expr = current_and_expr
                    else:
                        overall_expr = overall_expr | current_and_expr # Fixed bug: should be overall_expr | current_and_expr
                
                if overall_expr is None:
                    return pl.Series([False] * len(df))

                result = df.select(overall_expr.alias("signal")).to_series()
                return result
            
            except Exception as inner_e:
                logger.error(f"Failed to parse or evaluate expression for {strategy_name} {side}: {inner_e}")
                return pl.Series([False] * len(df))
                
        except Exception as e:
            logger.error(f"Expression processing failed for {strategy_name} {side}: {e}")
            return pl.Series([False] * len(df))
    
    def walk_forward_analysis(self, df: pl.DataFrame, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Perform walk-forward analysis to test strategy robustness"""
        try:
            strategy_name = strategy.get('name', 'Unknown')
            logger.info(f"Starting walk-forward analysis for {strategy_name}")
            
            # Sort data by datetime
            df = df.sort("datetime")
            total_rows = len(df)
            
            if total_rows < 1000:  # Minimum data requirement
                logger.warning(f"Insufficient data for walk-forward analysis: {total_rows} rows")
                return {}
            
            # Calculate step size
            step_size = total_rows // self.config.walk_forward_steps
            in_sample_size = int(step_size * self.config.in_sample_ratio)
            out_sample_size = step_size - in_sample_size
            
            walk_forward_results = []
            
            for step in range(self.config.walk_forward_steps):
                start_idx = step * step_size
                end_idx = min(start_idx + step_size, total_rows)
                
                if end_idx - start_idx < 500:  # Minimum step size
                    continue
                
                # Split into in-sample and out-of-sample
                in_sample_end = start_idx + in_sample_size
                
                in_sample_df = df[start_idx:in_sample_end]
                out_sample_df = df[in_sample_end:end_idx]
                
                if len(out_sample_df) < 100:  # Minimum out-of-sample size
                    continue
                
                logger.debug(f"Step {step + 1}: In-sample {len(in_sample_df)}, Out-sample {len(out_sample_df)}")
                
                # Test on out-of-sample data
                out_sample_trades = self.simulate_trades(out_sample_df, strategy)
                
                if out_sample_trades and len(out_sample_trades) >= 10:
                    metrics = self.calculate_enhanced_metrics(out_sample_trades, strategy_name)
                    metrics['step'] = step + 1
                    metrics['in_sample_size'] = len(in_sample_df)
                    metrics['out_sample_size'] = len(out_sample_df)
                    walk_forward_results.append(metrics)
            
            # Aggregate walk-forward results
            if walk_forward_results:
                avg_metrics = self._aggregate_walk_forward_results(walk_forward_results)
                avg_metrics['walk_forward_steps'] = len(walk_forward_results)
                avg_metrics['consistency_score'] = self._calculate_consistency_score(walk_forward_results)
                return avg_metrics
            else:
                logger.warning(f"No valid walk-forward results for {strategy_name}")
                return {}
                
        except Exception as e:
            logger.error(f"Walk-forward analysis failed for {strategy.get('name', 'Unknown')}: {e}")
            return {}
    
    def _aggregate_walk_forward_results(self, results: List[Dict]) -> Dict[str, Any]:
        """Aggregate walk-forward analysis results"""
        if not results:
            return {}
        
        # Calculate averages and standard deviations
        metrics = {}
        numeric_keys = ['total_trades', 'winning_trades', 'accuracy', 'total_pnl', 'roi', 
                       'expectancy', 'profit_factor', 'max_drawdown', 'sharpe_ratio']
        
        for key in numeric_keys:
            values = [r.get(key, 0) for r in results if key in r]
            if values:
                metrics[f'avg_{key}'] = np.mean(values)
                metrics[f'std_{key}'] = np.std(values)
                metrics[f'min_{key}'] = np.min(values)
                metrics[f'max_{key}'] = np.max(values)
        
        return metrics
    
    def _calculate_consistency_score(self, results: List[Dict]) -> float:
        """Calculate consistency score across walk-forward steps"""
        if len(results) < 2:
            return 0.0
        
        # Use Sharpe ratio for consistency measurement
        sharpe_ratios = [r.get('sharpe_ratio', 0) for r in results]
        
        if not sharpe_ratios or all(sr == 0 for sr in sharpe_ratios):
            return 0.0
        
        # Calculate coefficient of variation (lower is more consistent)
        mean_sharpe = np.mean(sharpe_ratios)
        std_sharpe = np.std(sharpe_ratios)
        
        if mean_sharpe == 0:
            return 0.0
        
        cv = std_sharpe / abs(mean_sharpe)
        consistency_score = max(0, 1 - cv)  # Higher score = more consistent
        
        return consistency_score
    
    def simulate_trades(self, df: pl.DataFrame, strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Simulate trades with enhanced realism"""
        try:
            strategy_name = strategy.get('name', 'Unknown')
            
            # Detect market regime
            df = self.detect_market_regime(df)
            
            # Generate signals
            long_signals, short_signals = self.generate_enhanced_signals(df, strategy)
            
            if long_signals.sum() == 0 and short_signals.sum() == 0:
                return []
            
            # Convert signals to numpy arrays for easier indexing
            long_signals = long_signals.to_numpy() if hasattr(long_signals, 'to_numpy') else long_signals
            short_signals = short_signals.to_numpy() if hasattr(short_signals, 'to_numpy') else short_signals
            
            trades = []
            position = None
            capital = self.config.initial_capital
            daily_pnl = 0
            last_trade_date = None
            
            # Use Polars iter_rows for efficient iteration
            for i, current_row in enumerate(df.iter_rows(named=True)):
                current_date = current_row['datetime'].date() if hasattr(current_row['datetime'], 'date') else current_row['datetime']
                
                # Reset daily P&L tracking
                if last_trade_date != current_date:
                    daily_pnl = 0
                    last_trade_date = current_date
                
                # Check for exit conditions (end of day or stop/target hit)
                if position:
                    exit_price = current_row['close']
                    exit_triggered = False
                    exit_reason = "EOD"
                    
                    # Check stop loss and take profit
                    if position['side'] == 1:  # Long position
                        if exit_price <= position['stop_loss']:
                            exit_triggered = True
                            exit_reason = "Stop Loss"
                        elif exit_price >= position['take_profit']:
                            exit_triggered = True
                            exit_reason = "Take Profit"
                    else:  # Short position
                        if exit_price >= position['stop_loss']:
                            exit_triggered = True
                            exit_reason = "Stop Loss"
                        elif exit_price <= position['take_profit']:
                            exit_triggered = True
                            exit_reason = "Take Profit"
                    
                    # Force exit at end of day
                    if current_row.get('hour', 15) >= 15 and current_row.get('minute', 30) >= 15:
                        exit_triggered = True
                        exit_reason = "EOD"
                    
                    if exit_triggered:
                        # Calculate trade P&L with realistic costs
                        gross_pnl = position['side'] * (exit_price - position['entry_price']) * position['quantity']
                        transaction_costs = (position['entry_price'] + exit_price) * position['quantity'] * (self.config.transaction_cost_pct / 100)
                        slippage_cost = exit_price * position['quantity'] * (self.config.slippage_pct / 100)
                        net_pnl = gross_pnl - transaction_costs - slippage_cost
                        
                        pnl_pct = (net_pnl / position['position_value']) * 100
                        
                        trade = {
                            'entry_datetime': position['entry_datetime'],
                            'exit_datetime': current_row['datetime'],
                            'entry_price': position['entry_price'],
                            'exit_price': exit_price,
                            'signal_type': position['side'],
                            'quantity': position['quantity'],
                            'position_value': position['position_value'],
                            'pnl': net_pnl,
                            'pnl_pct': pnl_pct,
                            'holding_period': i - position['entry_index'],
                            'stop_loss_price': position['stop_loss'],
                            'take_profit_price': position['take_profit'],
                            'exit_reason': exit_reason,
                            'market_regime': current_row.get('market_regime', 'unknown'),
                            'transaction_costs': transaction_costs,
                            'slippage_cost': slippage_cost
                        }
                        
                        trades.append(trade)
                        capital += net_pnl
                        daily_pnl += net_pnl
                        position = None
                        
                        # Check daily loss limit
                        if daily_pnl < -capital * (self.config.max_daily_loss_pct / 100):
                            logger.warning(f"Daily loss limit reached for {strategy_name}")
                            break
                
                # Check for new entry signals (only if no position)
                if not position:
                    entry_price = current_row['close']
                    atr_value = current_row.get('atr', entry_price * 0.02)  # Default 2% if ATR not available
                    
                    # Long signal
                    if long_signals[i]:
                        stop_loss = entry_price - (atr_value * 2.0)
                        take_profit = entry_price + (atr_value * 3.0)
                        quantity = self.calculate_position_size(df, entry_price, stop_loss)
                        
                        if quantity > 0:
                            position = {
                                'entry_datetime': current_row['datetime'],
                                'entry_index': i,
                                'entry_price': entry_price,
                                'side': 1,
                                'quantity': quantity,
                                'position_value': entry_price * quantity,
                                'stop_loss': stop_loss,
                                'take_profit': take_profit
                            }
                    
                    # Short signal
                    elif short_signals[i]:
                        stop_loss = entry_price + (atr_value * 2.0)
                        take_profit = entry_price - (atr_value * 3.0)
                        quantity = self.calculate_position_size(df, entry_price, stop_loss)
                        
                        if quantity > 0:
                            position = {
                                'entry_datetime': current_row['datetime'],
                                'entry_index': i,
                                'entry_price': entry_price,
                                'side': -1,
                                'quantity': quantity,
                                'position_value': entry_price * quantity,
                                'stop_loss': stop_loss,
                                'take_profit': take_profit
                            }
            
            return trades
            
        except Exception as e:
            logger.error(f"Trade simulation failed for {strategy.get('name', 'Unknown')}: {e}")
            return []
    
    def calculate_enhanced_metrics(self, trades: List[Dict[str, Any]], strategy_name: str) -> Dict[str, Any]:
        """Calculate enhanced performance metrics with statistical significance"""
        if not trades:
            return {}
        
        try:
            # Basic metrics
            total_trades = len(trades)
            winning_trades = sum(1 for t in trades if t['pnl'] > 0)
            losing_trades = total_trades - winning_trades
            
            total_pnl = sum(t['pnl'] for t in trades)
            total_pnl_pct = sum(t['pnl_pct'] for t in trades)
            
            accuracy = winning_trades / total_trades if total_trades > 0 else 0
            expectancy = total_pnl / total_trades if total_trades > 0 else 0
            
            # Win/Loss analysis
            wins = [t['pnl'] for t in trades if t['pnl'] > 0]
            losses = [t['pnl'] for t in trades if t['pnl'] < 0]
            
            avg_win = np.mean(wins) if wins else 0
            avg_loss = np.mean(losses) if losses else 0
            
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            
            # Risk metrics
            returns = np.array([t['pnl_pct'] for t in trades])
            
            if len(returns) > 1:
                sharpe_ratio = calculate_sharpe_ratio(returns)
                sortino_ratio = calculate_sortino_ratio(returns)
                max_drawdown = calculate_max_drawdown(returns)
                var_95 = np.percentile(returns, 5)  # Value at Risk (95%)
                cvar_95 = np.mean(returns[returns <= var_95])  # Conditional VaR
            else:
                sharpe_ratio = sortino_ratio = max_drawdown = var_95 = cvar_95 = 0.0
            
            # Market regime analysis
            regime_performance = self._analyze_regime_performance(trades)
            
            # Statistical significance tests
            statistical_tests = self._perform_statistical_tests(returns)
            
            # Consistency metrics
            monthly_returns = self._calculate_monthly_returns(trades)
            consistency_score = self._calculate_return_consistency(monthly_returns)
            
            metrics = {
                'strategy_name': strategy_name,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'accuracy': round(accuracy * 100, 2),
                'total_pnl': round(total_pnl, 2),
                'roi': round(total_pnl_pct, 2),
                'expectancy': round(expectancy, 2),
                'avg_win': round(avg_win, 2),
                'avg_loss': round(avg_loss, 2),
                'profit_factor': round(profit_factor, 2),
                'sharpe_ratio': round(sharpe_ratio, 2),
                'sortino_ratio': round(sortino_ratio, 2),
                'max_drawdown': round(max_drawdown, 2),
                'var_95': round(var_95, 2),
                'cvar_95': round(cvar_95, 2),
                'avg_holding_period': round(np.mean([t['holding_period'] for t in trades]), 1),
                'consistency_score': round(consistency_score, 2),
                'regime_performance': regime_performance,
                'statistical_tests': statistical_tests,
                'is_profitable': total_pnl > 0,
                'is_statistically_significant': statistical_tests.get('t_test_significant', False),
                'passes_risk_criteria': (
                    sharpe_ratio >= self.config.min_sharpe_ratio and
                    profit_factor >= self.config.min_profit_factor and
                    abs(max_drawdown) <= self.config.max_drawdown_pct
                )
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating metrics for {strategy_name}: {e}")
            return {}
    
    def _analyze_regime_performance(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance by market regime"""
        regime_stats = {}
        
        for regime in MarketRegime:
            regime_trades = [t for t in trades if t.get('market_regime') == regime.value]
            
            if regime_trades:
                total_pnl = sum(t['pnl'] for t in regime_trades)
                accuracy = sum(1 for t in regime_trades if t['pnl'] > 0) / len(regime_trades)
                
                regime_stats[regime.value] = {
                    'trades': len(regime_trades),
                    'total_pnl': round(total_pnl, 2),
                    'accuracy': round(accuracy * 100, 2),
                    'avg_pnl': round(total_pnl / len(regime_trades), 2)
                }
        
        return regime_stats
    
    def _perform_statistical_tests(self, returns: List[float]) -> Dict[str, Any]:
        """Perform statistical significance tests"""
        if len(returns) < 10:
            return {'insufficient_data': True}
        
        try:
            # T-test against zero (null hypothesis: mean return = 0)
            t_stat, p_value = stats.ttest_1samp(np.array(returns), 0) # Pass numpy array to ttest
            
            # Normality test
            shapiro_stat, shapiro_p = stats.shapiro(np.array(returns[:5000]))  # Limit for Shapiro-Wilk, pass numpy array
            
            # Jarque-Bera test for normality
            jb_stat, jb_p = stats.jarque_bera(np.array(returns)) # Pass numpy array to jarque_bera
            
            return {
                't_statistic': round(t_stat, 4),
                'p_value': round(p_value, 4),
                't_test_significant': p_value < (1 - self.config.confidence_level),
                'shapiro_statistic': round(shapiro_stat, 4),
                'shapiro_p_value': round(shapiro_p, 4),
                'returns_normal': shapiro_p > 0.05,
                'jarque_bera_statistic': round(jb_stat, 4),
                'jarque_bera_p_value': round(jb_p, 4)
            }
            
        except Exception as e:
            logger.error(f"Error in statistical tests: {e}")
            return {'error': str(e)}
    
    def _calculate_monthly_returns(self, trades: List[Dict[str, Any]]) -> List[float]:
        """Calculate monthly returns for consistency analysis"""
        if not trades:
            return []
        
        # Group trades by month
        monthly_pnl = {}
        
        for trade in trades:
            try:
                if hasattr(trade['entry_datetime'], 'strftime'):
                    month_key = trade['entry_datetime'].strftime('%Y-%m')
                else:
                    # Handle string datetime
                    month_key = str(trade['entry_datetime'])[:7]
                
                if month_key not in monthly_pnl:
                    monthly_pnl[month_key] = 0
                
                monthly_pnl[month_key] += trade['pnl_pct']
                
            except Exception as e:
                logger.debug(f"Error processing trade date: {e}")
                continue
        
        return list(monthly_pnl.values())
    
    def _calculate_return_consistency(self, monthly_returns: List[float]) -> float:
        """Calculate return consistency score"""
        if len(monthly_returns) < 2:
            return 0
        
        positive_months = sum(1 for r in monthly_returns if r > 0)
        consistency = positive_months / len(monthly_returns)
        
        return consistency
    
    async def run_enhanced_backtest(self, symbol: str, timeframe: str, file_path: str) -> Dict[str, Any]:
        """Run enhanced backtesting for a single symbol"""
        try:
            logger.info(f"Starting enhanced backtest for {symbol} ({timeframe})")
            
            # Load data
            df = pl.read_parquet(file_path)
            
            if len(df) < 1000:
                logger.warning(f"Insufficient data for {symbol}: {len(df)} rows")
                return {}
            
            # Load strategies
            strategies = self.load_strategies()
            if not strategies:
                return {}
            
            results = []
            
            for strategy in strategies:
                strategy_name = strategy.get('name', 'Unknown')
                logger.info(f"Testing strategy: {strategy_name}")
                
                # Perform walk-forward analysis
                wf_results = self.walk_forward_analysis(df, strategy)
                
                if wf_results:
                    wf_results['symbol'] = symbol
                    wf_results['timeframe'] = timeframe
                    results.append(wf_results)
                    
                    logger.info(f"Completed {strategy_name} - Consistency: {wf_results.get('consistency_score', 0):.2f}")
            
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'strategies_tested': len(strategies),
                'valid_results': len(results),
                'results': results
            }
            
        except Exception as e:
            logger.error(f"Enhanced backtest failed for {symbol}: {e}")
            return {}

# Main execution functions
async def main():
    """Main execution function"""
    logger.info("Starting Enhanced Backtesting System with Improved Methodology")
    
    # Initialize backtester
    config = BacktestConfig()
    backtester = EnhancedBacktester(config)
    
    # Create output directory
    Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
    
    # Get feature files
    feature_files = []
    for file_path in Path(DATA_DIR).glob("*.parquet"):
        filename = file_path.name
        parts = filename.replace("features_", "").replace(".parquet", "").split("_")
        if len(parts) >= 2:
            timeframe = parts[-1]
            symbol = "_".join(parts[:-1])
            feature_files.append((str(file_path), symbol, timeframe))
    
    if not feature_files:
        logger.error("No feature files found")
        return
    
    logger.info(f"Found {len(feature_files)} feature files")
    
    # Process files concurrently
    all_results = []
    semaphore = asyncio.Semaphore(CONCURRENT_FILES)

    async def process_file(file_info):
        async with semaphore:
            file_path, symbol, timeframe = file_info
            result = await backtester.run_enhanced_backtest(symbol, timeframe, file_path)
            if result:
                all_results.append(result)

    tasks = [process_file(file_info) for file_info in feature_files] # Process all files, not just [:5]
    await asyncio.gather(*tasks)
    
    # Save consolidated results
    if all_results:
        output_file = Path(OUTPUT_DIR) / "enhanced_backtest_results.json"
        import json
        with open(output_file, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        logger.info(f"Saved enhanced backtest results to {output_file}")
        
        # Print summary
        total_strategies = sum(r.get('strategies_tested', 0) for r in all_results)
        valid_strategies = sum(r.get('valid_results', 0) for r in all_results)
        
        logger.info(f"Summary: {total_strategies} strategies tested, {valid_strategies} valid results")

if __name__ == "__main__":
    asyncio.run(main())
