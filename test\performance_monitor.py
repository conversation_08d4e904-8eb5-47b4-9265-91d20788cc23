#!/usr/bin/env python3
"""
Performance monitoring utility for concurrent backtesting
Monitors GPU utilization, memory usage, and async task performance
"""

import time
import psutil
import threading
import logging
from datetime import datetime
import csv
import os

# Try to import GPU monitoring libraries
try:
    import pynvml
    GPU_MONITORING = True
    pynvml.nvmlInit()
except ImportError:
    GPU_MONITORING = False
    print("⚠️  pynvml not available - GPU monitoring disabled")

class PerformanceMonitor:
    """Monitor system performance during backtesting"""
    
    def __init__(self, log_interval=5, save_to_file=True):
        self.log_interval = log_interval
        self.save_to_file = save_to_file
        self.monitoring = False
        self.metrics = []
        self.start_time = None
        
        # Setup logging
        self.logger = logging.getLogger("PerformanceMonitor")
        self.logger.setLevel(logging.INFO)
        
        # Create metrics file
        if save_to_file:
            os.makedirs("data", exist_ok=True)
            self.metrics_file = "data/performance_metrics.csv"
            self._init_metrics_file()
    
    def _init_metrics_file(self):
        """Initialize CSV file for metrics"""
        headers = [
            "timestamp", "elapsed_time", "cpu_percent", "memory_percent", 
            "memory_used_gb", "gpu_utilization", "gpu_memory_used", 
            "gpu_memory_total", "gpu_temperature", "active_threads"
        ]
        
        with open(self.metrics_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(headers)
    
    def get_gpu_metrics(self):
        """Get GPU metrics using pynvml"""
        if not GPU_MONITORING:
            return None, None, None, None
        
        try:
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)  # First GPU
            
            # GPU utilization
            util = pynvml.nvmlDeviceGetUtilizationRates(handle)
            gpu_util = util.gpu
            
            # Memory info
            mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            mem_used = mem_info.used / 1024**3  # Convert to GB
            mem_total = mem_info.total / 1024**3
            
            # Temperature
            temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
            
            return gpu_util, mem_used, mem_total, temp
            
        except Exception as e:
            self.logger.warning(f"Failed to get GPU metrics: {e}")
            return None, None, None, None
    
    def get_system_metrics(self):
        """Get system CPU and memory metrics"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_gb = memory.used / 1024**3
        
        # Count active threads
        active_threads = threading.active_count()
        
        return cpu_percent, memory_percent, memory_used_gb, active_threads
    
    def collect_metrics(self):
        """Collect all performance metrics"""
        timestamp = datetime.now().isoformat()
        elapsed_time = time.time() - self.start_time if self.start_time else 0
        
        # System metrics
        cpu_percent, memory_percent, memory_used_gb, active_threads = self.get_system_metrics()
        
        # GPU metrics
        gpu_util, gpu_mem_used, gpu_mem_total, gpu_temp = self.get_gpu_metrics()
        
        metrics = {
            "timestamp": timestamp,
            "elapsed_time": elapsed_time,
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent,
            "memory_used_gb": memory_used_gb,
            "gpu_utilization": gpu_util,
            "gpu_memory_used": gpu_mem_used,
            "gpu_memory_total": gpu_mem_total,
            "gpu_temperature": gpu_temp,
            "active_threads": active_threads
        }
        
        return metrics
    
    def log_metrics(self, metrics):
        """Log metrics to console and file"""
        # Console logging
        self.logger.info(
            f"CPU: {metrics['cpu_percent']:.1f}% | "
            f"RAM: {metrics['memory_used_gb']:.1f}GB ({metrics['memory_percent']:.1f}%) | "
            f"Threads: {metrics['active_threads']}"
        )
        
        if metrics['gpu_utilization'] is not None:
            self.logger.info(
                f"GPU: {metrics['gpu_utilization']:.1f}% | "
                f"VRAM: {metrics['gpu_memory_used']:.1f}/{metrics['gpu_memory_total']:.1f}GB | "
                f"Temp: {metrics['gpu_temperature']}°C"
            )
        
        # File logging
        if self.save_to_file:
            with open(self.metrics_file, 'a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    metrics['timestamp'], metrics['elapsed_time'],
                    metrics['cpu_percent'], metrics['memory_percent'],
                    metrics['memory_used_gb'], metrics['gpu_utilization'],
                    metrics['gpu_memory_used'], metrics['gpu_memory_total'],
                    metrics['gpu_temperature'], metrics['active_threads']
                ])
    
    def monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                metrics = self.collect_metrics()
                self.log_metrics(metrics)
                self.metrics.append(metrics)
                time.sleep(self.log_interval)
            except Exception as e:
                self.logger.error(f"Monitoring error: {e}")
                time.sleep(self.log_interval)
    
    def start_monitoring(self):
        """Start performance monitoring in background thread"""
        if self.monitoring:
            self.logger.warning("Monitoring already started")
            return
        
        self.monitoring = True
        self.start_time = time.time()
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        if not self.monitoring:
            return
        
        self.monitoring = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("Performance monitoring stopped")
        
        # Log summary
        if self.metrics:
            self._log_summary()
    
    def _log_summary(self):
        """Log performance summary"""
        if not self.metrics:
            return
        
        cpu_avg = sum(m['cpu_percent'] for m in self.metrics if m['cpu_percent']) / len(self.metrics)
        memory_avg = sum(m['memory_used_gb'] for m in self.metrics if m['memory_used_gb']) / len(self.metrics)
        
        gpu_metrics = [m for m in self.metrics if m['gpu_utilization'] is not None]
        if gpu_metrics:
            gpu_avg = sum(m['gpu_utilization'] for m in gpu_metrics) / len(gpu_metrics)
            gpu_mem_avg = sum(m['gpu_memory_used'] for m in gpu_metrics) / len(gpu_metrics)
            
            self.logger.info(f"📊 Performance Summary:")
            self.logger.info(f"   Average CPU: {cpu_avg:.1f}%")
            self.logger.info(f"   Average RAM: {memory_avg:.1f}GB")
            self.logger.info(f"   Average GPU: {gpu_avg:.1f}%")
            self.logger.info(f"   Average VRAM: {gpu_mem_avg:.1f}GB")
        else:
            self.logger.info(f"📊 Performance Summary:")
            self.logger.info(f"   Average CPU: {cpu_avg:.1f}%")
            self.logger.info(f"   Average RAM: {memory_avg:.1f}GB")

# Global monitor instance
performance_monitor = PerformanceMonitor()

def start_monitoring():
    """Start global performance monitoring"""
    performance_monitor.start_monitoring()

def stop_monitoring():
    """Stop global performance monitoring"""
    performance_monitor.stop_monitoring()

if __name__ == "__main__":
    # Test the monitor
    print("Testing performance monitor for 30 seconds...")
    monitor = PerformanceMonitor(log_interval=2)
    monitor.start_monitoring()
    
    try:
        time.sleep(30)
    except KeyboardInterrupt:
        print("\nStopping monitor...")
    finally:
        monitor.stop_monitoring()
