#!/usr/bin/env python3
"""
Test script for incremental training functionality
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from agents.ai_training_agent import AITrainingAgent, AITrainingConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_incremental_training():
    """Test incremental training functionality"""
    
    logger.info("🧪 Testing Incremental Training Functionality")
    logger.info("=" * 60)
    
    # Configuration for testing
    data_file = "data/historical/historical_5min.parquet"
    
    # Check if data file exists
    if not Path(data_file).exists():
        logger.error(f"❌ Data file not found: {data_file}")
        return False
    
    try:
        # Initialize AI Training Agent with incremental training enabled
        config = AITrainingConfig()
        config.incremental_training_enabled = True
        config.incremental_batch_size = 5000  # Small batch for testing
        config.max_incremental_updates = 3
        
        agent = AITrainingAgent(config)
        
        # Test 1: First training (should be full training)
        logger.info("\n🔄 Test 1: Initial Training (Full)")
        logger.info("-" * 40)
        
        results1 = await agent.train_incremental(data_file)
        
        if results1['status'] == 'success':
            logger.info(f"✅ Initial training completed")
            logger.info(f"   Training type: {results1.get('training_type', 'unknown')}")
            logger.info(f"   Data shape: {results1.get('data_shape', 'unknown')}")
            logger.info(f"   Incremental updates: {results1.get('incremental_update_count', 0)}")
        else:
            logger.error(f"❌ Initial training failed: {results1.get('error', 'Unknown error')}")
            return False
        
        # Test 2: Second training with same data (should skip)
        logger.info("\n🔄 Test 2: Same Data Training (Should Skip)")
        logger.info("-" * 40)
        
        results2 = await agent.train_incremental(data_file)
        
        if results2['status'] == 'skipped':
            logger.info(f"✅ Correctly skipped training - no significant changes")
            logger.info(f"   Training type: {results2.get('training_type', 'unknown')}")
            logger.info(f"   Message: {results2.get('message', 'No message')}")
        else:
            logger.warning(f"⚠️  Expected skip but got: {results2.get('training_type', 'unknown')}")
        
        # Test 3: Force full retrain
        logger.info("\n🔄 Test 3: Forced Full Retrain")
        logger.info("-" * 40)
        
        results3 = await agent.train_incremental(data_file, force_full_retrain=True)
        
        if results3['status'] == 'success':
            logger.info(f"✅ Forced retrain completed")
            logger.info(f"   Training type: {results3.get('training_type', 'unknown')}")
            logger.info(f"   Updates reset: {results3.get('incremental_updates_reset', False)}")
        else:
            logger.error(f"❌ Forced retrain failed: {results3.get('error', 'Unknown error')}")
        
        # Test 4: Check incremental state
        logger.info("\n🔄 Test 4: Incremental State Check")
        logger.info("-" * 40)
        
        logger.info(f"   Is trained: {agent.is_trained}")
        logger.info(f"   Incremental updates: {agent.incremental_update_count}")
        logger.info(f"   Last training: {agent.last_training_timestamp}")
        logger.info(f"   Data hash: {agent.training_data_hash[:16] if agent.training_data_hash else 'None'}...")
        logger.info(f"   Checkpoint path: {agent.data_checkpoint_path}")
        
        # Test 5: Configuration check
        logger.info("\n🔄 Test 5: Configuration Check")
        logger.info("-" * 40)
        
        logger.info(f"   Incremental enabled: {config.incremental_training_enabled}")
        logger.info(f"   Batch size: {config.incremental_batch_size:,}")
        logger.info(f"   Max updates: {config.max_incremental_updates}")
        logger.info(f"   Learning rate: {config.incremental_learning_rate}")
        
        logger.info("\n" + "=" * 60)
        logger.info("✅ Incremental Training Tests Completed Successfully!")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Main function"""
    success = await test_incremental_training()
    
    if success:
        logger.info("\n🎉 All tests passed! Incremental training is working correctly.")
    else:
        logger.error("\n💥 Tests failed! Check the logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
