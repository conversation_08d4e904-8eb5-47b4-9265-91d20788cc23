#!/usr/bin/env python3
"""
Risk Management Agent Test Runner
Simple test runner for the Risk Management Agent tests
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_tests():
    """Run Risk Management Agent tests"""
    try:
        logger.info("🧪 Starting Risk Management Agent tests...")
        
        # Get test directory
        test_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Run pytest with verbose output
        cmd = [
            sys.executable, "-m", "pytest", 
            os.path.join(test_dir, "test_risk_agent.py"),
            "-v", "--tb=short", "--color=yes"
        ]
        
        logger.info(f"Running command: {' '.join(cmd)}")
        
        # Run tests
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=test_dir)
        
        # Print output
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        # Check result
        if result.returncode == 0:
            logger.info("✅ All tests passed!")
        else:
            logger.error(f"❌ Tests failed with return code: {result.returncode}")
        
        return result.returncode == 0
        
    except Exception as e:
        logger.error(f"❌ Error running tests: {e}")
        return False

def run_specific_test(test_name: str):
    """Run a specific test"""
    try:
        logger.info(f"🧪 Running specific test: {test_name}")
        
        test_dir = os.path.dirname(os.path.abspath(__file__))
        
        cmd = [
            sys.executable, "-m", "pytest", 
            os.path.join(test_dir, "test_risk_agent.py"),
            "-k", test_name,
            "-v", "--tb=short", "--color=yes"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=test_dir)
        
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        logger.error(f"❌ Error running specific test: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are available"""
    try:
        import pytest
        import yaml
        logger.info("✅ All test dependencies available")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing test dependency: {e}")
        logger.info("Install with: pip install pytest pyyaml")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🛡️  Risk Management Agent Test Suite")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Run tests based on command line arguments
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        success = run_specific_test(test_name)
    else:
        success = run_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
