#!/usr/bin/env python3
"""
[INIT] Enhanced Signal Agent
Advanced signal enhancement system integrating multiple ML models and signal processing techniques

Features:
- Integration with existing AI training agent
- Advanced ensemble methods (stacking, blending, voting)
- Signal processing and noise reduction
- Multi-timeframe signal fusion
- Confidence-weighted predictions
- Real-time signal enhancement
"""

import os
import sys
import logging
import asyncio
import numpy as np
import pandas as pd
import polars as pl
from typing import Dict, List, Tuple, Optional, Union, Any
from datetime import datetime, timedelta
import joblib
import yaml

# Import our signal enhancement models
from agents.signal_enhancement_models import (
    SignalEnhancementConfig,
    TransformerSignalEnhancer,
    AdvancedEnsembleManager,
    SignalProcessor,
    MultiTimeframeSignalFusion,
    ConfidenceWeightedPredictor
)

# Import existing AI training agent
from agents.ai_training_agent import AITrainingAgent, AITrainingConfig

logger = logging.getLogger(__name__)

class EnhancedSignalAgent:
    """
    Enhanced Signal Agent for advanced trading signal processing and enhancement
    """
    
    def __init__(self, config_path: str = "config/signal_generation_config.yaml"):
        """Initialize Enhanced Signal Agent"""
        
        self.config_path = config_path
        self.config = self._load_config()
        
        # Initialize signal enhancement configuration
        self.signal_config = SignalEnhancementConfig()
        
        # Initialize components
        self.ai_training_agent = None
        self.ensemble_manager = AdvancedEnsembleManager(self.signal_config)
        self.signal_processor = SignalProcessor(self.signal_config)
        self.timeframe_fusion = MultiTimeframeSignalFusion(self.signal_config)
        self.confidence_predictor = ConfidenceWeightedPredictor(self.signal_config)
        
        # Model storage
        self.enhanced_models = {}
        self.transformer_models = {}
        self.performance_metrics = {}
        
        # State tracking
        self.is_initialized = False
        self.last_enhancement_time = None
        
        logger.info("[INIT] Enhanced Signal Agent initialized")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"[CONFIG] Loaded configuration from {self.config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"[CONFIG] Config file not found: {self.config_path}, using defaults")
            return {}
        except Exception as e:
            logger.error(f"[ERROR] Failed to load config: {e}")
            return {}
    
    async def initialize(self) -> bool:
        """Initialize the enhanced signal agent"""
        try:
            logger.info("[INIT] Initializing Enhanced Signal Agent...")
            
            # Initialize AI training agent
            ai_config = AITrainingConfig()
            self.ai_training_agent = AITrainingAgent(ai_config)
            
            # Load existing models if available
            await self._load_existing_models()
            
            # Initialize transformer models
            await self._initialize_transformer_models()
            
            self.is_initialized = True
            logger.info("[INIT] Enhanced Signal Agent initialization completed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize Enhanced Signal Agent: {e}")
            return False
    
    async def _load_existing_models(self):
        """Load existing trained models"""
        try:
            models_dir = "data/models"
            if os.path.exists(models_dir):
                # Load AI training agent models
                if self.ai_training_agent:
                    await self.ai_training_agent.load_models_from_registry()
                    
                    # Add base models to ensemble manager
                    if hasattr(self.ai_training_agent, 'models'):
                        for name, model in self.ai_training_agent.models.items():
                            if model is not None:
                                self.ensemble_manager.add_base_model(name, model)
                
                logger.info("[MODELS] Existing models loaded successfully")
            else:
                logger.info("[MODELS] No existing models found")
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to load existing models: {e}")
    
    async def _initialize_transformer_models(self):
        """Initialize transformer-based signal enhancement models"""
        try:
            # Determine input dimension based on feature configuration
            input_dim = len(self.signal_config.timeframes) * 50  # Approximate feature count
            
            # Create transformer models for different tasks
            tasks = ["direction", "profitability", "confidence", "roi"]
            
            for task in tasks:
                transformer = TransformerSignalEnhancer(
                    self.signal_config, 
                    input_dim=input_dim,
                    output_dim=1
                )
                self.transformer_models[task] = transformer
                
            logger.info(f"[TRANSFORMER] Initialized {len(tasks)} transformer models")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize transformer models: {e}")
    
    async def enhance_signals(self, raw_signals: Dict[str, np.ndarray],
                            features: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Enhance trading signals using advanced ML and signal processing
        
        Args:
            raw_signals: Dictionary of signal_type -> raw signals
            features: Optional feature matrix for ML enhancement
            
        Returns:
            Enhanced signals with confidence scores and metadata
        """
        try:
            if not self.is_initialized:
                await self.initialize()
            
            logger.info("[ENHANCE] Starting signal enhancement process...")
            
            enhanced_results = {}
            
            # Step 1: Apply signal processing to raw signals
            processed_signals = {}
            for signal_type, signals in raw_signals.items():
                processed = self.signal_processor.enhance_signals(signals)
                processed_signals[signal_type] = processed
            
            # Step 2: Multi-timeframe fusion if multiple timeframes available
            if len(processed_signals) > 1:
                fused_signals = self.timeframe_fusion.fuse_signals(processed_signals)
                enhanced_results["fused_signals"] = fused_signals
            
            # Step 3: ML-based enhancement using ensemble models
            if features is not None and self.ensemble_manager.base_models:
                ml_enhanced = await self._apply_ml_enhancement(features)
                enhanced_results["ml_enhanced"] = ml_enhanced
            
            # Step 4: Confidence-weighted predictions
            if self.ensemble_manager.ensemble_models:
                conf_predictions = await self._apply_confidence_weighting(features)
                enhanced_results["confidence_weighted"] = conf_predictions
            
            # Step 5: Generate final enhanced signals
            final_signals = self._combine_enhancement_results(
                processed_signals, enhanced_results
            )
            
            # Update performance tracking
            self.last_enhancement_time = datetime.now()
            
            logger.info("[ENHANCE] Signal enhancement completed successfully")
            
            return {
                "enhanced_signals": final_signals,
                "processed_signals": processed_signals,
                "enhancement_metadata": {
                    "timestamp": self.last_enhancement_time.isoformat(),
                    "methods_applied": list(enhanced_results.keys()),
                    "signal_quality_score": self._calculate_signal_quality(final_signals)
                }
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Signal enhancement failed: {e}")
            return {"error": str(e)}
    
    async def _apply_ml_enhancement(self, features: np.ndarray) -> Dict[str, Any]:
        """Apply ML-based signal enhancement"""
        try:
            ml_results = {}
            
            # Use ensemble models for prediction
            if "stacking" in self.ensemble_manager.ensemble_models:
                stacking_model = self.ensemble_manager.ensemble_models["stacking"]
                stacking_pred = stacking_model.predict(features)
                ml_results["stacking_predictions"] = stacking_pred
            
            if "voting" in self.ensemble_manager.ensemble_models:
                voting_model = self.ensemble_manager.ensemble_models["voting"]
                voting_pred = voting_model.predict(features)
                ml_results["voting_predictions"] = voting_pred
            
            # Use transformer models if available
            for task, transformer in self.transformer_models.items():
                if features.ndim == 2:
                    # Reshape for transformer (add sequence dimension)
                    seq_features = features.reshape(1, features.shape[0], features.shape[1])
                    pred, conf = transformer(seq_features)
                    ml_results[f"transformer_{task}"] = {
                        "predictions": pred.detach().numpy(),
                        "confidence": conf.detach().numpy()
                    }
            
            return ml_results
            
        except Exception as e:
            logger.error(f"[ERROR] ML enhancement failed: {e}")
            return {}
    
    async def _apply_confidence_weighting(self, features: np.ndarray) -> Dict[str, Any]:
        """Apply confidence-weighted predictions"""
        try:
            if not self.ensemble_manager.base_models:
                return {}
            
            predictions, confidences = self.confidence_predictor.predict_with_confidence(
                self.ensemble_manager.base_models, features
            )
            
            # Filter low confidence predictions
            filtered_pred, filtered_conf = self.confidence_predictor.filter_low_confidence_predictions(
                predictions, confidences
            )
            
            return {
                "predictions": predictions,
                "confidences": confidences,
                "filtered_predictions": filtered_pred,
                "filtered_confidences": filtered_conf,
                "high_confidence_ratio": len(filtered_pred) / len(predictions) if len(predictions) > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Confidence weighting failed: {e}")
            return {}
    
    def _combine_enhancement_results(self, processed_signals: Dict[str, np.ndarray],
                                   enhanced_results: Dict[str, Any]) -> np.ndarray:
        """Combine all enhancement results into final signals"""
        try:
            # Start with processed signals
            if "fused_signals" in enhanced_results:
                base_signals = enhanced_results["fused_signals"]
            else:
                # Use the first available processed signal
                base_signals = list(processed_signals.values())[0]
            
            # Apply ML enhancements if available
            if "ml_enhanced" in enhanced_results:
                ml_data = enhanced_results["ml_enhanced"]
                if "stacking_predictions" in ml_data:
                    # Blend with ML predictions
                    ml_pred = ml_data["stacking_predictions"]
                    if len(ml_pred) == len(base_signals):
                        base_signals = 0.7 * base_signals + 0.3 * ml_pred
            
            # Apply confidence weighting if available
            if "confidence_weighted" in enhanced_results:
                conf_data = enhanced_results["confidence_weighted"]
                if "filtered_predictions" in conf_data:
                    filtered_pred = conf_data["filtered_predictions"]
                    if len(filtered_pred) == len(base_signals):
                        # Use high-confidence predictions where available
                        base_signals = 0.6 * base_signals + 0.4 * filtered_pred
            
            return base_signals
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to combine enhancement results: {e}")
            return list(processed_signals.values())[0] if processed_signals else np.array([])
    
    def _calculate_signal_quality(self, signals: np.ndarray) -> float:
        """Calculate signal quality score"""
        try:
            if len(signals) == 0:
                return 0.0
            
            # Calculate various quality metrics
            signal_variance = np.var(signals)
            signal_mean = np.abs(np.mean(signals))
            signal_range = np.max(signals) - np.min(signals)
            
            # Normalize and combine metrics
            quality_score = min(1.0, (signal_variance + signal_mean + signal_range) / 3.0)
            
            return quality_score
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate signal quality: {e}")
            return 0.0
    
    async def train_enhanced_models(self, training_data: str) -> Dict[str, Any]:
        """Train enhanced signal models"""
        try:
            logger.info("[TRAIN] Starting enhanced model training...")
            
            # First train base AI models
            if self.ai_training_agent:
                ai_results = await self.ai_training_agent.train_enhanced_models(training_data)
                
                # Update ensemble manager with new models
                if hasattr(self.ai_training_agent, 'models'):
                    for name, model in self.ai_training_agent.models.items():
                        if model is not None:
                            self.ensemble_manager.add_base_model(name, model)
            
            # Load training data for ensemble training
            data = pl.read_parquet(training_data)
            
            # Prepare features and targets
            feature_cols = [col for col in data.columns if col not in ['target', 'timestamp', 'symbol']]
            X = data.select(feature_cols).to_numpy()
            y = data.select('target').to_numpy().flatten()
            
            # Split data
            split_idx = int(0.8 * len(X))
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]
            
            # Train ensemble models
            ensemble_results = {}
            
            # Train stacking ensemble
            stacking_model = self.ensemble_manager.create_stacking_ensemble(X_train, y_train)
            if stacking_model:
                ensemble_results["stacking"] = "trained"
            
            # Train voting ensemble
            voting_model = self.ensemble_manager.create_voting_ensemble()
            if voting_model:
                voting_model.fit(X_train, y_train)
                ensemble_results["voting"] = "trained"
            
            # Optimize blending weights
            blending_weights = self.ensemble_manager.create_blending_ensemble(X_val, y_val)
            if blending_weights:
                ensemble_results["blending"] = blending_weights
            
            logger.info("[TRAIN] Enhanced model training completed")
            
            return {
                "ai_training_results": ai_results if 'ai_results' in locals() else {},
                "ensemble_results": ensemble_results,
                "training_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Enhanced model training failed: {e}")
            return {"error": str(e)}
