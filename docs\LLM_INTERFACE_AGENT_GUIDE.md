# 🧠 LLM Interface Agent - Complete Guide

The LLM Interface Agent provides a natural language interface for your entire trading system, enabling users to interact with all agents and services through conversational queries.

## 🌟 Key Features

### 🗣️ Natural Language Querying
- **Plain English Queries**: Ask questions in natural language
- **Multi-Intent Understanding**: Handle complex queries with multiple intents
- **Context-Aware Responses**: Maintain conversation context across queries
- **Entity Extraction**: Automatically extract symbols, strategies, dates, and numbers

### 🧠 Intelligent Model Selection
- **Task-Specific Models**: Automatically select the best Ollama model for each task
- **Performance Optimization**: Choose models based on speed vs. accuracy requirements
- **Adaptive Learning**: Improve model selection based on performance feedback

### 🎯 Smart Agent Routing
- **Intent Classification**: Understand user intent and route to appropriate agents
- **Confidence Scoring**: Provide confidence levels for routing decisions
- **Fallback Handling**: Graceful degradation when primary agents are unavailable

### 🔌 Comprehensive Integration
- **All Trading Agents**: Seamless integration with all system agents
- **Angel One API**: Real-time trading data and operations
- **Code Generation**: Automatic code generation and bug fixing
- **Configuration Management**: Natural language config editing

## 📋 Supported Query Types

### 📊 Performance Analysis
```
"What's the ROI of my RSI strategy on RELIANCE last week?"
"Show me the Sharpe ratio for momentum strategies"
"Which strategy has the best win rate this month?"
"Compare performance of EMA vs SMA strategies"
```

### ⚙️ Trading Operations
```
"Buy 100 shares of ADANIPORTS at market price"
"What's my current position in TCS?"
"Cancel all pending orders for RELIANCE"
"Show me today's executed trades"
```

### 📈 Market Analysis
```
"What's the current market regime?"
"Show me RSI levels for NIFTY 50 stocks"
"Are there any momentum breakout signals?"
"What's the volatility in banking sector?"
```

### 💰 Portfolio & Risk
```
"What's my portfolio status?"
"How much margin do I have available?"
"Show me my risk exposure by sector"
"What's my current drawdown?"
```

### 🔧 Code Assistance
```
"Generate a new VWAP scalping strategy"
"Fix the error in my MACD calculation"
"Explain how SuperTrend indicator works"
"Optimize my backtesting algorithm"
```

### ⚙️ System Management
```
"Are all agents running properly?"
"Show me system performance metrics"
"Update RSI threshold in strategy config"
"Generate documentation for my strategies"
```

## 🚀 Quick Start

### 1. Installation & Setup

```bash
# Install dependencies
pip install langchain-ollama langgraph pyyaml

# Ensure Ollama is installed and running
ollama pull qwen3-8b
ollama pull deepseek-coder-6.7b
ollama pull mistral-7b-instruct
ollama pull codellama-7b-instruct
ollama pull phi4-mini
```

### 2. Configuration

The agent uses `config/llm_interface_config.yaml` for configuration:

```yaml
llm_interface:
  models:
    general_reasoning:
      model: "qwen3-8b"
      temperature: 0.7
      max_tokens: 2048
    
    code_generation:
      model: "deepseek-coder-6.7b"
      temperature: 0.3
      max_tokens: 4096
```

### 3. Basic Usage

```python
from agents.llm_interface_agent import LLMInterfaceAgent, QueryRequest

# Initialize agent
agent = LLMInterfaceAgent()
await agent.initialize()

# Process natural language query
query = QueryRequest(query="What's my portfolio performance today?")
response = await agent.process_query(query)

print(response.response)
```

### 4. Interactive Mode

```bash
# Run interactive demo
python agents/run_llm_interface_demo.py

# Run main agent
python agents/llm_interface_agent.py
```

## 🧠 Model Selection Strategy

### Model Capabilities

| Model | Best For | Strengths | Use Cases |
|-------|----------|-----------|-----------|
| **qwen3-8b** | General reasoning, analysis | Multilingual, balanced performance | Strategy explanations, market analysis |
| **deepseek-coder-6.7b** | Code generation | Exceptional coding skills | Strategy creation, bug fixing |
| **codellama-7b-instruct** | Code explanation | Detailed explanations | Code documentation, refactoring |
| **mistral-7b-instruct** | Quick tasks | Fast, accurate responses | Error fixing, simple queries |
| **phi4-mini** | Fast responses | Very fast inference | Status checks, summaries |

### Automatic Selection

The system automatically selects the best model based on:
- **Query Type**: Code vs. analysis vs. quick response
- **Complexity**: Simple vs. complex queries
- **Urgency**: Fast response vs. detailed analysis
- **Context**: Previous queries and session focus

## 🔀 Query Routing Logic

### Intent Classification

1. **Performance Analysis** → `performance_analysis_agent`
2. **Trading Execution** → `execution_agent`
3. **Market Monitoring** → `market_monitoring_agent`
4. **Risk Management** → `risk_agent`
5. **Signal Generation** → `signal_generation_agent`
6. **Code Assistance** → Direct LLM processing
7. **Configuration** → Config management system

### Entity Extraction

The system automatically extracts:
- **Symbols**: RELIANCE, TCS, NIFTY, etc.
- **Strategies**: RSI, MACD, EMA, SuperTrend, etc.
- **Timeframes**: 5m, 15m, 1h, daily, etc.
- **Numbers**: Quantities, percentages, amounts
- **Actions**: Buy, sell, cancel, modify, etc.

## 🔌 Angel One Integration

### Real-Time Queries

```python
# Portfolio status
"What's my current portfolio value?"

# Margin information
"How much margin do I have available?"

# Trade status
"Show me today's executed trades"

# Market data
"What's the current price of RELIANCE?"
```

### API Endpoints Used

- **Portfolio**: Holdings, positions, P&L
- **Funds**: Available margin, used margin
- **Orders**: Order book, trade book
- **Market Data**: LTP, quotes, market depth

## 🧪 Testing & Validation

### Run Tests

```bash
# Unit tests
python test/test_llm_interface_agent.py

# Model selection tests
python agents/model_selector.py

# Query routing tests
python agents/query_router.py

# Full demo
python agents/run_llm_interface_demo.py
```

### Performance Benchmarks

- **Model Selection**: ~2ms per query
- **Query Routing**: ~1ms per query
- **End-to-End Processing**: ~50-500ms (depending on model)

## 📊 Monitoring & Analytics

### Performance Metrics

```python
# Get system metrics
metrics = agent.get_performance_metrics()
print(f"Queries processed: {metrics['queries_processed']}")
print(f"Success rate: {metrics['successful_queries']/metrics['queries_processed']:.1%}")

# Model usage statistics
print(f"Model usage: {metrics['model_usage']}")
```

### Query Analytics

- **Intent Distribution**: Track most common query types
- **Model Performance**: Monitor model selection accuracy
- **Response Times**: Track processing performance
- **User Satisfaction**: Confidence scores and feedback

## 🔧 Advanced Configuration

### Custom Model Addition

```yaml
models:
  custom_model:
    model: "your-custom-model"
    description: "Custom model for specific tasks"
    use_cases: ["custom_task"]
    temperature: 0.5
    max_tokens: 2048
```

### Custom Agent Integration

```python
# Add custom agent
agent_config = {
    'module': 'your_module.custom_agent',
    'class': 'CustomAgent',
    'methods': {
        'custom_method': 'process_custom_query'
    }
}
```

### Routing Customization

```yaml
routing:
  patterns:
    custom_queries:
      keywords: ["custom", "special"]
      agent: "custom_agent"
      model: "custom_model"
```

## 🚨 Troubleshooting

### Common Issues

1. **Ollama Connection Failed**
   ```bash
   # Check Ollama status
   ollama list
   
   # Restart Ollama
   ollama serve
   ```

2. **Model Not Found**
   ```bash
   # Pull missing model
   ollama pull qwen3-8b
   ```

3. **Agent Connection Failed**
   - Check agent configuration paths
   - Verify agent initialization
   - Review log files for errors

### Debug Mode

```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Check system status
status = agent.get_system_status()
print(status)
```

## 🔮 Future Enhancements

- **Voice Interface**: Speech-to-text integration
- **Visual Charts**: Generate charts and graphs
- **Multi-Language**: Support for Hindi and other languages
- **Learning**: Adaptive responses based on user feedback
- **Plugins**: Extensible plugin system for custom functionality

## 📚 API Reference

### Core Classes

- `LLMInterfaceAgent`: Main agent class
- `QueryRequest`: Input query structure
- `QueryResponse`: Response structure
- `ModelSelector`: Model selection logic
- `QueryRouter`: Query routing logic

### Key Methods

- `process_query()`: Main query processing
- `select_model()`: Model selection
- `route_query()`: Query routing
- `get_performance_metrics()`: System metrics

---

For more detailed examples and advanced usage, see the demo scripts and test files.
