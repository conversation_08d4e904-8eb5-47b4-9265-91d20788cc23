#!/usr/bin/env python3
"""
🧪 TEST SCRIPT FOR MAIN.PY FUNCTIONALITY
═══════════════════════════════════════════════════════════════════════════════

Quick test script to verify the centralized main.py works correctly
with all the new GPU optimizations and workflow management.

Usage:
  python test_main.py

Author: AI Trading System
Version: 2.0.0 (2024-2025 Optimized)
"""

import asyncio
import sys
import logging
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.append(str(PROJECT_ROOT))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_health_check():
    """Test the health check functionality"""
    print("\n" + "="*80)
    print("🧪 TESTING HEALTH CHECK")
    print("="*80)
    
    try:
        from main import TradingSystemOrchestrator
        orchestrator = TradingSystemOrchestrator()
        
        health_status = await orchestrator.health_check()
        
        print(f"✅ Health check completed")
        print(f"📊 Overall Status: {health_status.get('overall_status', 'unknown')}")
        print(f"🔧 Components checked: {len(health_status.get('components', {}))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

async def test_gpu_optimization():
    """Test GPU optimization functionality"""
    print("\n" + "="*80)
    print("🧪 TESTING GPU OPTIMIZATION")
    print("="*80)
    
    try:
        from utils.gpu_optimizer import GPUOptimizer
        
        optimizer = GPUOptimizer()
        results = optimizer.optimize_all()
        
        print(f"✅ GPU optimization completed")
        print(f"🚀 Successful optimizations: {sum(results.values())}/{len(results)}")
        
        # Print optimization summary
        optimizer.print_optimization_summary()
        
        return True
        
    except Exception as e:
        print(f"❌ GPU optimization test failed: {e}")
        return False

async def test_status_check():
    """Test the status check functionality"""
    print("\n" + "="*80)
    print("🧪 TESTING STATUS CHECK")
    print("="*80)
    
    try:
        from main import TradingSystemOrchestrator
        orchestrator = TradingSystemOrchestrator()
        
        status = await orchestrator.get_status()
        
        print(f"✅ Status check completed")
        print(f"⏰ Uptime: {status.get('uptime_hours', 0):.2f} hours")
        print(f"🤖 Running agents: {status.get('agent_count', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Status check failed: {e}")
        return False

async def test_config_loading():
    """Test configuration loading"""
    print("\n" + "="*80)
    print("🧪 TESTING CONFIGURATION LOADING")
    print("="*80)
    
    try:
        # Test AI training config
        from agents.ai_training_agent import AITrainingConfig
        config = AITrainingConfig()
        print(f"✅ AI Training config loaded")
        print(f"📁 Data dir: {config.data_dir}")
        print(f"📁 Models dir: {config.models_dir}")
        
        # Test GPU optimization config
        from utils.gpu_optimizer import GPUOptimizer
        optimizer = GPUOptimizer()
        print(f"✅ GPU optimization config loaded")
        print(f"🔧 GPU available: {optimizer.gpu_available}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return False

async def test_requirements():
    """Test that all required packages are available"""
    print("\n" + "="*80)
    print("🧪 TESTING PACKAGE REQUIREMENTS")
    print("="*80)
    
    required_packages = [
        ('polars', 'Data processing'),
        ('lightgbm', 'Machine learning'),
        ('optuna', 'Hyperparameter optimization'),
        ('torch', 'Deep learning'),
        ('sklearn', 'Machine learning utilities'),
        ('yaml', 'Configuration'),
        ('asyncio', 'Async processing')
    ]
    
    success_count = 0
    
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"✅ {package:<15} - {description}")
            success_count += 1
        except ImportError:
            print(f"❌ {package:<15} - {description} (NOT AVAILABLE)")
    
    # Test optional packages
    optional_packages = [
        ('catboost', 'Enhanced ensemble learning'),
        ('cudf', 'GPU data processing'),
        ('cupy', 'GPU arrays')
    ]
    
    print(f"\n📦 Optional packages:")
    for package, description in optional_packages:
        try:
            __import__(package)
            print(f"✅ {package:<15} - {description}")
        except ImportError:
            print(f"⚠️ {package:<15} - {description} (Optional, not installed)")
    
    print(f"\n📊 Required packages: {success_count}/{len(required_packages)} available")
    
    return success_count == len(required_packages)

async def run_all_tests():
    """Run all tests"""
    print("🚀 STARTING COMPREHENSIVE SYSTEM TESTS")
    print("="*80)
    
    tests = [
        ("Requirements Check", test_requirements),
        ("Configuration Loading", test_config_loading),
        ("GPU Optimization", test_gpu_optimization),
        ("Health Check", test_health_check),
        ("Status Check", test_status_check)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "="*80)
    print("📊 TEST RESULTS SUMMARY")
    print("="*80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status:<10} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is ready for use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

async def main():
    """Main test function"""
    try:
        # Ensure required directories exist
        required_dirs = ['logs', 'data/features', 'data/backtest', 'data/models', 'config']
        for dir_path in required_dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        # Run all tests
        success = await run_all_tests()
        
        if success:
            print("\n🚀 You can now use main.py with confidence!")
            print("\nExample commands:")
            print("  python main.py --health_check")
            print("  python main.py --status")
            print("  python main.py --optimize_gpu")
            print("  python main.py --agent ai_training --demo")
            print("  python main.py --workflow training_pipeline")
        else:
            print("\n⚠️ Please fix the issues above before using main.py")
            sys.exit(1)
        
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
