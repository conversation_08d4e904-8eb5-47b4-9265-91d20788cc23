#!/usr/bin/env python3
"""
Test suite for Performance Analysis Agent

Tests cover:
- Performance metrics calculation
- Strategy analysis
- Execution quality analysis
- Data persistence
- Report generation
- Angel One API integration
"""

import os
import sys
import pytest
import asyncio
import tempfile
import shutil
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import polars as pl
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.performance_analysis_agent import (
    PerformanceAnalysisAgent,
    TradeMetrics,
    StrategyPerformance,
    ExecutionQuality,
    PerformanceAnalysisConfig,
    load_performance_config
)

class TestPerformanceAnalysisAgent:
    """Test cases for Performance Analysis Agent"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def test_config(self, temp_dir):
        """Create test configuration"""
        return PerformanceAnalysisConfig(
            angel_api_config={'enabled': False},
            data_sources_config={
                'execution_logs_path': os.path.join(temp_dir, 'execution.log'),
                'signals_path': os.path.join(temp_dir, 'signals'),
                'backtest_results_path': os.path.join(temp_dir, 'backtest')
            },
            analysis_config={
                'analysis_interval_minutes': 1,
                'initial_capital': 100000,
                'min_trades_for_analysis': 2
            },
            reporting_config={
                'daily_report': {'enabled': True},
                'formats': {'json': True, 'csv': True}
            },
            storage_config={
                'trade_data_path': os.path.join(temp_dir, 'trades'),
                'strategy_performance_path': os.path.join(temp_dir, 'strategies'),
                'execution_quality_path': os.path.join(temp_dir, 'execution'),
                'reports_path': os.path.join(temp_dir, 'reports'),
                'equity_curves_path': os.path.join(temp_dir, 'equity_curves')
            },
            performance_config={'processing': {'max_workers': 2}},
            logging_config={'level': 'DEBUG', 'file_logging': {'enable': False}},
            integration_config={'signal_agent': {'enabled': False}}
        )
    
    @pytest.fixture
    def sample_trades(self):
        """Create sample trade data for testing"""
        base_time = datetime.now() - timedelta(days=1)
        
        return [
            TradeMetrics(
                trade_id="trade_001",
                symbol="RELIANCE-EQ",
                strategy="VWAP_Reversal",
                timeframe="5min",
                entry_time=base_time,
                exit_time=base_time + timedelta(minutes=30),
                entry_price=2780.0,
                exit_price=2795.0,
                quantity=1,
                side="BUY",
                pnl=15.0,
                pnl_percent=0.54,
                holding_time_minutes=30.0,
                is_winner=True,
                market_regime="bull"
            ),
            TradeMetrics(
                trade_id="trade_002",
                symbol="RELIANCE-EQ",
                strategy="VWAP_Reversal",
                timeframe="5min",
                entry_time=base_time + timedelta(hours=1),
                exit_time=base_time + timedelta(hours=1, minutes=45),
                entry_price=2800.0,
                exit_price=2785.0,
                quantity=1,
                side="BUY",
                pnl=-15.0,
                pnl_percent=-0.54,
                holding_time_minutes=45.0,
                is_winner=False,
                market_regime="bull"
            ),
            TradeMetrics(
                trade_id="trade_003",
                symbol="TCS-EQ",
                strategy="RSI_Divergence",
                timeframe="15min",
                entry_time=base_time + timedelta(hours=2),
                exit_time=base_time + timedelta(hours=2, minutes=60),
                entry_price=3450.0,
                exit_price=3475.0,
                quantity=1,
                side="BUY",
                pnl=25.0,
                pnl_percent=0.72,
                holding_time_minutes=60.0,
                is_winner=True,
                market_regime="sideways"
            )
        ]
    
    @pytest.fixture
    async def agent(self, test_config):
        """Create Performance Analysis Agent for testing"""
        with patch('agents.performance_analysis_agent.load_performance_config', return_value=test_config):
            agent = PerformanceAnalysisAgent()
            await agent.setup()
            yield agent
            await agent.stop()
    
    def test_config_loading(self, temp_dir):
        """Test configuration loading"""
        # Create test config file
        config_data = {
            'angel_one_api': {'enabled': True},
            'analysis': {'initial_capital': 50000},
            'storage': {'trade_data_path': temp_dir}
        }
        
        config_file = os.path.join(temp_dir, 'test_config.yaml')
        with open(config_file, 'w') as f:
            import yaml
            yaml.dump(config_data, f)
        
        # Load configuration
        config = load_performance_config(config_file)
        
        assert config.angel_api_config['enabled'] == True
        assert config.analysis_config['initial_capital'] == 50000
        assert config.storage_config['trade_data_path'] == temp_dir
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, agent):
        """Test agent initialization"""
        assert agent is not None
        assert agent.config is not None
        assert isinstance(agent.trade_data, list)
        assert isinstance(agent.strategy_performance, dict)
        assert isinstance(agent.execution_quality, dict)
    
    @pytest.mark.asyncio
    async def test_trade_metrics_calculation(self, agent, sample_trades):
        """Test trade metrics calculation"""
        # Add sample trades
        agent.trade_data = sample_trades
        
        # Calculate performance metrics
        await agent._calculate_performance_metrics()
        
        # Verify calculations
        assert len(agent.trade_data) == 3
        
        # Check individual trade calculations
        for trade in agent.trade_data:
            if trade.trade_id == "trade_001":
                assert trade.pnl == 15.0
                assert trade.is_winner == True
                assert trade.holding_time_minutes == 30.0
    
    @pytest.mark.asyncio
    async def test_strategy_performance_calculation(self, agent, sample_trades):
        """Test strategy performance calculation"""
        # Add sample trades
        agent.trade_data = sample_trades
        
        # Update strategy performance
        await agent._update_strategy_performance()
        
        # Verify strategy performance calculations
        assert len(agent.strategy_performance) > 0
        
        # Check VWAP_Reversal strategy for RELIANCE-EQ
        vwap_key = "VWAP_Reversal_RELIANCE-EQ_5min_bull"
        if vwap_key in agent.strategy_performance:
            strategy_perf = agent.strategy_performance[vwap_key]
            assert strategy_perf.total_trades == 2
            assert strategy_perf.winning_trades == 1
            assert strategy_perf.losing_trades == 1
            assert strategy_perf.win_rate == 0.5
    
    @pytest.mark.asyncio
    async def test_execution_quality_calculation(self, agent, sample_trades):
        """Test execution quality calculation"""
        # Add sample trades with execution data
        for trade in sample_trades:
            trade.signal_to_fill_ms = 150.0  # 150ms signal to fill
            trade.slippage_entry = 0.1  # 0.1% slippage
        
        agent.trade_data = sample_trades
        
        # Update execution quality
        await agent._update_execution_quality()
        
        # Verify execution quality calculations
        assert len(agent.execution_quality) > 0
        
        # Check execution quality for RELIANCE-EQ
        reliance_key = "RELIANCE-EQ_VWAP_Reversal"
        if reliance_key in agent.execution_quality:
            exec_quality = agent.execution_quality[reliance_key]
            assert exec_quality.avg_signal_to_fill_ms == 150.0
            assert exec_quality.avg_slippage_percent == 0.1
    
    @pytest.mark.asyncio
    async def test_data_persistence(self, agent, sample_trades):
        """Test data saving and loading"""
        # Add sample trades
        agent.trade_data = sample_trades
        await agent._update_strategy_performance()
        await agent._update_execution_quality()
        
        # Save data
        await agent._save_all_data()
        
        # Verify files were created
        trade_file = os.path.join(agent.config.storage_config['trade_data_path'], 'trades.parquet')
        strategy_file = os.path.join(agent.config.storage_config['strategy_performance_path'], 'strategy_performance.parquet')
        
        assert os.path.exists(trade_file)
        assert os.path.exists(strategy_file)
        
        # Test loading data
        agent.trade_data = []
        agent.strategy_performance = {}
        
        await agent._load_existing_trade_data()
        await agent._load_existing_strategy_performance()
        
        assert len(agent.trade_data) == 3
        assert len(agent.strategy_performance) > 0
    
    @pytest.mark.asyncio
    async def test_report_generation(self, agent, sample_trades):
        """Test report generation"""
        # Add sample trades and calculate performance
        agent.trade_data = sample_trades
        await agent._update_strategy_performance()
        
        # Generate daily report
        await agent._generate_daily_report()
        
        # Verify report was created
        report_file = os.path.join(
            agent.config.storage_config['reports_path'],
            f"daily_report_{datetime.now().strftime('%Y%m%d')}.json"
        )
        
        assert os.path.exists(report_file)
        
        # Verify report content
        with open(report_file, 'r') as f:
            report_data = json.load(f)
        
        assert 'summary' in report_data
        assert 'strategy_performance' in report_data
        assert report_data['summary']['total_trades'] == 3
    
    @pytest.mark.asyncio
    async def test_equity_curve_calculation(self, agent, sample_trades):
        """Test equity curve calculation"""
        # Add sample trades
        agent.trade_data = sample_trades
        
        # Update equity curves
        await agent._update_equity_curves()
        
        # Verify equity curves were calculated
        assert len(agent.equity_curves) > 0
        
        # Check equity curve structure
        for curve in agent.equity_curves.values():
            assert isinstance(curve, pl.DataFrame)
            assert 'timestamp' in curve.columns
            assert 'cumulative_pnl' in curve.columns
    
    def test_sharpe_ratio_calculation(self, agent):
        """Test Sharpe ratio calculation"""
        # Create test DataFrame with returns
        test_data = {
            'pnl_percent': [1.0, -0.5, 2.0, -1.0, 1.5]
        }
        df = pl.DataFrame(test_data)
        
        # Calculate Sharpe ratio
        sharpe_ratio = agent._calculate_sharpe_ratio(df)
        
        # Verify calculation
        assert isinstance(sharpe_ratio, float)
        assert sharpe_ratio != 0.0  # Should have some value
    
    def test_drawdown_calculation(self, agent):
        """Test drawdown calculation"""
        # Create test DataFrame with PnL
        test_data = {
            'pnl': [10, -5, 15, -20, 25],
            'entry_time': [
                datetime.now() - timedelta(hours=i) for i in range(5, 0, -1)
            ]
        }
        df = pl.DataFrame(test_data)
        
        # Calculate drawdown
        max_drawdown, max_dd_duration = agent._calculate_drawdown(df)
        
        # Verify calculation
        assert isinstance(max_drawdown, float)
        assert max_drawdown >= 0
        assert isinstance(max_dd_duration, float)
    
    @pytest.mark.asyncio
    async def test_angel_one_integration(self, agent):
        """Test Angel One API integration"""
        # Mock Angel One API
        mock_api = Mock()
        mock_api.get_tradebook = AsyncMock(return_value={
            'data': [
                {
                    'orderid': 'ANG001',
                    'tradingsymbol': 'RELIANCE-EQ',
                    'filltime': '2024-01-15 10:30:00',
                    'fillprice': '2780.0',
                    'fillsize': '1',
                    'transactiontype': 'BUY',
                    'brokerage': '5.0'
                }
            ]
        })
        
        agent.angel_api = mock_api
        
        # Fetch Angel One trades
        await agent._fetch_angel_one_trades()
        
        # Verify trade was added
        assert len(agent.trade_data) > 0
        
        # Verify trade data
        trade = agent.trade_data[0]
        assert trade.trade_id == 'ANG001'
        assert trade.symbol == 'RELIANCE-EQ'
        assert trade.entry_price == 2780.0

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
