#!/usr/bin/env python3
"""
Enhanced Strategy Generation Agent - Advanced Strategy Creation & Management

Features:
🧠 1. Regime-Aware Strategy Tagging
- Market regime-based strategy filtering (trending, sideways, volatile, calm)
- Volatility regime classification (low IV, high IV, IV crush setups)
- Conditional strategy activation based on regime scores

📅 2. Event-Driven Strategies
- Weekly/Monthly expiry strategies
- RBI/Budget day event strategies
- Gap-up/gap-down detection and routing
- Dynamic event-based strategy activation

[TIME] 3. Time-of-Day Based Strategy Routing
- Pre-market, opening range breakout (9:15–10:15)
- Mid-day lull, power hour (2:00–3:15) strategies
- Time-dependent triggers and routing

[TARGET] 4. Inter-Strategy Ensemble Scoring
- Voting and confidence aggregation for multiple triggers
- Strategy confidence engine for conflicting signals
- Past accuracy, ROI, and expectancy-based scoring

[WARN] 5. Risk-Aware Strategy Filtering
- Capital at risk calculation and filtering
- Expected drawdown estimation
- Dynamic strategy suppression based on performance

[STATUS] 6. Strategy Meta-data Versioning
- 30-day success rate tracking
- Hit ratio by timeframe analysis
- Ideal holding period and volatility range tracking

[WORKFLOW] 7. Synthetic Strategy Creation Module
- Auto-generation by combining filters
- Backtesting integration for new strategies
- Automatic naming, scoring, and versioning

🏷️ 8. Strategy Intent Tags
- Objective-based tagging (breakout, IV expansion, gap fill, mean reversion)
- Confidence type classification (backtested, live adaptive, AI-scored)
- Asset-specific tagging (BANKNIFTY only, both NIFTY & BANKNIFTY)

[DEBUG] 9. Multi-Layer Filtering Pipeline
- Strategy trigger validation
- Confidence + risk scoring
- Time of day + market regime compatibility
- Final signal generation with comprehensive checks
"""

import os
import sys
import asyncio
import logging
import json
import yaml
import polars as pl
import pyarrow as pa
import pyarrow.compute as pc
from datetime import datetime, timedelta, time
from typing import Dict, List, Any, Optional, Tuple, Union, Set
from dataclasses import dataclass, asdict, field
from collections import defaultdict, deque
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Technical analysis with Polars extension
try:
    import polars_talib as ta
    POLARS_TALIB_AVAILABLE = True
except ImportError:
    print("[WARN]  polars-talib not installed. Install with: pip install polars-talib")
    POLARS_TALIB_AVAILABLE = False

# Statistical functions
import numpy as np
from scipy import stats
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score

# Import other agents
try:
    from market_monitoring_agent import MarketMonitoringAgent, MarketTick, OHLCV, MarketIndicators, MarketRegime
except ImportError:
    print("[WARN]  Market Monitoring Agent not found. Some features will be disabled.")
    MarketMonitoringAgent = None

try:
    from enhanced_backtesting_polars import EnhancedBacktestingAgent
except ImportError:
    print("[WARN]  Enhanced Backtesting Agent not found. Strategy testing will be disabled.")
    EnhancedBacktestingAgent = None

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] ENUMS AND DATA STRUCTURES
# ═══════════════════════════════════════════════════════════════════════════════

class MarketRegimeType(Enum):
    """Market regime classifications"""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    CALM = "calm"
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGE_BOUND = "range_bound"

class VolatilityRegimeType(Enum):
    """Volatility regime classifications"""
    LOW_IV = "low_IV"
    HIGH_IV = "high_IV"
    IV_EXPANSION = "IV_expansion"
    IV_CRUSH = "IV_crush"
    NORMAL_IV = "normal_IV"

class EventType(Enum):
    """Event-driven strategy types"""
    WEEKLY_EXPIRY = "weekly_expiry"
    MONTHLY_EXPIRY = "monthly_expiry"
    RBI_POLICY = "rbi_policy"
    BUDGET_DAY = "budget_day"
    GAP_UP = "gap_up"
    GAP_DOWN = "gap_down"
    EARNINGS = "earnings"
    DIVIDEND = "dividend"

class TimeWindow(Enum):
    """Trading time windows"""
    PRE_MARKET = "pre_market"          # 9:00-9:15
    OPENING_RANGE = "opening_range"    # 9:15-10:15
    MORNING_SESSION = "morning_session" # 10:15-11:30
    MIDDAY_LULL = "midday_lull"       # 11:30-13:30
    AFTERNOON_SESSION = "afternoon_session" # 13:30-14:30
    POWER_HOUR = "power_hour"         # 14:30-15:15
    CLOSING = "closing"               # 15:15-15:30

class StrategyObjective(Enum):
    """Strategy objective classifications"""
    BREAKOUT = "breakout"
    MEAN_REVERSION = "mean_reversion"
    MOMENTUM = "momentum"
    IV_EXPANSION = "iv_expansion"
    GAP_FILL = "gap_fill"
    SCALPING = "scalping"
    SWING = "swing"
    ARBITRAGE = "arbitrage"

class ConfidenceType(Enum):
    """Strategy confidence classifications"""
    BACKTESTED = "backtested"
    LIVE_ADAPTIVE = "live_adaptive"
    AI_SCORED = "ai_scored"
    ENSEMBLE = "ensemble"
    EXPERIMENTAL = "experimental"

@dataclass
class StrategyMetadata:
    """Comprehensive strategy metadata"""
    id: str
    name: str
    description: str
    enabled: bool = True
    
    # Asset and timeframe configuration
    applicable_assets: List[str] = field(default_factory=list)
    timeframes: List[str] = field(default_factory=list)
    
    # Time and event configuration
    trigger_window: Optional[str] = None
    expiry_types: List[str] = field(default_factory=list)
    event_types: List[EventType] = field(default_factory=list)
    
    # Regime configuration
    market_regime: List[MarketRegimeType] = field(default_factory=list)
    volatility_regime: List[VolatilityRegimeType] = field(default_factory=list)
    
    # Signal logic
    signal_logic: Dict[str, Any] = field(default_factory=dict)
    
    # Confidence and scoring
    confidence_scoring: Dict[str, Any] = field(default_factory=dict)
    min_confidence_threshold: float = 0.7
    
    # Risk management
    risk_model: Dict[str, Any] = field(default_factory=dict)
    max_capital_risk_percent: float = 2.0
    expected_risk_reward: str = "1:2.5"
    max_drawdown_percent: float = 5.0
    
    # Strategy classification
    strategy_type: List[StrategyObjective] = field(default_factory=list)
    confidence_type: ConfidenceType = ConfidenceType.BACKTESTED
    holding_period: str = "15min-60min"
    
    # Performance tracking
    backtest: Dict[str, Any] = field(default_factory=dict)
    live_performance: Dict[str, Any] = field(default_factory=dict)
    
    # AI and evolution tags
    ai_tags: List[str] = field(default_factory=list)
    evolution: Optional[Dict[str, Any]] = None
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    last_tested: Optional[datetime] = None

@dataclass
class StrategySignal:
    """Enhanced strategy signal with metadata"""
    signal_id: str
    strategy_id: str
    symbol: str
    signal_type: str  # 'long', 'short', 'exit'
    action: str
    
    # Price and risk data
    entry_price: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    quantity: int = 0
    
    # Confidence and scoring
    confidence: float = 0.0
    ensemble_score: float = 0.0
    risk_score: float = 0.0
    
    # Context information
    market_regime: str = "unknown"
    volatility_regime: str = "unknown"
    time_window: TimeWindow = TimeWindow.MORNING_SESSION
    event_context: Optional[EventType] = None
    
    # Risk management
    capital_allocated: float = 0.0
    risk_amount: float = 0.0
    risk_reward_ratio: float = 0.0
    
    # Metadata
    timestamp: datetime = field(default_factory=datetime.now)
    strategy_metadata: Optional[StrategyMetadata] = None
    
    # Validation flags
    passed_risk_filter: bool = False
    passed_time_filter: bool = False
    passed_regime_filter: bool = False
    passed_confidence_filter: bool = False

# ═══════════════════════════════════════════════════════════════════════════════
# [PROD] ENHANCED STRATEGY GENERATION AGENT
# ═══════════════════════════════════════════════════════════════════════════════

class EnhancedStrategyGenerationAgent:
    """
    Enhanced Strategy Generation Agent with advanced features:
    - Regime-aware strategy tagging and filtering
    - Event-driven strategy activation
    - Time-of-day based routing
    - Inter-strategy ensemble scoring
    - Risk-aware filtering and performance tracking
    - Synthetic strategy creation and evolution
    - Multi-layer filtering pipeline
    """

    def __init__(self, config_path: str = "config/enhanced_strategies.yaml"):
        self.config_path = config_path
        self.config = {}
        self.strategies: Dict[str, StrategyMetadata] = {}
        self.strategy_performance: Dict[str, Dict] = defaultdict(dict)
        self.regime_detector = None
        self.volatility_detector = None
        self.event_detector = None
        self.ensemble_scorer = None
        self.synthetic_creator = None
        self.backtesting_agent = None

        # Performance tracking
        self.signal_history: deque = deque(maxlen=10000)
        self.strategy_stats: Dict[str, Dict] = defaultdict(lambda: {
            'total_signals': 0,
            'successful_signals': 0,
            'win_rate': 0.0,
            'avg_return': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'last_30_days': {'signals': 0, 'wins': 0, 'win_rate': 0.0}
        })

        # Time window mappings
        self.time_windows = {
            TimeWindow.PRE_MARKET: (time(9, 0), time(9, 15)),
            TimeWindow.OPENING_RANGE: (time(9, 15), time(10, 15)),
            TimeWindow.MORNING_SESSION: (time(10, 15), time(11, 30)),
            TimeWindow.MIDDAY_LULL: (time(11, 30), time(13, 30)),
            TimeWindow.AFTERNOON_SESSION: (time(13, 30), time(14, 30)),
            TimeWindow.POWER_HOUR: (time(14, 30), time(15, 15)),
            TimeWindow.CLOSING: (time(15, 15), time(15, 30))
        }

        # Event calendar
        self.event_calendar = {}
        self.market_holidays = set()

        # Regime and volatility state
        self.current_market_regime = MarketRegimeType.SIDEWAYS
        self.current_volatility_regime = VolatilityRegimeType.NORMAL_IV
        self.regime_confidence = 0.5

        # Multi-layer filtering pipeline
        self.filter_pipeline = [
            self._validate_strategy_trigger,
            self._apply_confidence_scoring,
            self._apply_risk_filtering,
            self._apply_time_filtering,
            self._apply_regime_filtering,
            self._apply_ensemble_scoring
        ]

        self.is_running = False
        logger.info("[INIT] Enhanced Strategy Generation Agent initialized")

    async def initialize(self) -> bool:
        """Initialize the agent with configuration and dependencies"""
        try:
            logger.info("[INIT] Initializing Enhanced Strategy Generation Agent...")

            # Load configuration
            await self._load_config()

            # Load existing strategies
            await self._load_strategies()

            # Initialize components
            await self._initialize_regime_detector()
            await self._initialize_volatility_detector()
            await self._initialize_event_detector()
            await self._initialize_ensemble_scorer()
            await self._initialize_synthetic_creator()

            # Initialize backtesting agent if available
            if EnhancedBacktestingAgent:
                self.backtesting_agent = EnhancedBacktestingAgent()
                await self.backtesting_agent.initialize()

            # Load performance history
            await self._load_performance_history()

            # Load event calendar
            await self._load_event_calendar()

            logger.info("[SUCCESS] Enhanced Strategy Generation Agent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False

    async def _load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as file:
                    self.config = yaml.safe_load(file)
            else:
                # Default configuration
                self.config = {
                    'general': {
                        'max_strategies_per_symbol': 10,
                        'min_confidence_threshold': 0.7,
                        'max_risk_per_strategy': 0.02,
                        'enable_synthetic_creation': True,
                        'enable_ensemble_scoring': True,
                        'enable_regime_filtering': True
                    },
                    'regime_detection': {
                        'lookback_periods': 20,
                        'volatility_threshold': 0.02,
                        'trend_threshold': 0.01,
                        'regime_confidence_threshold': 0.6
                    },
                    'time_filtering': {
                        'enable_time_windows': True,
                        'market_hours_only': True,
                        'exclude_first_last_minutes': 5
                    },
                    'risk_management': {
                        'max_drawdown_threshold': 0.1,
                        'min_sharpe_ratio': 1.0,
                        'performance_lookback_days': 30,
                        'auto_disable_poor_performers': True
                    },
                    'ensemble_scoring': {
                        'min_strategies_for_ensemble': 2,
                        'max_strategies_for_ensemble': 5,
                        'confidence_weight': 0.4,
                        'performance_weight': 0.6
                    }
                }

                # Save default config
                await self._save_config()

            logger.info("[CONFIG] Configuration loaded successfully")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load configuration: {e}")
            raise

    async def _save_config(self):
        """Save configuration to file"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(self.config, file, default_flow_style=False, indent=2)
            logger.info("[CONFIG] Configuration saved successfully")
        except Exception as e:
            logger.error(f"[ERROR] Failed to save configuration: {e}")

    async def _load_strategies(self):
        """Load strategies from configuration file"""
        try:
            strategies_path = "config/enhanced_strategies_metadata.yaml"

            if os.path.exists(strategies_path):
                with open(strategies_path, 'r', encoding='utf-8') as file:
                    strategies_data = yaml.safe_load(file)

                for strategy_data in strategies_data.get('strategies', []):
                    strategy = self._create_strategy_from_dict(strategy_data)
                    self.strategies[strategy.id] = strategy

                logger.info(f"[STRATEGIES] Loaded {len(self.strategies)} strategies")
            else:
                logger.info("[STRATEGIES] No existing strategies file found, will create default strategies")
                await self._create_default_strategies()

        except Exception as e:
            logger.error(f"[ERROR] Failed to load strategies: {e}")

    def _create_strategy_from_dict(self, data: Dict) -> StrategyMetadata:
        """Create StrategyMetadata object from dictionary"""
        try:
            # Convert string enums back to enum objects
            if 'market_regime' in data:
                data['market_regime'] = [MarketRegimeType(regime) for regime in data['market_regime']]
            if 'volatility_regime' in data:
                data['volatility_regime'] = [VolatilityRegimeType(regime) for regime in data['volatility_regime']]
            if 'event_types' in data:
                data['event_types'] = [EventType(event) for event in data['event_types']]
            if 'strategy_type' in data:
                data['strategy_type'] = [StrategyObjective(obj) for obj in data['strategy_type']]
            if 'confidence_type' in data:
                data['confidence_type'] = ConfidenceType(data['confidence_type'])

            # Handle datetime fields
            if 'created_at' in data and isinstance(data['created_at'], str):
                data['created_at'] = datetime.fromisoformat(data['created_at'])
            if 'last_updated' in data and isinstance(data['last_updated'], str):
                data['last_updated'] = datetime.fromisoformat(data['last_updated'])
            if 'last_tested' in data and isinstance(data['last_tested'], str):
                data['last_tested'] = datetime.fromisoformat(data['last_tested'])

            return StrategyMetadata(**data)

        except Exception as e:
            logger.error(f"[ERROR] Failed to create strategy from dict: {e}")
            raise

    async def _create_default_strategies(self):
        """Create default enhanced strategies"""
        try:
            default_strategies = [
                {
                    'id': 'strat_001',
                    'name': 'Morning Breakout IV Expansion',
                    'description': 'Captures early breakout with low IV and increasing momentum in BANKNIFTY',
                    'enabled': True,
                    'applicable_assets': ['BANKNIFTY'],
                    'timeframes': ['5min', '15min'],
                    'trigger_window': '09:15-10:15',
                    'expiry_types': ['weekly', 'monthly'],
                    'market_regime': ['bullish', 'volatile'],
                    'volatility_regime': ['low_IV'],
                    'signal_logic': {
                        'long_conditions': [
                            'close > high.shift(1)',
                            'volume > volume.rolling(20).mean() * 2',
                            'rsi_14 > 50',
                            'rsi_14 < 70'
                        ],
                        'short_conditions': [
                            'close < low.shift(1)',
                            'volume > volume.rolling(20).mean() * 2',
                            'rsi_14 < 50',
                            'rsi_14 > 30'
                        ]
                    },
                    'confidence_scoring': {
                        'type': 'ensemble',
                        'metrics': ['past_accuracy', 'sharpe_ratio', 'drawdown'],
                        'min_threshold': 0.7
                    },
                    'risk_model': {
                        'max_capital_risk_percent': 2,
                        'expected_risk_reward': '1:2.5',
                        'max_drawdown_percent': 5,
                        'stop_loss_trigger': '1.2 * atr_14'
                    },
                    'strategy_type': ['breakout', 'iv_expansion'],
                    'holding_period': '15min-60min',
                    'ai_tags': ['early_entry', 'favorable_rr', 'scalping', 'low_iv_entry']
                },
                {
                    'id': 'strat_002',
                    'name': 'VWAP Mean Reversion',
                    'description': 'Mean reversion strategy around VWAP with volume confirmation',
                    'enabled': True,
                    'applicable_assets': ['NIFTY', 'BANKNIFTY'],
                    'timeframes': ['5min', '15min', '30min'],
                    'trigger_window': '10:15-14:30',
                    'market_regime': ['sideways', 'range_bound'],
                    'volatility_regime': ['normal_IV', 'low_IV'],
                    'signal_logic': {
                        'long_conditions': [
                            'close < vwap * 0.998',
                            'rsi_14 < 35',
                            'volume > volume.rolling(20).mean() * 1.2',
                            'close > low.rolling(5).min()'
                        ],
                        'short_conditions': [
                            'close > vwap * 1.002',
                            'rsi_14 > 65',
                            'volume > volume.rolling(20).mean() * 1.2',
                            'close < high.rolling(5).max()'
                        ]
                    },
                    'confidence_scoring': {
                        'type': 'backtested',
                        'metrics': ['win_rate', 'profit_factor'],
                        'min_threshold': 0.65
                    },
                    'risk_model': {
                        'max_capital_risk_percent': 1.5,
                        'expected_risk_reward': '1:2',
                        'max_drawdown_percent': 3,
                        'stop_loss_trigger': '0.8 * atr_14'
                    },
                    'strategy_type': ['mean_reversion'],
                    'holding_period': '30min-120min',
                    'ai_tags': ['mean_reversion', 'vwap_based', 'volume_confirmation']
                },
                {
                    'id': 'strat_003',
                    'name': 'Gap Fill Strategy',
                    'description': 'Trades gap fills with volume and momentum confirmation',
                    'enabled': True,
                    'applicable_assets': ['NIFTY', 'BANKNIFTY'],
                    'timeframes': ['5min', '15min'],
                    'trigger_window': '09:15-11:00',
                    'event_types': ['gap_up', 'gap_down'],
                    'market_regime': ['bullish', 'bearish'],
                    'signal_logic': {
                        'long_conditions': [
                            'gap_down_detected',
                            'close > open',
                            'volume > volume.rolling(20).mean() * 1.5',
                            'rsi_14 > 40'
                        ],
                        'short_conditions': [
                            'gap_up_detected',
                            'close < open',
                            'volume > volume.rolling(20).mean() * 1.5',
                            'rsi_14 < 60'
                        ]
                    },
                    'confidence_scoring': {
                        'type': 'ai_scored',
                        'metrics': ['gap_size', 'volume_ratio', 'momentum'],
                        'min_threshold': 0.75
                    },
                    'risk_model': {
                        'max_capital_risk_percent': 2.5,
                        'expected_risk_reward': '1:1.5',
                        'max_drawdown_percent': 4,
                        'stop_loss_trigger': 'gap_size * 0.5'
                    },
                    'strategy_type': ['gap_fill'],
                    'holding_period': '15min-45min',
                    'ai_tags': ['gap_fill', 'event_driven', 'momentum']
                }
            ]

            for strategy_data in default_strategies:
                strategy = self._create_strategy_from_dict(strategy_data)
                self.strategies[strategy.id] = strategy

            # Save default strategies
            await self._save_strategies()

            logger.info(f"[STRATEGIES] Created {len(default_strategies)} default strategies")

        except Exception as e:
            logger.error(f"[ERROR] Failed to create default strategies: {e}")

    async def _save_strategies(self):
        """Save strategies to configuration file"""
        try:
            strategies_path = "config/enhanced_strategies_metadata.yaml"
            os.makedirs(os.path.dirname(strategies_path), exist_ok=True)

            strategies_data = {
                'strategies': []
            }

            for strategy in self.strategies.values():
                strategy_dict = asdict(strategy)

                # Convert enums to strings for YAML serialization
                if 'market_regime' in strategy_dict:
                    strategy_dict['market_regime'] = [regime.value for regime in strategy.market_regime]
                if 'volatility_regime' in strategy_dict:
                    strategy_dict['volatility_regime'] = [regime.value for regime in strategy.volatility_regime]
                if 'event_types' in strategy_dict:
                    strategy_dict['event_types'] = [event.value for event in strategy.event_types]
                if 'strategy_type' in strategy_dict:
                    strategy_dict['strategy_type'] = [obj.value for obj in strategy.strategy_type]
                if 'confidence_type' in strategy_dict:
                    strategy_dict['confidence_type'] = strategy.confidence_type.value

                # Convert datetime to ISO format
                if strategy_dict['created_at']:
                    strategy_dict['created_at'] = strategy.created_at.isoformat()
                if strategy_dict['last_updated']:
                    strategy_dict['last_updated'] = strategy.last_updated.isoformat()
                if strategy_dict['last_tested']:
                    strategy_dict['last_tested'] = strategy.last_tested.isoformat()

                strategies_data['strategies'].append(strategy_dict)

            with open(strategies_path, 'w', encoding='utf-8') as file:
                yaml.dump(strategies_data, file, default_flow_style=False, indent=2)

            logger.info(f"[STRATEGIES] Saved {len(self.strategies)} strategies")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save strategies: {e}")

    async def _initialize_regime_detector(self):
        """Initialize market regime detection"""
        try:
            self.regime_detector = MarketRegimeDetector(self.config.get('regime_detection', {}))
            logger.info("[REGIME] Market regime detector initialized")
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize regime detector: {e}")

    async def _initialize_volatility_detector(self):
        """Initialize volatility regime detection"""
        try:
            self.volatility_detector = VolatilityRegimeDetector(self.config.get('volatility_detection', {}))
            logger.info("[VOLATILITY] Volatility regime detector initialized")
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize volatility detector: {e}")

    async def _initialize_event_detector(self):
        """Initialize event detection"""
        try:
            self.event_detector = EventDetector(self.config.get('event_detection', {}))
            logger.info("[EVENT] Event detector initialized")
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize event detector: {e}")

    async def _initialize_ensemble_scorer(self):
        """Initialize ensemble scoring system"""
        try:
            self.ensemble_scorer = EnsembleScorer(self.config.get('ensemble_scoring', {}))
            logger.info("[ENSEMBLE] Ensemble scorer initialized")
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize ensemble scorer: {e}")

    async def _initialize_synthetic_creator(self):
        """Initialize synthetic strategy creator"""
        try:
            self.synthetic_creator = SyntheticStrategyCreator(self.config.get('synthetic_creation', {}))
            logger.info("[SYNTHETIC] Synthetic strategy creator initialized")
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize synthetic creator: {e}")

    async def start(self, **kwargs) -> bool:
        """Start the strategy generation agent"""
        try:
            logger.info("[START] Starting Enhanced Strategy Generation Agent...")
            self.is_running = True

            # Start background tasks
            await asyncio.gather(
                self._monitor_regime_changes(),
                self._update_strategy_performance(),
                self._create_synthetic_strategies(),
                self._cleanup_poor_performers()
            )

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False

    async def stop(self):
        """Stop the strategy generation agent"""
        try:
            logger.info("[STOP] Stopping Enhanced Strategy Generation Agent...")
            self.is_running = False

        except Exception as e:
            logger.error(f"[ERROR] Failed to stop agent: {e}")

    async def generate_signals(self, market_data: Dict, indicators: Dict,
                             symbol: str, timestamp: datetime) -> List[StrategySignal]:
        """
        Generate trading signals using multi-layer filtering pipeline

        Args:
            market_data: Current market data (OHLCV)
            indicators: Technical indicators
            symbol: Trading symbol
            timestamp: Current timestamp

        Returns:
            List of validated strategy signals
        """
        try:
            signals = []

            # Update current market context
            await self._update_market_context(market_data, indicators, symbol, timestamp)

            # Get applicable strategies for current context
            applicable_strategies = await self._get_applicable_strategies(symbol, timestamp)

            if not applicable_strategies:
                return signals

            # Generate raw signals from applicable strategies
            raw_signals = []
            for strategy in applicable_strategies:
                strategy_signals = await self._evaluate_strategy(strategy, market_data, indicators, symbol, timestamp)
                raw_signals.extend(strategy_signals)

            # Apply multi-layer filtering pipeline
            for signal in raw_signals:
                filtered_signal = await self._apply_filtering_pipeline(signal, market_data, indicators)
                if filtered_signal:
                    signals.append(filtered_signal)

            # Apply ensemble scoring if multiple signals
            if len(signals) > 1:
                signals = await self._apply_ensemble_scoring(signals)

            # Update signal history
            for signal in signals:
                self.signal_history.append({
                    'timestamp': timestamp,
                    'symbol': symbol,
                    'strategy_id': signal.strategy_id,
                    'signal_type': signal.signal_type,
                    'confidence': signal.confidence,
                    'ensemble_score': signal.ensemble_score
                })

            logger.info(f"[SIGNALS] Generated {len(signals)} signals for {symbol}")
            return signals

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate signals: {e}")
            return []

    async def _update_market_context(self, market_data: Dict, indicators: Dict,
                                   symbol: str, timestamp: datetime):
        """Update current market regime and context"""
        try:
            # Update market regime
            if self.regime_detector:
                self.current_market_regime, self.regime_confidence = await self.regime_detector.detect_regime(
                    market_data, indicators
                )

            # Update volatility regime
            if self.volatility_detector:
                self.current_volatility_regime = await self.volatility_detector.detect_volatility_regime(
                    market_data, indicators
                )

            # Check for events
            current_events = []
            if self.event_detector:
                current_events = await self.event_detector.detect_events(market_data, timestamp)

            logger.debug(f"[CONTEXT] Market: {self.current_market_regime.value}, "
                        f"Volatility: {self.current_volatility_regime.value}, "
                        f"Events: {[e.value for e in current_events]}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to update market context: {e}")

    async def _get_applicable_strategies(self, symbol: str, timestamp: datetime) -> List[StrategyMetadata]:
        """Get strategies applicable for current context"""
        try:
            applicable_strategies = []
            current_time = timestamp.time()
            current_window = self._get_current_time_window(current_time)

            for strategy in self.strategies.values():
                # Check if strategy is enabled
                if not strategy.enabled:
                    continue

                # Check asset applicability
                if strategy.applicable_assets and symbol not in strategy.applicable_assets:
                    continue

                # Check time window
                if strategy.trigger_window:
                    if not self._is_time_in_window(current_time, strategy.trigger_window):
                        continue

                # Check market regime compatibility
                if strategy.market_regime:
                    if self.current_market_regime not in strategy.market_regime:
                        continue

                # Check volatility regime compatibility
                if strategy.volatility_regime:
                    if self.current_volatility_regime not in strategy.volatility_regime:
                        continue

                # Check performance-based filtering
                if await self._should_suppress_strategy(strategy.id):
                    continue

                applicable_strategies.append(strategy)

            logger.debug(f"[APPLICABLE] Found {len(applicable_strategies)} applicable strategies for {symbol}")
            return applicable_strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to get applicable strategies: {e}")
            return []

    def _get_current_time_window(self, current_time: time) -> TimeWindow:
        """Determine current time window"""
        for window, (start_time, end_time) in self.time_windows.items():
            if start_time <= current_time <= end_time:
                return window
        return TimeWindow.MORNING_SESSION  # Default

    def _is_time_in_window(self, current_time: time, window_str: str) -> bool:
        """Check if current time is within specified window"""
        try:
            start_str, end_str = window_str.split('-')
            start_time = time(*map(int, start_str.split(':')))
            end_time = time(*map(int, end_str.split(':')))
            return start_time <= current_time <= end_time
        except:
            return True  # If parsing fails, allow the strategy

    async def _should_suppress_strategy(self, strategy_id: str) -> bool:
        """Check if strategy should be suppressed due to poor performance"""
        try:
            if not self.config.get('risk_management', {}).get('auto_disable_poor_performers', False):
                return False

            stats = self.strategy_stats.get(strategy_id, {})

            # Check recent performance
            recent_stats = stats.get('last_30_days', {})
            if recent_stats.get('signals', 0) >= 10:  # Minimum signals for evaluation
                win_rate = recent_stats.get('win_rate', 0)
                if win_rate < 0.3:  # Less than 30% win rate
                    logger.warning(f"[SUPPRESS] Suppressing strategy {strategy_id} due to poor win rate: {win_rate}")
                    return True

            # Check overall drawdown
            max_drawdown = stats.get('max_drawdown', 0)
            threshold = self.config.get('risk_management', {}).get('max_drawdown_threshold', 0.1)
            if max_drawdown > threshold:
                logger.warning(f"[SUPPRESS] Suppressing strategy {strategy_id} due to high drawdown: {max_drawdown}")
                return True

            return False

        except Exception as e:
            logger.error(f"[ERROR] Failed to check strategy suppression: {e}")
            return False

    async def _evaluate_strategy(self, strategy: StrategyMetadata, market_data: Dict,
                               indicators: Dict, symbol: str, timestamp: datetime) -> List[StrategySignal]:
        """Evaluate a single strategy and generate signals"""
        try:
            signals = []

            # Create DataFrame for evaluation
            df = self._create_evaluation_dataframe(market_data, indicators)

            if len(df) < 20:  # Need sufficient data
                return signals

            # Evaluate long conditions
            long_signal = await self._evaluate_signal_conditions(
                strategy, df, 'long_conditions', 'long', symbol, timestamp
            )
            if long_signal:
                signals.append(long_signal)

            # Evaluate short conditions
            short_signal = await self._evaluate_signal_conditions(
                strategy, df, 'short_conditions', 'short', symbol, timestamp
            )
            if short_signal:
                signals.append(short_signal)

            return signals

        except Exception as e:
            logger.error(f"[ERROR] Failed to evaluate strategy {strategy.id}: {e}")
            return []

    def _create_evaluation_dataframe(self, market_data: Dict, indicators: Dict) -> pl.DataFrame:
        """Create Polars DataFrame for strategy evaluation"""
        try:
            # Combine market data and indicators
            data = {**market_data, **indicators}

            # Ensure all data is the same length and convert to float
            min_length = min(len(v) for v in data.values() if isinstance(v, list))

            # Truncate all series to the same length and convert to float
            processed_data = {}
            for key, values in data.items():
                if isinstance(values, list):
                    # Convert to float and truncate to min_length
                    processed_data[key] = [float(v) for v in values[:min_length]]
                else:
                    # Single value, repeat for min_length
                    processed_data[key] = [float(values)] * min_length

            # Convert to Polars DataFrame with strict=False to handle mixed types
            df = pl.DataFrame(processed_data, strict=False)

            # Add derived columns if needed
            if 'open' in df.columns and 'close' in df.columns:
                df = df.with_columns([
                    (pl.col('close') > pl.col('open')).alias('green_candle'),
                    (pl.col('close') < pl.col('open')).alias('red_candle'),
                    (pl.col('high') - pl.col('low')).alias('candle_range')
                ])

            return df

        except Exception as e:
            logger.error(f"[ERROR] Failed to create evaluation DataFrame: {e}")
            return pl.DataFrame()

    async def _evaluate_signal_conditions(self, strategy: StrategyMetadata, df: pl.DataFrame,
                                        condition_type: str, signal_type: str,
                                        symbol: str, timestamp: datetime) -> Optional[StrategySignal]:
        """Evaluate signal conditions for a strategy"""
        try:
            conditions = strategy.signal_logic.get(condition_type, [])
            if not conditions:
                return None

            # Evaluate each condition
            condition_results = []
            for condition in conditions:
                try:
                    # Simple condition evaluation using basic comparisons
                    # This is a simplified version - in production, you'd want more sophisticated parsing
                    result = self._evaluate_simple_condition(condition, df)
                    condition_results.append(result)
                except Exception as e:
                    logger.debug(f"[CONDITION] Failed to evaluate condition '{condition}': {e}")
                    condition_results.append(False)

            # Check if all conditions are met
            if not all(condition_results):
                return None

            # Create signal
            signal = StrategySignal(
                signal_id=f"{strategy.id}_{symbol}_{signal_type}_{timestamp.strftime('%Y%m%d%H%M%S')}",
                strategy_id=strategy.id,
                symbol=symbol,
                signal_type=signal_type,
                action='buy' if signal_type == 'long' else 'sell',
                entry_price=df.select(pl.col('close')).to_series().to_list()[-1],
                timestamp=timestamp,
                strategy_metadata=strategy,
                market_regime=self.current_market_regime.value,
                volatility_regime=self.current_volatility_regime.value,
                time_window=self._get_current_time_window(timestamp.time())
            )

            # Calculate stop loss and take profit
            await self._calculate_risk_levels(signal, df, strategy)

            return signal

        except Exception as e:
            logger.error(f"[ERROR] Failed to evaluate signal conditions: {e}")
            return None

    def _evaluate_simple_condition(self, condition: str, df: pl.DataFrame) -> bool:
        """Evaluate a simple trading condition"""
        try:
            # Get the latest row of data
            if len(df) == 0:
                return False

            latest = df.tail(1).to_dicts()[0]

            # Simple condition evaluation
            # This is a basic implementation - in production you'd want more sophisticated parsing

            # Handle basic comparisons
            if 'close > high.shift(1)' in condition:
                if len(df) >= 2:
                    prev_high = df.tail(2).head(1).select('high').to_series().to_list()[0]
                    return latest['close'] > prev_high
                return False

            elif 'close < low.shift(1)' in condition:
                if len(df) >= 2:
                    prev_low = df.tail(2).head(1).select('low').to_series().to_list()[0]
                    return latest['close'] < prev_low
                return False

            elif 'volume > volume.rolling(20).mean()' in condition:
                if len(df) >= 20:
                    avg_volume = df.tail(20).select('volume').mean().to_series().to_list()[0]
                    multiplier = 2.0  # Default multiplier
                    if '* 2' in condition:
                        multiplier = 2.0
                    elif '* 1.5' in condition:
                        multiplier = 1.5
                    elif '* 1.2' in condition:
                        multiplier = 1.2
                    return latest['volume'] > avg_volume * multiplier
                return False

            elif 'rsi_14 >' in condition:
                if 'rsi_14' in latest:
                    threshold = 50  # Default
                    if '> 70' in condition:
                        threshold = 70
                    elif '> 60' in condition:
                        threshold = 60
                    elif '> 55' in condition:
                        threshold = 55
                    elif '> 50' in condition:
                        threshold = 50
                    elif '> 45' in condition:
                        threshold = 45
                    elif '> 40' in condition:
                        threshold = 40
                    return latest['rsi_14'] > threshold
                return False

            elif 'rsi_14 <' in condition:
                if 'rsi_14' in latest:
                    threshold = 50  # Default
                    if '< 30' in condition:
                        threshold = 30
                    elif '< 35' in condition:
                        threshold = 35
                    elif '< 45' in condition:
                        threshold = 45
                    elif '< 50' in condition:
                        threshold = 50
                    elif '< 55' in condition:
                        threshold = 55
                    elif '< 60' in condition:
                        threshold = 60
                    elif '< 65' in condition:
                        threshold = 65
                    elif '< 70' in condition:
                        threshold = 70
                    return latest['rsi_14'] < threshold
                return False

            elif 'close > vwap' in condition:
                if 'vwap' in latest:
                    multiplier = 1.0
                    if '* 1.002' in condition:
                        multiplier = 1.002
                    elif '* 0.998' in condition:
                        multiplier = 0.998
                    return latest['close'] > latest['vwap'] * multiplier
                return False

            elif 'close < vwap' in condition:
                if 'vwap' in latest:
                    multiplier = 1.0
                    if '* 1.002' in condition:
                        multiplier = 1.002
                    elif '* 0.998' in condition:
                        multiplier = 0.998
                    return latest['close'] < latest['vwap'] * multiplier
                return False

            elif 'close > ema_20' in condition:
                if 'ema_20' in latest:
                    return latest['close'] > latest['ema_20']
                return False

            elif 'close < ema_20' in condition:
                if 'ema_20' in latest:
                    return latest['close'] < latest['ema_20']
                return False

            elif 'ema_5 > ema_20' in condition:
                if 'ema_5' in latest and 'ema_20' in latest:
                    return latest['ema_5'] > latest['ema_20']
                return False

            elif 'ema_5 < ema_20' in condition:
                if 'ema_5' in latest and 'ema_20' in latest:
                    return latest['ema_5'] < latest['ema_20']
                return False

            elif 'macd > macd_signal' in condition:
                if 'macd' in latest and 'macd_signal' in latest:
                    return latest['macd'] > latest['macd_signal']
                return False

            elif 'macd < macd_signal' in condition:
                if 'macd' in latest and 'macd_signal' in latest:
                    return latest['macd'] < latest['macd_signal']
                return False

            # Default case - try to evaluate as simple comparison
            return True  # Placeholder for unknown conditions

        except Exception as e:
            logger.debug(f"[CONDITION] Error evaluating condition '{condition}': {e}")
            return False

    async def _calculate_risk_levels(self, signal: StrategySignal, df: pl.DataFrame, strategy: StrategyMetadata):
        """Calculate stop loss and take profit levels"""
        try:
            entry_price = signal.entry_price

            # Get ATR for dynamic stop loss
            atr_values = df.select(pl.col('atr_14')).to_series().to_list() if 'atr_14' in df.columns else [entry_price * 0.01]
            atr = atr_values[-1] if atr_values else entry_price * 0.01

            # Parse risk-reward ratio
            rr_parts = strategy.expected_risk_reward.split(':')
            risk_ratio = float(rr_parts[0]) if len(rr_parts) > 0 else 1.0
            reward_ratio = float(rr_parts[1]) if len(rr_parts) > 1 else 2.0

            # Calculate stop loss based on strategy configuration
            stop_loss_trigger = strategy.risk_model.get('stop_loss_trigger', '1.0 * atr_14')
            if 'atr' in stop_loss_trigger.lower():
                multiplier = float(stop_loss_trigger.split('*')[0].strip()) if '*' in stop_loss_trigger else 1.0
                stop_distance = atr * multiplier
            else:
                stop_distance = entry_price * 0.01  # Default 1%

            if signal.signal_type == 'long':
                signal.stop_loss = entry_price - stop_distance
                signal.take_profit = entry_price + (stop_distance * reward_ratio / risk_ratio)
            else:
                signal.stop_loss = entry_price + stop_distance
                signal.take_profit = entry_price - (stop_distance * reward_ratio / risk_ratio)

            # Calculate risk-reward ratio
            if signal.stop_loss and signal.take_profit:
                risk = abs(entry_price - signal.stop_loss)
                reward = abs(signal.take_profit - entry_price)
                signal.risk_reward_ratio = reward / risk if risk > 0 else 0

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate risk levels: {e}")

    async def _apply_filtering_pipeline(self, signal: StrategySignal,
                                      market_data: Dict, indicators: Dict) -> Optional[StrategySignal]:
        """Apply multi-layer filtering pipeline to signal"""
        try:
            # Apply each filter in the pipeline
            for filter_func in self.filter_pipeline:
                if not await filter_func(signal, market_data, indicators):
                    return None

            return signal

        except Exception as e:
            logger.error(f"[ERROR] Failed to apply filtering pipeline: {e}")
            return None

    async def _validate_strategy_trigger(self, signal: StrategySignal,
                                       market_data: Dict, indicators: Dict) -> bool:
        """Validate strategy trigger conditions"""
        try:
            # Basic validation
            if not signal.strategy_metadata:
                return False

            if signal.entry_price <= 0:
                return False

            if not signal.stop_loss or not signal.take_profit:
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to validate strategy trigger: {e}")
            return False

    async def _apply_confidence_scoring(self, signal: StrategySignal,
                                      market_data: Dict, indicators: Dict) -> bool:
        """Apply confidence scoring to signal"""
        try:
            strategy = signal.strategy_metadata
            if not strategy:
                return False

            # Calculate base confidence from strategy performance
            stats = self.strategy_stats.get(strategy.id, {})
            base_confidence = stats.get('win_rate', 0.5)

            # Adjust confidence based on market regime match
            regime_bonus = 0.1 if self.current_market_regime in strategy.market_regime else -0.1

            # Adjust confidence based on volatility regime match
            vol_bonus = 0.1 if self.current_volatility_regime in strategy.volatility_regime else -0.1

            # Calculate final confidence
            signal.confidence = min(1.0, max(0.0, base_confidence + regime_bonus + vol_bonus))

            # Check minimum threshold
            min_threshold = strategy.confidence_scoring.get('min_threshold', strategy.min_confidence_threshold)
            signal.passed_confidence_filter = signal.confidence >= min_threshold

            return signal.passed_confidence_filter

        except Exception as e:
            logger.error(f"[ERROR] Failed to apply confidence scoring: {e}")
            return False

    async def _apply_risk_filtering(self, signal: StrategySignal,
                                  market_data: Dict, indicators: Dict) -> bool:
        """Apply risk-based filtering to signal"""
        try:
            strategy = signal.strategy_metadata
            if not strategy:
                return False

            # Check risk-reward ratio
            min_rr = 1.5  # Minimum risk-reward ratio
            if signal.risk_reward_ratio < min_rr:
                return False

            # Check maximum capital risk
            max_risk_percent = strategy.max_capital_risk_percent / 100
            if signal.risk_amount > 0:
                risk_ratio = signal.risk_amount / signal.capital_allocated if signal.capital_allocated > 0 else 0
                if risk_ratio > max_risk_percent:
                    return False

            # Check strategy's recent performance
            stats = self.strategy_stats.get(strategy.id, {})
            if stats.get('max_drawdown', 0) > strategy.max_drawdown_percent / 100:
                return False

            signal.passed_risk_filter = True
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to apply risk filtering: {e}")
            return False

    async def _apply_time_filtering(self, signal: StrategySignal,
                                  market_data: Dict, indicators: Dict) -> bool:
        """Apply time-based filtering to signal"""
        try:
            current_time = signal.timestamp.time()

            # Check if within market hours
            market_start = time(9, 15)
            market_end = time(15, 15)

            if not (market_start <= current_time <= market_end):
                return False

            # Check strategy-specific time window
            strategy = signal.strategy_metadata
            if strategy and strategy.trigger_window:
                if not self._is_time_in_window(current_time, strategy.trigger_window):
                    return False

            signal.passed_time_filter = True
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to apply time filtering: {e}")
            return False

    async def _apply_regime_filtering(self, signal: StrategySignal,
                                    market_data: Dict, indicators: Dict) -> bool:
        """Apply market regime filtering to signal"""
        try:
            strategy = signal.strategy_metadata
            if not strategy:
                return False

            # Check market regime compatibility
            if strategy.market_regime:
                if self.current_market_regime not in strategy.market_regime:
                    return False

            # Check volatility regime compatibility
            if strategy.volatility_regime:
                if self.current_volatility_regime not in strategy.volatility_regime:
                    return False

            # Check regime confidence
            if self.regime_confidence < 0.6:  # Low confidence in regime detection
                return False

            signal.passed_regime_filter = True
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to apply regime filtering: {e}")
            return False

    async def _apply_ensemble_scoring(self, signals: List[StrategySignal]) -> List[StrategySignal]:
        """Apply ensemble scoring to multiple signals"""
        try:
            if len(signals) <= 1:
                return signals

            if not self.ensemble_scorer:
                return signals

            # Group signals by type (long/short)
            long_signals = [s for s in signals if s.signal_type == 'long']
            short_signals = [s for s in signals if s.signal_type == 'short']

            # Apply ensemble scoring to each group
            scored_signals = []

            if long_signals:
                ensemble_long = await self.ensemble_scorer.score_signals(long_signals)
                scored_signals.extend(ensemble_long)

            if short_signals:
                ensemble_short = await self.ensemble_scorer.score_signals(short_signals)
                scored_signals.extend(ensemble_short)

            return scored_signals

        except Exception as e:
            logger.error(f"[ERROR] Failed to apply ensemble scoring: {e}")
            return signals

    # Background monitoring tasks
    async def _monitor_regime_changes(self):
        """Monitor and react to regime changes"""
        while self.is_running:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                # Regime monitoring logic would go here

            except Exception as e:
                logger.error(f"[ERROR] Regime monitoring failed: {e}")

    async def _update_strategy_performance(self):
        """Update strategy performance metrics"""
        while self.is_running:
            try:
                await asyncio.sleep(3600)  # Update every hour
                # Performance update logic would go here

            except Exception as e:
                logger.error(f"[ERROR] Performance update failed: {e}")

    async def _create_synthetic_strategies(self):
        """Create new synthetic strategies"""
        while self.is_running:
            try:
                await asyncio.sleep(86400)  # Check daily
                # Synthetic strategy creation logic would go here

            except Exception as e:
                logger.error(f"[ERROR] Synthetic strategy creation failed: {e}")

    async def _cleanup_poor_performers(self):
        """Cleanup poorly performing strategies"""
        while self.is_running:
            try:
                await asyncio.sleep(86400)  # Check daily
                # Cleanup logic would go here

            except Exception as e:
                logger.error(f"[ERROR] Cleanup failed: {e}")

    async def _load_performance_history(self):
        """Load historical performance data"""
        try:
            # Load performance history from file or database
            pass
        except Exception as e:
            logger.error(f"[ERROR] Failed to load performance history: {e}")

    async def _load_event_calendar(self):
        """Load event calendar for event-driven strategies"""
        try:
            # Load event calendar from file or API
            pass
        except Exception as e:
            logger.error(f"[ERROR] Failed to load event calendar: {e}")

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] HELPER CLASSES
# ═══════════════════════════════════════════════════════════════════════════════

class MarketRegimeDetector:
    """Market regime detection using technical indicators"""

    def __init__(self, config: Dict):
        self.config = config
        self.lookback_periods = config.get('lookback_periods', 20)
        self.volatility_threshold = config.get('volatility_threshold', 0.02)
        self.trend_threshold = config.get('trend_threshold', 0.01)

    async def detect_regime(self, market_data: Dict, indicators: Dict) -> Tuple[MarketRegimeType, float]:
        """Detect current market regime"""
        try:
            # Simple regime detection based on price action and volatility
            # This would be more sophisticated in a real implementation

            if 'close' in market_data and len(market_data['close']) >= self.lookback_periods:
                closes = market_data['close'][-self.lookback_periods:]

                # Calculate trend
                trend = (closes[-1] - closes[0]) / closes[0]

                # Calculate volatility
                returns = [(closes[i] - closes[i-1]) / closes[i-1] for i in range(1, len(closes))]
                volatility = np.std(returns) if returns else 0

                # Determine regime
                if volatility > self.volatility_threshold:
                    regime = MarketRegimeType.VOLATILE
                elif trend > self.trend_threshold:
                    regime = MarketRegimeType.BULLISH
                elif trend < -self.trend_threshold:
                    regime = MarketRegimeType.BEARISH
                else:
                    regime = MarketRegimeType.SIDEWAYS

                confidence = min(1.0, abs(trend) / self.trend_threshold + volatility / self.volatility_threshold)
                return regime, confidence

            return MarketRegimeType.SIDEWAYS, 0.5

        except Exception as e:
            logger.error(f"[ERROR] Failed to detect market regime: {e}")
            return MarketRegimeType.SIDEWAYS, 0.5

class VolatilityRegimeDetector:
    """Volatility regime detection"""

    def __init__(self, config: Dict):
        self.config = config

    async def detect_volatility_regime(self, market_data: Dict, indicators: Dict) -> VolatilityRegimeType:
        """Detect current volatility regime"""
        try:
            # Simple volatility regime detection
            # This would use IV data in a real implementation

            if 'atr_14' in indicators:
                atr_values = indicators['atr_14']
                if len(atr_values) >= 20:
                    current_atr = atr_values[-1]
                    avg_atr = np.mean(atr_values[-20:])

                    if current_atr > avg_atr * 1.5:
                        return VolatilityRegimeType.HIGH_IV
                    elif current_atr < avg_atr * 0.7:
                        return VolatilityRegimeType.LOW_IV
                    else:
                        return VolatilityRegimeType.NORMAL_IV

            return VolatilityRegimeType.NORMAL_IV

        except Exception as e:
            logger.error(f"[ERROR] Failed to detect volatility regime: {e}")
            return VolatilityRegimeType.NORMAL_IV

class EventDetector:
    """Event detection for event-driven strategies"""

    def __init__(self, config: Dict):
        self.config = config

    async def detect_events(self, market_data: Dict, timestamp: datetime) -> List[EventType]:
        """Detect current market events"""
        try:
            events = []

            # Gap detection
            if 'open' in market_data and 'close' in market_data:
                opens = market_data['open']
                closes = market_data['close']

                if len(opens) >= 2 and len(closes) >= 2:
                    gap = (opens[-1] - closes[-2]) / closes[-2]

                    if gap > 0.01:  # 1% gap up
                        events.append(EventType.GAP_UP)
                    elif gap < -0.01:  # 1% gap down
                        events.append(EventType.GAP_DOWN)

            # Weekly expiry detection (Thursdays for NIFTY/BANKNIFTY)
            if timestamp.weekday() == 3:  # Thursday
                events.append(EventType.WEEKLY_EXPIRY)

            # Monthly expiry detection (last Thursday of month)
            if self._is_monthly_expiry(timestamp):
                events.append(EventType.MONTHLY_EXPIRY)

            return events

        except Exception as e:
            logger.error(f"[ERROR] Failed to detect events: {e}")
            return []

    def _is_monthly_expiry(self, timestamp: datetime) -> bool:
        """Check if date is monthly expiry (last Thursday of month)"""
        try:
            # Get last day of month
            if timestamp.month == 12:
                next_month = timestamp.replace(year=timestamp.year + 1, month=1, day=1)
            else:
                next_month = timestamp.replace(month=timestamp.month + 1, day=1)

            last_day = next_month - timedelta(days=1)

            # Find last Thursday
            days_back = (last_day.weekday() - 3) % 7
            last_thursday = last_day - timedelta(days=days_back)

            return timestamp.date() == last_thursday.date()

        except Exception as e:
            logger.error(f"[ERROR] Failed to check monthly expiry: {e}")
            return False

class EnsembleScorer:
    """Ensemble scoring for multiple strategy signals"""

    def __init__(self, config: Dict):
        self.config = config
        self.min_strategies = config.get('min_strategies_for_ensemble', 2)
        self.max_strategies = config.get('max_strategies_for_ensemble', 5)
        self.confidence_weight = config.get('confidence_weight', 0.4)
        self.performance_weight = config.get('performance_weight', 0.6)

    async def score_signals(self, signals: List[StrategySignal]) -> List[StrategySignal]:
        """Apply ensemble scoring to signals"""
        try:
            if len(signals) < self.min_strategies:
                return signals

            # Sort signals by confidence
            signals.sort(key=lambda x: x.confidence, reverse=True)

            # Take top signals
            top_signals = signals[:self.max_strategies]

            # Calculate ensemble score
            total_confidence = sum(s.confidence for s in top_signals)

            for signal in top_signals:
                # Weighted ensemble score
                confidence_score = signal.confidence * self.confidence_weight
                performance_score = 0.5 * self.performance_weight  # Placeholder

                signal.ensemble_score = confidence_score + performance_score

            # Return signals above threshold
            threshold = 0.7
            return [s for s in top_signals if s.ensemble_score >= threshold]

        except Exception as e:
            logger.error(f"[ERROR] Failed to score signals: {e}")
            return signals

class SyntheticStrategyCreator:
    """Create synthetic strategies by combining existing strategies"""

    def __init__(self, config: Dict):
        self.config = config

    async def create_synthetic_strategy(self, base_strategies: List[StrategyMetadata]) -> Optional[StrategyMetadata]:
        """Create a new synthetic strategy"""
        try:
            if len(base_strategies) < 2:
                return None

            # Combine strategy elements
            new_strategy = StrategyMetadata(
                id=f"synthetic_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                name=f"Synthetic_{'+'.join([s.name[:10] for s in base_strategies[:2]])}",
                description=f"Synthetic strategy combining {len(base_strategies)} base strategies",
                applicable_assets=list(set().union(*[s.applicable_assets for s in base_strategies])),
                timeframes=list(set().union(*[s.timeframes for s in base_strategies])),
                market_regime=list(set().union(*[s.market_regime for s in base_strategies])),
                volatility_regime=list(set().union(*[s.volatility_regime for s in base_strategies])),
                strategy_type=[StrategyObjective.ENSEMBLE],
                confidence_type=ConfidenceType.EXPERIMENTAL,
                ai_tags=['synthetic', 'experimental', 'ensemble']
            )

            # Combine signal logic (simplified)
            combined_logic = {
                'long_conditions': [],
                'short_conditions': []
            }

            for strategy in base_strategies:
                if 'long_conditions' in strategy.signal_logic:
                    combined_logic['long_conditions'].extend(strategy.signal_logic['long_conditions'])
                if 'short_conditions' in strategy.signal_logic:
                    combined_logic['short_conditions'].extend(strategy.signal_logic['short_conditions'])

            new_strategy.signal_logic = combined_logic

            return new_strategy

        except Exception as e:
            logger.error(f"[ERROR] Failed to create synthetic strategy: {e}")
            return None

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main execution function for testing"""
    try:
        # Initialize agent
        agent = EnhancedStrategyGenerationAgent()

        if await agent.initialize():
            logger.info("[MAIN] Agent initialized successfully")

            # Test signal generation
            test_market_data = {
                'open': [100, 101, 102],
                'high': [102, 103, 104],
                'low': [99, 100, 101],
                'close': [101, 102, 103],
                'volume': [1000, 1100, 1200]
            }

            test_indicators = {
                'rsi_14': [45, 50, 55],
                'ema_20': [100, 101, 102],
                'vwap': [100.5, 101.5, 102.5],
                'atr_14': [1.0, 1.1, 1.2]
            }

            signals = await agent.generate_signals(
                test_market_data,
                test_indicators,
                'BANKNIFTY',
                datetime.now()
            )

            logger.info(f"[MAIN] Generated {len(signals)} signals")

            for signal in signals:
                logger.info(f"[SIGNAL] {signal.strategy_id}: {signal.signal_type} at {signal.entry_price}")

        else:
            logger.error("[MAIN] Failed to initialize agent")

    except Exception as e:
        logger.error(f"[MAIN] Error in main execution: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
