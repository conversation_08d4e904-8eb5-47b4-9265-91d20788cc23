#!/usr/bin/env python3
"""
Test the Updated Robust WebSocket Manager
Verify that the timeout issue is fixed and subscriptions work properly
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Local imports
from utils.robust_websocket_manager import RobustWebSocketManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RobustWebSocketTest:
    """Test the updated robust WebSocket manager"""
    
    def __init__(self):
        self.data_received_count = 0
        self.start_time = None
        self.test_symbols = ['RELIANCE', 'TCS', 'INFY', 'HDFCBANK', 'ICICIBANK']
        
        # Configuration for robust WebSocket
        self.config = {
            'max_retry_attempts': 3,
            'retry_delay_base': 2,
            'connection_timeout': 20,
            'heartbeat_interval': 30
        }
        
        self.websocket_manager = RobustWebSocketManager(self.config)
        
        logger.info("[INIT] Robust WebSocket Test initialized")
    
    def on_connected(self):
        """Handle WebSocket connection established"""
        logger.info("[CALLBACK] WebSocket connected successfully!")
    
    def on_data(self, message):
        """Handle incoming WebSocket data"""
        try:
            self.data_received_count += 1
            
            # Extract basic info from message
            token = str(message.get('token', 'Unknown'))
            ltp = message.get('last_traded_price', 0) / 100
            exchange = message.get('exchange_type', 'Unknown')
            
            # Get symbol info if available
            symbol_info = self.websocket_manager.instrument_master.get_symbol(token) if self.websocket_manager.instrument_master else None
            symbol_name = symbol_info['symbol'] if symbol_info else f"Token_{token}"
            
            logger.info(f"[DATA] {symbol_name}: ₹{ltp:.2f} | Exchange: {exchange} | Count: {self.data_received_count}")
            
            # Print summary every 10 messages
            if self.data_received_count % 10 == 0:
                elapsed = asyncio.get_event_loop().time() - self.start_time if self.start_time else 0
                rate = self.data_received_count / elapsed if elapsed > 0 else 0
                logger.info(f"[STATS] Received {self.data_received_count} messages in {elapsed:.1f}s (Rate: {rate:.1f} msg/s)")
                
        except Exception as e:
            logger.error(f"[ERROR] Data processing error: {e}")
    
    def on_error(self, error):
        """Handle WebSocket errors"""
        logger.error(f"[ERROR] WebSocket error: {error}")
    
    def on_disconnected(self):
        """Handle WebSocket disconnection"""
        logger.warning("[CALLBACK] WebSocket disconnected")
    
    async def run_test(self, duration_seconds: int = 30) -> bool:
        """Run the robust WebSocket test"""
        try:
            print("\n" + "=" * 80)
            print("🔧 ROBUST WEBSOCKET MANAGER TEST")
            print("=" * 80)
            
            # Set up callbacks
            self.websocket_manager.set_callbacks(
                on_connected=self.on_connected,
                on_data=self.on_data,
                on_error=self.on_error,
                on_disconnected=self.on_disconnected
            )
            
            # Step 1: Connect
            print("\n🔄 Step 1: Connecting to WebSocket...")
            self.start_time = asyncio.get_event_loop().time()
            
            connection_success = await self.websocket_manager.connect()
            
            if not connection_success:
                print("❌ Connection failed")
                return False
            
            print("✅ Connection successful")
            
            # Step 2: Subscribe to symbols
            print(f"\n📊 Step 2: Subscribing to test symbols: {self.test_symbols}")
            
            subscription_success = await self.websocket_manager.subscribe_symbols(self.test_symbols)
            
            if not subscription_success:
                print("❌ Subscription failed")
                return False
            
            print("✅ Subscription successful")
            
            # Step 3: Monitor for data
            print(f"\n⏳ Step 3: Monitoring for {duration_seconds} seconds...")
            print("   (You should see real-time market data below)")
            print("-" * 80)
            
            # Wait for the specified duration
            await asyncio.sleep(duration_seconds)
            
            # Step 4: Results
            print("\n" + "=" * 80)
            print("📊 ROBUST WEBSOCKET TEST RESULTS")
            print("=" * 80)
            
            elapsed = asyncio.get_event_loop().time() - self.start_time if self.start_time else 0
            rate = self.data_received_count / elapsed if elapsed > 0 else 0
            
            # Get connection status
            status = self.websocket_manager.get_connection_status()
            
            print(f"✅ Test Duration: {elapsed:.1f} seconds")
            print(f"📈 Messages Received: {self.data_received_count}")
            print(f"⚡ Average Rate: {rate:.1f} messages/second")
            print(f"🔗 Connection State: {status['state']}")
            print(f"📊 Connection Attempts: {status['metrics']['connection_attempts']}")
            print(f"✅ Successful Connections: {status['metrics']['successful_connections']}")
            
            if self.data_received_count > 0:
                print("\n🎉 SUCCESS: Robust WebSocket Manager is working properly!")
                print("   ✅ Connection timeout issue is FIXED")
                print("   ✅ Token-based subscriptions are working")
                print("   ✅ Real-time data is being received")
                return True
            else:
                print("\n⚠️ WARNING: No data received")
                print("   Connection and subscription worked, but no market data received.")
                print("   This might be normal if market is closed.")
                return False
                
        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            logger.error(f"Test error: {e}", exc_info=True)
            return False
        finally:
            # Cleanup
            try:
                await self.websocket_manager.disconnect()
                logger.info("[CLEANUP] WebSocket disconnected")
            except:
                pass

async def main():
    """Main test execution"""
    try:
        print("🔧 Robust WebSocket Manager Test")
        print("This test verifies that the timeout issue is fixed and subscriptions work properly.")
        print("\nPress Ctrl+C to stop at any time...")
        
        # Create and run test
        test = RobustWebSocketTest()
        success = await test.run_test(duration_seconds=30)  # 30 second test
        
        if success:
            print("\n✅ Robust WebSocket Manager test completed successfully!")
            print("   The timeout issue has been FIXED! 🎉")
        else:
            print("\n⚠️ Robust WebSocket Manager test completed with issues.")
            print("   Check the logs above for details.")
            
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
