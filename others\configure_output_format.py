#!/usr/bin/env python3
"""
Configure Output Format for Backtesting Scripts
Easily switch between CSV, Parquet, and Feather formats
"""

import os
import sys
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_backtesting_format(script_path, output_format, compression=None):
    """Update the output format in a backtesting script"""
    
    if not os.path.exists(script_path):
        logger.error(f"Script not found: {script_path}")
        return False
    
    # Read the current script
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Determine file extension and compression
    if output_format.lower() == 'parquet':
        file_ext = '.parquet'
        default_compression = compression or 'brotli'
    elif output_format.lower() == 'feather':
        file_ext = '.feather'
        default_compression = compression or 'zstd'
    else:  # csv
        file_ext = '.csv'
        default_compression = compression or 'none'
    
    # Update OUTPUT_SUMMARY path
    lines = content.split('\n')
    updated_lines = []
    
    for line in lines:
        if line.strip().startswith('OUTPUT_SUMMARY = '):
            # Extract the path and change extension
            parts = line.split('=', 1)
            if len(parts) == 2:
                path_part = parts[1].strip().strip('"\'')
                # Change extension
                base_path = os.path.splitext(path_part)[0]
                new_path = base_path + file_ext
                updated_line = f'OUTPUT_SUMMARY = "{new_path}"'
                updated_lines.append(updated_line)
                logger.info(f"Updated OUTPUT_SUMMARY to: {new_path}")
            else:
                updated_lines.append(line)
        elif line.strip().startswith('OUTPUT_FORMAT = '):
            updated_line = f'OUTPUT_FORMAT = "{output_format.lower()}"  # Options: "parquet", "csv", "feather"'
            updated_lines.append(updated_line)
            logger.info(f"Updated OUTPUT_FORMAT to: {output_format.lower()}")
        elif line.strip().startswith('COMPRESSION = '):
            updated_line = f'COMPRESSION = "{default_compression}"     # Compression algorithm'
            updated_lines.append(updated_line)
            logger.info(f"Updated COMPRESSION to: {default_compression}")
        else:
            updated_lines.append(line)
    
    # If OUTPUT_FORMAT doesn't exist, add it after OUTPUT_SUMMARY
    if 'OUTPUT_FORMAT = ' not in content:
        for i, line in enumerate(updated_lines):
            if line.strip().startswith('OUTPUT_SUMMARY = '):
                updated_lines.insert(i + 1, f'OUTPUT_FORMAT = "{output_format.lower()}"  # Options: "parquet", "csv", "feather"')
                updated_lines.insert(i + 2, f'COMPRESSION = "{default_compression}"     # Compression algorithm')
                logger.info(f"Added OUTPUT_FORMAT and COMPRESSION configuration")
                break
    
    # Write back the updated content
    updated_content = '\n'.join(updated_lines)
    
    # Backup original file
    backup_path = f"{script_path}.backup"
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    logger.info(f"Backup created: {backup_path}")
    
    # Write updated file
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    logger.info(f"✅ Successfully updated {script_path} to use {output_format.upper()} format")
    return True

def show_current_configuration(script_path):
    """Show current output configuration"""
    if not os.path.exists(script_path):
        logger.error(f"Script not found: {script_path}")
        return
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    logger.info(f"📋 Current configuration for {script_path}:")
    
    for line in content.split('\n'):
        if line.strip().startswith('OUTPUT_SUMMARY = '):
            logger.info(f"   Output file: {line.split('=', 1)[1].strip().strip('\"\'')}")
        elif line.strip().startswith('OUTPUT_FORMAT = '):
            logger.info(f"   Format: {line.split('=', 1)[1].strip().strip('\"\'').split()[0]}")
        elif line.strip().startswith('COMPRESSION = '):
            logger.info(f"   Compression: {line.split('=', 1)[1].strip().strip('\"\'').split()[0]}")

def get_format_recommendations():
    """Display format recommendations"""
    logger.info("\n🎯 FORMAT RECOMMENDATIONS:")
    logger.info("=" * 50)
    logger.info("📦 PARQUET (Recommended for storage):")
    logger.info("   ✅ 70-80% space savings")
    logger.info("   ✅ 5-10x faster reads")
    logger.info("   ✅ Excellent compression")
    logger.info("   ✅ GPU compatible")
    logger.info("   📊 Best for: Final results, archival storage")
    
    logger.info("\n⚡ FEATHER (Recommended for frequent access):")
    logger.info("   ✅ Fastest I/O operations")
    logger.info("   ✅ 50-60% space savings")
    logger.info("   ✅ Excellent for temporary files")
    logger.info("   📊 Best for: Working data, intermediate results")
    
    logger.info("\n📄 CSV (Fallback/compatibility):")
    logger.info("   ✅ Universal compatibility")
    logger.info("   ✅ Human readable")
    logger.info("   ❌ Large file sizes")
    logger.info("   ❌ Slower I/O")
    logger.info("   📊 Best for: Small datasets, debugging")

def main():
    parser = argparse.ArgumentParser(description='Configure output format for backtesting scripts')
    parser.add_argument('--script', default='backtesting2.py', 
                       help='Backtesting script to configure (default: backtesting2.py)')
    parser.add_argument('--format', choices=['parquet', 'feather', 'csv'], 
                       help='Output format to use')
    parser.add_argument('--compression', 
                       help='Compression algorithm (auto-selected if not specified)')
    parser.add_argument('--show', action='store_true', 
                       help='Show current configuration')
    parser.add_argument('--recommend', action='store_true', 
                       help='Show format recommendations')
    
    args = parser.parse_args()
    
    if args.recommend:
        get_format_recommendations()
        return
    
    if args.show:
        show_current_configuration(args.script)
        return
    
    if not args.format:
        logger.error("Please specify --format or use --show/--recommend")
        parser.print_help()
        return
    
    # Update the script
    success = update_backtesting_format(args.script, args.format, args.compression)
    
    if success:
        logger.info(f"\n✅ Configuration updated successfully!")
        logger.info(f"📁 Output will now be saved in {args.format.upper()} format")
        
        # Show expected benefits
        if args.format.lower() == 'parquet':
            logger.info("🎉 Expected benefits:")
            logger.info("   💾 70-80% smaller file sizes")
            logger.info("   ⚡ 5-10x faster data loading")
            logger.info("   🚀 Better GPU compatibility")
        elif args.format.lower() == 'feather':
            logger.info("🎉 Expected benefits:")
            logger.info("   ⚡ Fastest I/O operations")
            logger.info("   💾 50-60% smaller file sizes")
            logger.info("   🔄 Excellent for frequent access")
        
        logger.info(f"\n🚀 Run your backtesting script: python {args.script}")
    else:
        logger.error("❌ Configuration update failed")

if __name__ == "__main__":
    main()
