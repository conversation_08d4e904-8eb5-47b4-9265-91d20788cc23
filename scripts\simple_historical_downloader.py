#!/usr/bin/env python3
"""
Simple Historical Data Downloader for SmartAPI
Downloads 5-minute historical data with manual date input
"""

import os
import sys
import asyncio
import logging
import json
import polars as pl
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# SmartAPI integration
try:
    from SmartApi import SmartConnect
except ImportError:
    print("ERROR: SmartAPI not installed. Install with: pip install smartapi-python")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/historical_downloader.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class SimpleHistoricalDownloader:
    """Simple Historical Data Downloader"""
    
    def __init__(self):
        """Initialize the downloader"""
        self.smart_api = None
        self.symbols = []
        self.data_dir = "data/historical"
        
        # Create data directory
        Path(self.data_dir).mkdir(parents=True, exist_ok=True)
        Path("logs").mkdir(parents=True, exist_ok=True)
        
        logger.info("Simple Historical Data Downloader initialized")
    
    async def initialize(self) -> bool:
        """Initialize API connection"""
        try:
            # Get credentials from environment
            api_key = os.getenv('SMARTAPI_API_KEY')
            username = os.getenv('SMARTAPI_USERNAME')
            password = os.getenv('SMARTAPI_PASSWORD')
            totp_token = os.getenv('SMARTAPI_TOTP_TOKEN')
            
            if not all([api_key, username, password, totp_token]):
                logger.error("Missing SmartAPI credentials in environment variables")
                logger.error("Please set SMARTAPI_API_KEY, SMARTAPI_USERNAME, SMARTAPI_PASSWORD, SMARTAPI_TOTP_TOKEN")
                return False
            
            # Initialize SmartAPI
            self.smart_api = SmartConnect(api_key=api_key)
            
            logger.info("Logging into SmartAPI...")
            
            # Generate TOTP
            import pyotp
            totp = pyotp.TOTP(totp_token)
            totp_code = totp.now()
            
            # Login
            data = self.smart_api.generateSession(username, password, totp_code)
            
            if data['status']:
                logger.info("SmartAPI login successful")
                return True
            else:
                logger.error(f"SmartAPI login failed: {data.get('message', 'Unknown error')}")
                return False
                
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            return False
    
    def load_symbols(self):
        """Load symbols from backup parquet file"""
        try:
            # First priority: Load from backup parquet file
            historical_file = r"data\historical\backup\historical_1hr.parquet"
            if Path(historical_file).exists():
                logger.info(f"Loading symbols from {historical_file}")
                df = pl.read_parquet(historical_file)

                # Check for different possible column names
                if 'symbol' in df.columns:
                    unique_symbols = df['symbol'].unique().to_list()
                elif 'Stock_Name' in df.columns:
                    unique_symbols = df['Stock_Name'].unique().to_list()
                elif ' Stock_Name' in df.columns:  # Column with leading space
                    unique_symbols = df[' Stock_Name'].unique().to_list()
                else:
                    logger.warning(f"No symbol column found in backup file. Available columns: {df.columns}")
                    unique_symbols = []

                if unique_symbols:
                    # Get symbol-token mapping from the backup file
                    symbol_token_df = df.select([' Stock_Name', ' SymbolToken']).unique()
                    symbol_token_map = dict(zip(symbol_token_df[' Stock_Name'].to_list(),
                                              symbol_token_df[' SymbolToken'].to_list()))

                    # Convert to symbol format expected by API
                    self.symbols = []
                    for symbol in unique_symbols:
                        token = str(symbol_token_map.get(symbol, '0'))
                        self.symbols.append({
                            'symbol': symbol,
                            'token': token,
                            'exchange': 'NSE'
                        })

                logger.info(f"Loaded {len(unique_symbols)} symbols from backup historical data")
                return

            # Second priority: Try to load from config
            symbols_file = "config/symbols.json"
            if Path(symbols_file).exists():
                with open(symbols_file, 'r') as f:
                    self.symbols = json.load(f)
                logger.info(f"Loaded {len(self.symbols)} symbols from config")
                return

            # Fallback: Try to load from stock universe
            try:
                from utils.stock_universe import StockUniverse
                stock_universe = StockUniverse()
                if stock_universe.load_stock_universe():
                    # Get top 50 large cap stocks
                    large_cap_stocks = stock_universe.get_stocks_by_market_cap("Large")[:50]
                    self.symbols = []
                    for stock in large_cap_stocks:
                        self.symbols.append({
                            'symbol': f'{stock.symbol}-EQ',
                            'token': stock.token,
                            'exchange': 'NSE'
                        })
                    logger.info(f"Loaded {len(self.symbols)} symbols from stock universe")
                    return
            except ImportError:
                pass

            # Final fallback: empty list
            logger.warning("No symbols available - stock universe not loaded")
            self.symbols = []
            
        except Exception as e:
            logger.error(f"Error loading symbols: {e}")
            self.symbols = []
    
    async def download_symbol_data(self, symbol: str, token: str, exchange: str,
                                 from_date: datetime, to_date: datetime) -> Optional[pl.DataFrame]:
        """Download data for a single symbol"""
        try:
            # Prepare request with correct SmartAPI format
            hist_params = {
                "exchange": exchange,
                "symboltoken": token,
                "interval": "FIVE_MINUTE",  # Correct interval format
                "fromdate": from_date.strftime("%Y-%m-%d %H:%M"),
                "todate": to_date.strftime("%Y-%m-%d %H:%M")
            }

            logger.debug(f"Requesting {symbol} with params: {hist_params}")
            
            # Call API with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = self.smart_api.getCandleData(hist_params)

                    if response.get('status'):
                        break  # Success, exit retry loop
                    else:
                        error_msg = response.get('message', 'Unknown error')
                        if attempt < max_retries - 1:
                            logger.warning(f"Attempt {attempt + 1} failed for {symbol}: {error_msg}. Retrying...")
                            await asyncio.sleep(1)  # Wait before retry
                        else:
                            logger.error(f"Failed to download {symbol} after {max_retries} attempts: {error_msg}")
                            return None
                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"Attempt {attempt + 1} failed for {symbol}: {e}. Retrying...")
                        await asyncio.sleep(1)
                    else:
                        logger.error(f"Failed to download {symbol} after {max_retries} attempts: {e}")
                        return None
            
            data = response.get('data', [])
            if not data:
                logger.warning(f"No data for {symbol}")
                return None
            
            # Convert to DataFrame with separate date and time columns
            timestamps = [datetime.fromisoformat(row[0]) for row in data]
            df = pl.DataFrame({
                'date': [ts.strftime('%d-%m-%Y') for ts in timestamps],
                'time': [ts.strftime('%H:%M:%S') for ts in timestamps],
                'open': [float(row[1]) for row in data],
                'high': [float(row[2]) for row in data],
                'low': [float(row[3]) for row in data],
                'close': [float(row[4]) for row in data],
                'volume': [int(row[5]) for row in data],
                'symbol': [symbol] * len(data)
            })
            
            logger.info(f"Downloaded {len(df)} records for {symbol}")
            return df
            
        except Exception as e:
            logger.error(f"Error downloading {symbol}: {e}")
            return None
    
    async def download_all_symbols(self, from_date_str: str = None, to_date_str: str = None, 
                                 days_back: int = 7) -> bool:
        """Download data for all symbols"""
        try:
            if not await self.initialize():
                return False
            
            self.load_symbols()
            
            # Parse dates
            if from_date_str and to_date_str:
                try:
                    from_date = datetime.strptime(from_date_str, "%d-%m-%Y")
                    to_date = datetime.strptime(to_date_str, "%d-%m-%Y")
                    from_date = from_date.replace(hour=9, minute=15)
                    to_date = to_date.replace(hour=15, minute=30)
                    logger.info(f"Using manual date range: {from_date_str} to {to_date_str}")
                except ValueError as e:
                    logger.error(f"Invalid date format: {e}")
                    return False
            else:
                to_date = datetime.now()
                from_date = to_date - timedelta(days=days_back)
                logger.info(f"Using automatic date range: {days_back} days back")
            
            logger.info(f"Downloading data from {from_date.strftime('%d-%m-%Y %H:%M')} to {to_date.strftime('%d-%m-%Y %H:%M')}")
            logger.info(f"Processing ALL {len(self.symbols)} symbols")
            
            all_data = []
            failed_symbols = []
            
            for i, symbol_info in enumerate(self.symbols):
                try:
                    symbol = symbol_info['symbol']
                    token = symbol_info['token']
                    exchange = symbol_info['exchange']
                    
                    logger.info(f"Downloading {i+1}/{len(self.symbols)}: {symbol}")
                    
                    df = await self.download_symbol_data(symbol, token, exchange, from_date, to_date)
                    
                    if df is not None and len(df) > 0:
                        all_data.append(df)
                    else:
                        failed_symbols.append(symbol)
                    
                    # Rate limiting - increased to avoid API limits
                    await asyncio.sleep(0.75)  # 1 second between requests
                    
                except Exception as e:
                    logger.error(f"Error downloading {symbol}: {e}")
                    failed_symbols.append(symbol)
            
            if failed_symbols:
                logger.warning(f"Failed to download {len(failed_symbols)} symbols")
            
            if not all_data:
                logger.error("No data downloaded")
                return False
            
            # Combine and save
            combined_df = pl.concat(all_data)
            combined_df = combined_df.sort(['symbol', 'date', 'time'])
            
            output_file = f"{self.data_dir}/historical_5min.parquet"
            combined_df.write_parquet(output_file)
            
            logger.info(f"Saved {len(combined_df)} records to {output_file}")
            logger.info(f"Successfully downloaded: {len(self.symbols) - len(failed_symbols)} symbols")
            
            # Summary
            logger.info("Download Summary:")
            logger.info(f"   Total Records: {len(combined_df)}")
            logger.info(f"   Unique Symbols: {combined_df['symbol'].n_unique()}")
            
            file_size = Path(output_file).stat().st_size / (1024 * 1024)
            logger.info(f"   File Size: {file_size:.2f} MB")
            
            if len(combined_df) > 0:
                date_range = f"{combined_df['date'].min()} to {combined_df['date'].max()}"
                logger.info(f"   Date Range: {date_range}")
            
            logger.info("SUCCESS: Historical data download completed!")
            return True
            
        except Exception as e:
            logger.error(f"Download failed: {e}")
            return False

async def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Download historical data from SmartAPI')
    parser.add_argument('--from-date', type=str, help='Start date in dd-mm-yyyy format')
    parser.add_argument('--to-date', type=str, help='End date in dd-mm-yyyy format')
    parser.add_argument('--days', type=int, default=7, help='Days back if dates not provided')
    
    args = parser.parse_args()
    
    if (args.from_date and not args.to_date) or (args.to_date and not args.from_date):
        logger.error("Both --from-date and --to-date must be provided together")
        sys.exit(1)
    
    downloader = SimpleHistoricalDownloader()
    success = await downloader.download_all_symbols(
        from_date_str=args.from_date,
        to_date_str=args.to_date,
        days_back=args.days
    )
    
    if not success:
        logger.error("ERROR: Download failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
