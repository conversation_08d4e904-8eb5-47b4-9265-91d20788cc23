#!/usr/bin/env python3
"""
Instrument Master File Parser for Angel One SmartAPI
Handles symbol-to-token mapping for WebSocket subscriptions
"""

import json
import logging
import os
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import pandas as pd

logger = logging.getLogger(__name__)

@dataclass
class InstrumentInfo:
    token: str
    symbol: str
    name: str
    expiry: str
    strike: str
    lotsize: str
    instrumenttype: str
    exch_seg: str
    tick_size: str

class InstrumentMaster:
    """Manages Angel One instrument master data for token mapping"""
    
    def __init__(self, instruments_file: str = "instruments.json"):
        self.instruments_file = instruments_file
        self.instruments_data = []
        self.symbol_to_token = {}
        self.token_to_symbol = {}
        self.nifty_50_tokens = {}
        self.nifty_500_tokens = {}
        
        logger.info("[INIT] Instrument Master initialized")
    
    def load_instruments(self) -> bool:
        """Load and parse instrument master file"""
        try:
            if not os.path.exists(self.instruments_file):
                logger.error(f"[ERROR] Instrument file not found: {self.instruments_file}")
                return False
            
            logger.info(f"[LOAD] Loading instruments from {self.instruments_file}...")
            
            with open(self.instruments_file, 'r') as f:
                self.instruments_data = json.load(f)
            
            logger.info(f"[SUCCESS] Loaded {len(self.instruments_data)} instruments")
            
            # Build mappings
            self._build_mappings()
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load instruments: {e}")
            return False
    
    def _build_mappings(self):
        """Build symbol-to-token and token-to-symbol mappings"""
        try:
            logger.info("[BUILD] Building symbol-token mappings...")
            
            for instrument in self.instruments_data:
                token = instrument.get('token', '')
                symbol = instrument.get('symbol', '')
                name = instrument.get('name', '')
                exch_seg = instrument.get('exch_seg', '')
                instrumenttype = instrument.get('instrumenttype', '')
                
                if token and symbol:
                    # Create unique key with exchange
                    key = f"{symbol}_{exch_seg}"

                    self.symbol_to_token[key] = token
                    self.token_to_symbol[token] = {
                        'symbol': symbol,
                        'name': name,
                        'exch_seg': exch_seg,
                        'instrumenttype': instrumenttype
                    }

                    # Handle NSE equity stocks (they have empty instrumenttype and symbol ends with -EQ)
                    if exch_seg == 'NSE' and symbol.endswith('-EQ') and not instrumenttype:
                        # Store both with and without -EQ suffix
                        clean_symbol = symbol.replace('-EQ', '')
                        self.symbol_to_token[clean_symbol] = token
                        self.symbol_to_token[f"{clean_symbol}_NSE"] = token

                    # Handle BSE equity stocks
                    elif exch_seg == 'BSE' and not instrumenttype:
                        self.symbol_to_token[f"{symbol}_BSE"] = token
            
            logger.info(f"[SUCCESS] Built mappings for {len(self.symbol_to_token)} symbols")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to build mappings: {e}")
    
    def get_token(self, symbol: str, exchange: str = "NSE") -> Optional[str]:
        """Get token for a symbol"""
        try:
            # Try with exchange first
            key = f"{symbol}_{exchange}"
            if key in self.symbol_to_token:
                return self.symbol_to_token[key]
            
            # Try without exchange
            if symbol in self.symbol_to_token:
                return self.symbol_to_token[symbol]
            
            logger.warning(f"[WARN] Token not found for symbol: {symbol}")
            return None
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get token for {symbol}: {e}")
            return None
    
    def get_symbol(self, token: str) -> Optional[Dict]:
        """Get symbol info for a token"""
        try:
            return self.token_to_symbol.get(token)
        except Exception as e:
            logger.error(f"[ERROR] Failed to get symbol for token {token}: {e}")
            return None
    
    def get_nifty_50_tokens(self) -> Dict[str, str]:
        """Get tokens for Nifty 50 stocks"""
        try:
            if self.nifty_50_tokens:
                return self.nifty_50_tokens
            
            # Common Nifty 50 symbols
            nifty_50_symbols = [
                'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'HINDUNILVR', 'ITC',
                'SBIN', 'BHARTIARTL', 'ASIANPAINT', 'MARUTI', 'BAJFINANCE', 'HCLTECH',
                'AXISBANK', 'LT', 'DMART', 'SUNPHARMA', 'TITAN', 'ULTRACEMCO', 'NESTLEIND',
                'WIPRO', 'NTPC', 'JSWSTEEL', 'POWERGRID', 'TATAMOTORS', 'TECHM', 'INDUSINDBK',
                'HDFCLIFE', 'BAJAJFINSV', 'GRASIM', 'ADANIENT', 'KOTAKBANK', 'ONGC',
                'TATASTEEL', 'CIPLA', 'COALINDIA', 'BRITANNIA', 'EICHERMOT', 'DRREDDY',
                'BPCL', 'APOLLOHOSP', 'DIVISLAB', 'TATACONSUM', 'BAJAJ-AUTO', 'HEROMOTOCO',
                'SBILIFE', 'LTIM', 'ADANIPORTS', 'HINDALCO'
            ]
            
            for symbol in nifty_50_symbols:
                token = self.get_token(symbol)
                if token:
                    self.nifty_50_tokens[symbol] = token
            
            logger.info(f"[SUCCESS] Found tokens for {len(self.nifty_50_tokens)} Nifty 50 stocks")
            return self.nifty_50_tokens
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get Nifty 50 tokens: {e}")
            return {}
    
    def get_top_liquid_tokens(self, count: int = 100) -> Dict[str, str]:
        """Get tokens for top liquid stocks"""
        try:
            # Filter for NSE equity instruments (empty instrumenttype and symbol ends with -EQ)
            equity_instruments = [
                inst for inst in self.instruments_data
                if inst.get('exch_seg') == 'NSE' and
                   not inst.get('instrumenttype') and  # Empty instrumenttype for equity
                   inst.get('symbol', '').endswith('-EQ') and
                   inst.get('symbol', '').replace('-EQ', '').replace('-', '').isalpha()  # Only alphabetic symbols
            ]
            
            # Sort by some liquidity proxy (for now, just take first N)
            # In a real implementation, you'd sort by market cap or volume
            top_instruments = equity_instruments[:count]
            
            tokens = {}
            for inst in top_instruments:
                symbol = inst.get('symbol', '')
                token = inst.get('token', '')
                if symbol and token:
                    tokens[symbol] = token
            
            logger.info(f"[SUCCESS] Found {len(tokens)} top liquid stock tokens")
            return tokens
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get top liquid tokens: {e}")
            return {}
    
    def create_subscription_data(self, symbols: List[str], exchange_type: int = 1) -> List[Dict]:
        """Create properly formatted subscription data for WebSocket"""
        try:
            tokens = []
            
            for symbol in symbols:
                token = self.get_token(symbol)
                if token:
                    tokens.append(token)
                else:
                    logger.warning(f"[WARN] Token not found for symbol: {symbol}")
            
            if not tokens:
                logger.error("[ERROR] No valid tokens found for subscription")
                return []
            
            subscription_data = [{
                "exchangeType": exchange_type,  # 1 = NSE, 2 = NFO, etc.
                "tokens": tokens
            }]
            
            logger.info(f"[SUCCESS] Created subscription data for {len(tokens)} tokens")
            return subscription_data
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to create subscription data: {e}")
            return []
    
    def get_sample_tokens_for_testing(self, symbols: List[str] = None) -> Dict[str, str]:
        """Get a small set of tokens for testing"""
        try:
            # Use provided symbols or load from config
            if symbols is None:
                # Try to load from stock universe or config
                try:
                    from utils.stock_universe import StockUniverse
                    stock_universe = StockUniverse()
                    if stock_universe.load_stock_universe():
                        # Get top 5 large cap stocks for testing
                        large_cap_stocks = stock_universe.get_stocks_by_market_cap("Large")[:5]
                        symbols = [stock.symbol for stock in large_cap_stocks]
                    else:
                        logger.warning("[WARN] Could not load stock universe for testing")
                        return {}
                except ImportError:
                    logger.warning("[WARN] Stock universe not available for testing")
                    return {}

            tokens = {}
            for symbol in symbols:
                token = self.get_token(symbol)
                if token:
                    tokens[symbol] = token

            logger.info(f"[SUCCESS] Got {len(tokens)} test tokens for symbols: {list(tokens.keys())}")
            return tokens

        except Exception as e:
            logger.error(f"[ERROR] Failed to get test tokens: {e}")
            return {}
    
    def save_token_mapping(self, filename: str = "token_mapping.json"):
        """Save symbol-token mapping to file for quick access"""
        try:
            mapping_data = {
                'symbol_to_token': self.symbol_to_token,
                'nifty_50_tokens': self.get_nifty_50_tokens(),
                'total_instruments': len(self.instruments_data)
            }
            
            with open(filename, 'w') as f:
                json.dump(mapping_data, f, indent=2)
            
            logger.info(f"[SUCCESS] Saved token mapping to {filename}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save token mapping: {e}")

# Utility functions
def get_instrument_master() -> InstrumentMaster:
    """Get a configured instrument master instance"""
    master = InstrumentMaster()
    if master.load_instruments():
        return master
    else:
        logger.error("[ERROR] Failed to load instrument master")
        return None

def get_tokens_for_symbols(symbols: List[str]) -> Dict[str, str]:
    """Quick utility to get tokens for a list of symbols"""
    master = get_instrument_master()
    if not master:
        return {}
    
    tokens = {}
    for symbol in symbols:
        token = master.get_token(symbol)
        if token:
            tokens[symbol] = token
    
    return tokens

if __name__ == "__main__":
    # Test the instrument master
    logging.basicConfig(level=logging.INFO)
    
    master = InstrumentMaster()
    if master.load_instruments():
        print("✅ Instrument master loaded successfully")
        
        # Test token lookup
        test_symbols = ['RELIANCE', 'TCS', 'INFY']
        print(f"\n🔍 Testing token lookup for: {test_symbols}")
        
        for symbol in test_symbols:
            token = master.get_token(symbol)
            print(f"   {symbol}: {token}")
        
        # Get Nifty 50 tokens
        nifty_tokens = master.get_nifty_50_tokens()
        print(f"\n📊 Found {len(nifty_tokens)} Nifty 50 tokens")
        
        # Create sample subscription data
        subscription_data = master.create_subscription_data(test_symbols)
        print(f"\n📡 Sample subscription data: {subscription_data}")
        
        # Save mapping
        master.save_token_mapping()
        print("💾 Token mapping saved")
        
    else:
        print("❌ Failed to load instrument master")
