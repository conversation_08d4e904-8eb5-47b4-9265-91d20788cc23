#!/usr/bin/env python3
"""
AI Training Agent FastAPI Server
Real-time strategy performance prediction and ranking API
"""

import os
import sys
import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import numpy as np

# FastAPI imports
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_training_agent import AITrainingAgent, AITrainingConfig
from ai_training_utils import load_config, get_system_info, check_dependencies

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [LIST] PYDANTIC MODELS
# ═══════════════════════════════════════════════════════════════════════════════

class StrategyFeatures(BaseModel):
    """Input features for strategy prediction"""
    n_trades: int = Field(..., description="Number of trades")
    avg_holding_period: float = Field(..., description="Average holding period")
    capital_at_risk: float = Field(..., description="Capital at risk percentage")
    liquidity: float = Field(..., description="Liquidity score")
    volatility: float = Field(..., description="Volatility measure")
    market_regime: str = Field(..., description="Market regime (bull/bear/sideways)")
    correlation_index: float = Field(..., description="Correlation with market index")
    drawdown_duration: float = Field(..., description="Drawdown duration")
    winning_trades: int = Field(..., description="Number of winning trades")
    losing_trades: int = Field(..., description="Number of losing trades")
    avg_win: float = Field(..., description="Average win amount")
    avg_loss: float = Field(..., description="Average loss amount")
    total_pnl: float = Field(..., description="Total P&L")
    position_size_pct: float = Field(..., description="Position size percentage")

class StrategyPrediction(BaseModel):
    """Strategy performance prediction"""
    strategy_name: str
    predicted_metrics: Dict[str, float]
    confidence_score: float
    composite_score: float
    market_regime: str
    timestamp: str

class BulkPredictionRequest(BaseModel):
    """Bulk prediction request"""
    strategies: List[Dict[str, Any]] = Field(..., description="List of strategy features")
    market_regime: str = Field(default="normal", description="Market regime")

class RankingResponse(BaseModel):
    """Strategy ranking response"""
    rankings: List[StrategyPrediction]
    total_strategies: int
    pareto_frontier: List[StrategyPrediction]
    system_info: Dict[str, Any]

class TrainingRequest(BaseModel):
    """Training request"""
    data_file: Optional[str] = Field(None, description="Optional custom data file")
    optimize_hyperparams: bool = Field(True, description="Whether to optimize hyperparameters")

class TrainingStatus(BaseModel):
    """Training status response"""
    status: str
    progress: float
    message: str
    started_at: Optional[str] = None
    estimated_completion: Optional[str] = None

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] FASTAPI APPLICATION
# ═══════════════════════════════════════════════════════════════════════════════

# Initialize FastAPI app
app = FastAPI(
    title="AI Training Agent API",
    description="Multi-target strategy performance prediction and ranking API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
agent: Optional[AITrainingAgent] = None
training_status = {"status": "idle", "progress": 0.0, "message": "Ready"}

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] STARTUP AND SHUTDOWN
# ═══════════════════════════════════════════════════════════════════════════════

@app.on_event("startup")
async def startup_event():
    """Initialize the AI Training Agent on startup"""
    global agent
    
    logger.info("[INIT] Starting AI Training Agent API...")
    
    try:
        # Load configuration
        config_dict = load_config("config/ai_training_config.yaml")
        config = AITrainingConfig()
        
        # Initialize agent
        agent = AITrainingAgent(config)
        
        # Try to load existing models
        try:
            agent.load_models()
            logger.info("[SUCCESS] Existing models loaded successfully")
        except FileNotFoundError:
            logger.info("[WARN]  No existing models found. Training required.")
        
        logger.info("[SUCCESS] AI Training Agent API started successfully")
        
    except Exception as e:
        logger.error(f"[ERROR] Failed to start AI Training Agent API: {str(e)}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("[STOP] Shutting down AI Training Agent API...")

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] API ENDPOINTS
# ═══════════════════════════════════════════════════════════════════════════════

@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint"""
    return {
        "message": "AI Training Agent API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/health", response_model=Dict[str, Any])
async def health_check():
    """Health check endpoint"""
    global agent
    
    system_info = get_system_info()
    dependencies = check_dependencies()
    
    return {
        "status": "healthy",
        "agent_loaded": agent is not None,
        "models_trained": agent.is_trained if agent else False,
        "system_info": system_info,
        "dependencies": dependencies,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/predict", response_model=StrategyPrediction)
async def predict_strategy(features: StrategyFeatures, market_regime: str = "normal"):
    """Predict performance for a single strategy"""
    global agent
    
    if not agent or not agent.is_trained:
        raise HTTPException(status_code=400, detail="Models not trained. Please train models first.")
    
    try:
        # Convert features to numpy array
        feature_values = [
            features.n_trades, features.avg_holding_period, features.capital_at_risk,
            features.liquidity, features.volatility, 
            0 if features.market_regime == "bull" else 1 if features.market_regime == "bear" else 2,
            features.correlation_index, features.drawdown_duration,
            features.winning_trades, features.losing_trades,
            features.avg_win, features.avg_loss, features.total_pnl,
            features.position_size_pct
        ]
        
        X = np.array([feature_values])
        
        # Make prediction
        predictions, confidence = agent.predict(X)
        
        # Create prediction dictionary
        pred_dict = dict(zip(agent.config.target_columns, predictions[0]))
        
        # Calculate composite score
        rankings = agent.rank_strategies(predictions, ["Strategy"], market_regime)
        composite_score = rankings[0]['composite_score']
        
        return StrategyPrediction(
            strategy_name="Strategy",
            predicted_metrics=pred_dict,
            confidence_score=float(confidence[0]),
            composite_score=float(composite_score),
            market_regime=market_regime,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.post("/predict_bulk", response_model=RankingResponse)
async def predict_bulk_strategies(request: BulkPredictionRequest):
    """Predict and rank multiple strategies"""
    global agent
    
    if not agent or not agent.is_trained:
        raise HTTPException(status_code=400, detail="Models not trained. Please train models first.")
    
    try:
        # Convert strategies to feature matrix
        feature_matrix = []
        strategy_names = []
        
        for i, strategy in enumerate(request.strategies):
            strategy_names.append(strategy.get('strategy_name', f'Strategy_{i}'))
            
            # Extract features (handle missing values)
            feature_values = [
                strategy.get('n_trades', 0),
                strategy.get('avg_holding_period', 0),
                strategy.get('capital_at_risk', 0),
                strategy.get('liquidity', 0),
                strategy.get('volatility', 0),
                0 if strategy.get('market_regime', 'normal') == "bull" else 1 if strategy.get('market_regime', 'normal') == "bear" else 2,
                strategy.get('correlation_index', 0),
                strategy.get('drawdown_duration', 0),
                strategy.get('winning_trades', 0),
                strategy.get('losing_trades', 0),
                strategy.get('avg_win', 0),
                strategy.get('avg_loss', 0),
                strategy.get('total_pnl', 0),
                strategy.get('position_size_pct', 0)
            ]
            feature_matrix.append(feature_values)
        
        X = np.array(feature_matrix)
        
        # Make predictions
        predictions, confidence = agent.predict(X)
        
        # Rank strategies
        rankings = agent.rank_strategies(predictions, strategy_names, request.market_regime)
        
        # Convert to response format
        strategy_predictions = []
        for i, ranking in enumerate(rankings):
            strategy_predictions.append(StrategyPrediction(
                strategy_name=ranking['strategy_name'],
                predicted_metrics=ranking['predicted_metrics'],
                confidence_score=float(confidence[i]),
                composite_score=float(ranking['composite_score']),
                market_regime=ranking['market_regime'],
                timestamp=datetime.now().isoformat()
            ))
        
        # Calculate Pareto frontier (simplified)
        pareto_frontier = strategy_predictions[:min(5, len(strategy_predictions))]
        
        return RankingResponse(
            rankings=strategy_predictions,
            total_strategies=len(strategy_predictions),
            pareto_frontier=pareto_frontier,
            system_info=get_system_info()
        )
        
    except Exception as e:
        logger.error(f"Bulk prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Bulk prediction failed: {str(e)}")

@app.get("/training/status", response_model=TrainingStatus)
async def get_training_status():
    """Get current training status"""
    global training_status
    
    return TrainingStatus(**training_status)

@app.post("/training/start")
async def start_training(request: TrainingRequest, background_tasks: BackgroundTasks):
    """Start model training in background"""
    global agent, training_status
    
    if training_status["status"] == "training":
        raise HTTPException(status_code=400, detail="Training already in progress")
    
    if not agent:
        raise HTTPException(status_code=400, detail="Agent not initialized")
    
    # Start training in background
    background_tasks.add_task(
        train_models_background, 
        request.data_file, 
        request.optimize_hyperparams
    )
    
    training_status = {
        "status": "training",
        "progress": 0.0,
        "message": "Training started",
        "started_at": datetime.now().isoformat()
    }
    
    return {"message": "Training started", "status": "training"}

async def train_models_background(data_file: Optional[str], optimize_hyperparams: bool):
    """Background training task"""
    global agent, training_status
    
    try:
        training_status.update({
            "status": "training",
            "progress": 10.0,
            "message": "Loading data..."
        })
        
        # Train models
        results = await agent.train_async(data_file, optimize_hyperparams)
        
        training_status.update({
            "status": "completed",
            "progress": 100.0,
            "message": "Training completed successfully"
        })
        
        logger.info("[SUCCESS] Background training completed")
        
    except Exception as e:
        training_status.update({
            "status": "failed",
            "progress": 0.0,
            "message": f"Training failed: {str(e)}"
        })
        
        logger.error(f"[ERROR] Background training failed: {str(e)}")

@app.get("/models/info", response_model=Dict[str, Any])
async def get_model_info():
    """Get information about loaded models"""
    global agent
    
    if not agent:
        raise HTTPException(status_code=400, detail="Agent not initialized")
    
    return {
        "is_trained": agent.is_trained,
        "target_columns": agent.config.target_columns,
        "feature_columns": agent.config.feature_columns,
        "ensemble_weights": agent.config.ensemble_weights,
        "feature_importance": agent.feature_importance if hasattr(agent, 'feature_importance') else {},
        "models_available": list(agent.models.keys()) if agent.models else []
    }

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

if __name__ == "__main__":
    uvicorn.run(
        "ai_training_api:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
