#!/usr/bin/env python3
"""
Paper Trading Virtual Account System
Simulates real trading with virtual balance, commissions, and risk management
"""

import os
import json
import logging
import asyncio
import polars as pl
import pyarrow as pa
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DATA MODELS
# ═══════════════════════════════════════════════════════════════════════════════

class OrderStatus(Enum):
    """Order status for paper trading"""
    PENDING = "PENDING"
    EXECUTED = "EXECUTED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"

class TransactionType(Enum):
    """Transaction types"""
    BUY = "BUY"
    SELL = "SELL"

@dataclass
class PaperTrade:
    """Paper trade record"""
    trade_id: str
    symbol: str
    exchange: str
    transaction_type: str  # BUY/SELL
    quantity: int
    price: float
    order_type: str  # MARKET/LIMIT
    product_type: str  # MIS/CNC
    timestamp: datetime
    status: str = "EXECUTED"
    commission: float = 0.0
    brokerage: float = 0.0
    stt: float = 0.0
    gst: float = 0.0
    stamp_duty: float = 0.0
    total_charges: float = 0.0
    net_amount: float = 0.0
    strategy_name: str = ""
    signal_id: str = ""
    
    def __post_init__(self):
        if isinstance(self.timestamp, str):
            self.timestamp = datetime.fromisoformat(self.timestamp)

@dataclass
class PaperPosition:
    """Paper trading position"""
    symbol: str
    exchange: str
    quantity: int
    average_price: float
    current_price: float
    unrealized_pnl: float
    realized_pnl: float
    last_updated: datetime
    
    def __post_init__(self):
        if isinstance(self.last_updated, str):
            self.last_updated = datetime.fromisoformat(self.last_updated)

@dataclass
class DailyStats:
    """Daily trading statistics"""
    date: date
    trades_count: int
    total_turnover: float
    gross_pnl: float
    total_charges: float
    net_pnl: float
    winning_trades: int
    losing_trades: int
    largest_win: float
    largest_loss: float
    
    def __post_init__(self):
        if isinstance(self.date, str):
            self.date = date.fromisoformat(self.date)

# ═══════════════════════════════════════════════════════════════════════════════
# [MONEY] VIRTUAL ACCOUNT SYSTEM
# ═══════════════════════════════════════════════════════════════════════════════

class VirtualAccount:
    """
    Virtual Account for Paper Trading
    
    Features:
    - Virtual balance management
    - Realistic commission calculation
    - Trade limits enforcement
    - Position tracking
    - PnL calculation
    - Daily statistics
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize virtual account"""
        
        self.config = config
        self.paper_config = config.get('paper_trading', {})
        
        # Account settings
        self.initial_balance = float(os.getenv('PAPER_TRADING_INITIAL_BALANCE', 100000))
        self.max_trades_per_day = int(os.getenv('PAPER_TRADING_MAX_TRADES_PER_DAY', 5))
        self.max_position_size = float(os.getenv('PAPER_TRADING_MAX_POSITION_SIZE', 20000))
        self.max_daily_loss = float(os.getenv('PAPER_TRADING_MAX_DAILY_LOSS', 5000))
        self.margin_multiplier = float(os.getenv('PAPER_TRADING_MARGIN_MULTIPLIER', 3.5))
        
        # Commission structure (Angel One rates)
        self.commission_rate = float(os.getenv('PAPER_TRADING_COMMISSION_RATE', 0.0003))  # 0.03%
        self.brokerage_flat = float(os.getenv('PAPER_TRADING_BROKERAGE_FLAT', 20))  # Rs.20 per trade
        self.stt_rate = float(os.getenv('PAPER_TRADING_STT_RATE', 0.001))  # 0.1%
        self.gst_rate = float(os.getenv('PAPER_TRADING_GST_RATE', 0.18))  # 18%
        self.sebi_charges = float(os.getenv('PAPER_TRADING_SEBI_CHARGES', 10))  # Rs.10 per crore
        self.stamp_duty_rate = float(os.getenv('PAPER_TRADING_STAMP_DUTY_RATE', 0.00003))  # 0.003%
        
        # Account state
        self.current_balance = self.initial_balance
        self.available_margin = self.initial_balance * self.margin_multiplier
        self.used_margin = 0.0
        self.unrealized_pnl = 0.0
        self.realized_pnl = 0.0
        
        # Trading data
        self.trades: List[PaperTrade] = []
        self.positions: Dict[str, PaperPosition] = {}
        self.daily_stats: Dict[date, DailyStats] = {}
        
        # Data persistence
        self.data_file = "data/paper_trading_account.json"
        self.trades_file = "data/paper_trading_trades.parquet"
        
        # Load existing data
        self._load_account_data()
        
        logger.info(f"[MONEY] Virtual Account initialized with balance: Rs.{self.current_balance:,.2f}")
    
    def _load_account_data(self):
        """Load account data from file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                
                self.current_balance = data.get('current_balance', self.initial_balance)
                self.available_margin = data.get('available_margin', self.initial_balance * self.margin_multiplier)
                self.used_margin = data.get('used_margin', 0.0)
                self.unrealized_pnl = data.get('unrealized_pnl', 0.0)
                self.realized_pnl = data.get('realized_pnl', 0.0)
                
                # Load trades
                trades_data = data.get('trades', [])
                self.trades = [PaperTrade(**trade) for trade in trades_data]
                
                # Load positions
                positions_data = data.get('positions', {})
                self.positions = {k: PaperPosition(**v) for k, v in positions_data.items()}
                
                # Load daily stats
                daily_stats_data = data.get('daily_stats', {})
                self.daily_stats = {date.fromisoformat(k): DailyStats(**v) for k, v in daily_stats_data.items()}
                
                logger.info(f"[SUCCESS] Loaded account data: Balance Rs.{self.current_balance:,.2f}, {len(self.trades)} trades")
            
        except Exception as e:
            logger.error(f"[ERROR] Error loading account data: {e}")
    
    def _save_account_data(self):
        """Save account data to file"""
        try:
            os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
            
            data = {
                'current_balance': self.current_balance,
                'available_margin': self.available_margin,
                'used_margin': self.used_margin,
                'unrealized_pnl': self.unrealized_pnl,
                'realized_pnl': self.realized_pnl,
                'trades': [asdict(trade) for trade in self.trades],
                'positions': {k: asdict(v) for k, v in self.positions.items()},
                'daily_stats': {k.isoformat(): asdict(v) for k, v in self.daily_stats.items()},
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            # Save trades to parquet for analysis
            if self.trades:
                trades_df = pl.DataFrame([asdict(trade) for trade in self.trades])
                trades_df.write_parquet(self.trades_file)
            
        except Exception as e:
            logger.error(f"[ERROR] Error saving account data: {e}")
    
    def calculate_charges(self, symbol: str, quantity: int, price: float, 
                         transaction_type: str, product_type: str) -> Dict[str, float]:
        """
        Calculate realistic trading charges based on Angel One structure
        
        Returns:
            Dictionary with breakdown of charges
        """
        try:
            turnover = quantity * price
            
            # Brokerage: Flat rate or percentage, whichever is lower
            brokerage_percent = turnover * self.commission_rate
            brokerage = min(self.brokerage_flat, brokerage_percent)
            
            # STT (Securities Transaction Tax) - only on sell side for equity delivery
            stt = 0.0
            if transaction_type == "SELL":
                if product_type == "CNC":  # Delivery
                    stt = turnover * self.stt_rate
                else:  # Intraday
                    stt = turnover * (self.stt_rate / 10)  # Lower rate for intraday
            
            # GST on brokerage
            gst = brokerage * self.gst_rate
            
            # SEBI charges (Rs.10 per crore)
            sebi_charges = (turnover / 10000000) * self.sebi_charges
            
            # Stamp duty (on buy side only)
            stamp_duty = 0.0
            if transaction_type == "BUY":
                stamp_duty = turnover * self.stamp_duty_rate
            
            total_charges = brokerage + stt + gst + sebi_charges + stamp_duty
            
            return {
                'brokerage': round(brokerage, 2),
                'stt': round(stt, 2),
                'gst': round(gst, 2),
                'sebi_charges': round(sebi_charges, 2),
                'stamp_duty': round(stamp_duty, 2),
                'total_charges': round(total_charges, 2)
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating charges: {e}")
            return {'total_charges': 0.0}
    
    def can_place_trade(self, symbol: str, quantity: int, price: float,
                       transaction_type: str, product_type: str, exchange: str = "NSE") -> Tuple[bool, str]:
        """
        Check if trade can be placed based on various limits
        
        Returns:
            Tuple of (can_trade, reason)
        """
        try:
            today = date.today()
            
            # Check daily trade limit
            today_trades = len([t for t in self.trades if t.timestamp.date() == today])
            if today_trades >= self.max_trades_per_day:
                return False, f"Daily trade limit exceeded ({self.max_trades_per_day} trades/day)"
            
            # Check position size limit
            trade_value = quantity * price
            if trade_value > self.max_position_size:
                return False, f"Position size exceeds limit (Rs.{self.max_position_size:,.2f})"
            
            # Check daily loss limit
            today_pnl = self._get_daily_pnl(today)
            if today_pnl < -self.max_daily_loss:
                return False, f"Daily loss limit exceeded (Rs.{self.max_daily_loss:,.2f})"
            
            # Check margin availability for buy orders
            if transaction_type == "BUY":
                charges = self.calculate_charges(symbol, quantity, price, transaction_type, product_type)
                required_margin = trade_value + charges['total_charges']
                
                if product_type == "MIS":  # Intraday - use margin
                    required_margin = required_margin / self.margin_multiplier
                
                if required_margin > (self.available_margin - self.used_margin):
                    return False, f"Insufficient margin. Required: Rs.{required_margin:,.2f}, Available: Rs.{(self.available_margin - self.used_margin):,.2f}"
            
            # Check if selling position exists
            if transaction_type == "SELL":
                position_key = f"{symbol}_{exchange}"
                if position_key not in self.positions:
                    return False, f"No position to sell for {symbol}"
                
                position = self.positions[position_key]
                if position.quantity < quantity:
                    return False, f"Insufficient quantity. Available: {position.quantity}, Requested: {quantity}"
            
            return True, "Trade validation successful"
            
        except Exception as e:
            logger.error(f"[ERROR] Error validating trade: {e}")
            return False, f"Validation error: {str(e)}"
    
    def _get_daily_pnl(self, target_date: date) -> float:
        """Get PnL for a specific date"""
        try:
            if target_date in self.daily_stats:
                return self.daily_stats[target_date].net_pnl
            
            # Calculate from trades
            daily_trades = [t for t in self.trades if t.timestamp.date() == target_date]
            if not daily_trades:
                return 0.0
            
            total_pnl = 0.0
            for trade in daily_trades:
                if trade.transaction_type == "SELL":
                    total_pnl += trade.net_amount
                else:
                    total_pnl -= trade.net_amount
            
            return total_pnl
            
        except Exception as e:
            logger.error(f"[ERROR] Error calculating daily PnL: {e}")
            return 0.0

    async def execute_trade(self, symbol: str, exchange: str, quantity: int, price: float,
                           transaction_type: str, order_type: str = "LIMIT",
                           product_type: str = "MIS", strategy_name: str = "",
                           signal_id: str = "") -> Tuple[bool, str, Optional[PaperTrade]]:
        """
        Execute a paper trade

        Returns:
            Tuple of (success, message, trade_record)
        """
        try:
            # Validate trade
            can_trade, reason = self.can_place_trade(symbol, quantity, price, transaction_type, product_type)
            if not can_trade:
                return False, reason, None

            # Calculate charges
            charges = self.calculate_charges(symbol, quantity, price, transaction_type, product_type)

            # Create trade record
            trade_id = f"PT_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.trades)+1:04d}"
            trade_value = quantity * price

            if transaction_type == "BUY":
                net_amount = trade_value + charges['total_charges']
            else:
                net_amount = trade_value - charges['total_charges']

            trade = PaperTrade(
                trade_id=trade_id,
                symbol=symbol,
                exchange=exchange,
                transaction_type=transaction_type,
                quantity=quantity,
                price=price,
                order_type=order_type,
                product_type=product_type,
                timestamp=datetime.now(),
                commission=charges.get('brokerage', 0.0),
                brokerage=charges.get('brokerage', 0.0),
                stt=charges.get('stt', 0.0),
                gst=charges.get('gst', 0.0),
                stamp_duty=charges.get('stamp_duty', 0.0),
                total_charges=charges['total_charges'],
                net_amount=net_amount,
                strategy_name=strategy_name,
                signal_id=signal_id
            )

            # Update account balances
            if transaction_type == "BUY":
                self._process_buy_trade(trade)
            else:
                self._process_sell_trade(trade)

            # Add to trades list
            self.trades.append(trade)

            # Update daily statistics
            self._update_daily_stats(trade)

            # Save data
            self._save_account_data()

            logger.info(f"[SUCCESS] Paper trade executed: {transaction_type} {quantity} {symbol} @ Rs.{price:.2f}")
            logger.info(f"   Trade ID: {trade_id}")
            logger.info(f"   Charges: Rs.{charges['total_charges']:.2f}")
            logger.info(f"   Net Amount: Rs.{net_amount:.2f}")

            return True, "Trade executed successfully", trade

        except Exception as e:
            logger.error(f"[ERROR] Error executing paper trade: {e}")
            return False, f"Execution error: {str(e)}", None

    def _process_buy_trade(self, trade: PaperTrade):
        """Process a buy trade"""
        try:
            position_key = f"{trade.symbol}_{trade.exchange}"

            # Update margin usage
            if trade.product_type == "MIS":  # Intraday
                margin_used = trade.net_amount / self.margin_multiplier
            else:  # Delivery
                margin_used = trade.net_amount

            self.used_margin += margin_used

            # Update or create position
            if position_key in self.positions:
                # Average down the position
                existing_pos = self.positions[position_key]
                total_quantity = existing_pos.quantity + trade.quantity
                total_value = (existing_pos.quantity * existing_pos.average_price) + (trade.quantity * trade.price)
                new_avg_price = total_value / total_quantity

                existing_pos.quantity = total_quantity
                existing_pos.average_price = new_avg_price
                existing_pos.current_price = trade.price
                existing_pos.last_updated = trade.timestamp
            else:
                # Create new position
                self.positions[position_key] = PaperPosition(
                    symbol=trade.symbol,
                    exchange=trade.exchange,
                    quantity=trade.quantity,
                    average_price=trade.price,
                    current_price=trade.price,
                    unrealized_pnl=0.0,
                    realized_pnl=0.0,
                    last_updated=trade.timestamp
                )

        except Exception as e:
            logger.error(f"[ERROR] Error processing buy trade: {e}")

    def _process_sell_trade(self, trade: PaperTrade):
        """Process a sell trade"""
        try:
            position_key = f"{trade.symbol}_{trade.exchange}"

            if position_key not in self.positions:
                logger.error(f"[ERROR] No position found for {trade.symbol}")
                return

            position = self.positions[position_key]

            # Calculate realized PnL
            realized_pnl = (trade.price - position.average_price) * trade.quantity - trade.total_charges
            self.realized_pnl += realized_pnl
            position.realized_pnl += realized_pnl

            # Update position quantity
            position.quantity -= trade.quantity
            position.current_price = trade.price
            position.last_updated = trade.timestamp

            # Remove position if fully sold
            if position.quantity <= 0:
                del self.positions[position_key]

            # Release margin
            if trade.product_type == "MIS":  # Intraday
                margin_released = trade.net_amount / self.margin_multiplier
            else:  # Delivery
                margin_released = trade.net_amount

            self.used_margin = max(0, self.used_margin - margin_released)

            # Update cash balance
            self.current_balance += trade.net_amount

        except Exception as e:
            logger.error(f"[ERROR] Error processing sell trade: {e}")

    def _update_daily_stats(self, trade: PaperTrade):
        """Update daily statistics"""
        try:
            today = trade.timestamp.date()

            if today not in self.daily_stats:
                self.daily_stats[today] = DailyStats(
                    date=today,
                    trades_count=0,
                    total_turnover=0.0,
                    gross_pnl=0.0,
                    total_charges=0.0,
                    net_pnl=0.0,
                    winning_trades=0,
                    losing_trades=0,
                    largest_win=0.0,
                    largest_loss=0.0
                )

            stats = self.daily_stats[today]
            stats.trades_count += 1
            stats.total_turnover += trade.quantity * trade.price
            stats.total_charges += trade.total_charges

            # Calculate PnL for sell trades
            if trade.transaction_type == "SELL":
                position_key = f"{trade.symbol}_{trade.exchange}"
                if position_key in self.positions:
                    position = self.positions[position_key]
                    trade_pnl = (trade.price - position.average_price) * trade.quantity - trade.total_charges
                    stats.gross_pnl += trade_pnl
                    stats.net_pnl += trade_pnl

                    if trade_pnl > 0:
                        stats.winning_trades += 1
                        stats.largest_win = max(stats.largest_win, trade_pnl)
                    else:
                        stats.losing_trades += 1
                        stats.largest_loss = min(stats.largest_loss, trade_pnl)

        except Exception as e:
            logger.error(f"[ERROR] Error updating daily stats: {e}")

    def update_position_prices(self, price_updates: Dict[str, float]):
        """Update current prices for positions"""
        try:
            for position_key, position in self.positions.items():
                symbol = position.symbol
                if symbol in price_updates:
                    position.current_price = price_updates[symbol]
                    position.unrealized_pnl = (position.current_price - position.average_price) * position.quantity
                    position.last_updated = datetime.now()

            # Update total unrealized PnL
            self.unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())

        except Exception as e:
            logger.error(f"[ERROR] Error updating position prices: {e}")

    def get_account_summary(self) -> Dict[str, Any]:
        """Get account summary"""
        try:
            total_pnl = self.realized_pnl + self.unrealized_pnl
            total_value = self.current_balance + self.unrealized_pnl

            today = date.today()
            today_trades = len([t for t in self.trades if t.timestamp.date() == today])
            today_pnl = self._get_daily_pnl(today)

            return {
                'initial_balance': self.initial_balance,
                'current_balance': self.current_balance,
                'available_margin': self.available_margin - self.used_margin,
                'used_margin': self.used_margin,
                'unrealized_pnl': self.unrealized_pnl,
                'realized_pnl': self.realized_pnl,
                'total_pnl': total_pnl,
                'total_value': total_value,
                'return_percent': ((total_value - self.initial_balance) / self.initial_balance) * 100,
                'total_trades': len(self.trades),
                'active_positions': len(self.positions),
                'today_trades': today_trades,
                'today_pnl': today_pnl,
                'trades_remaining_today': max(0, self.max_trades_per_day - today_trades)
            }

        except Exception as e:
            logger.error(f"[ERROR] Error getting account summary: {e}")
            return {}

    def get_positions_summary(self) -> List[Dict[str, Any]]:
        """Get positions summary"""
        try:
            positions_list = []

            for position in self.positions.values():
                position_value = position.quantity * position.current_price
                pnl_percent = ((position.current_price - position.average_price) / position.average_price) * 100

                positions_list.append({
                    'symbol': position.symbol,
                    'exchange': position.exchange,
                    'quantity': position.quantity,
                    'average_price': position.average_price,
                    'current_price': position.current_price,
                    'position_value': position_value,
                    'unrealized_pnl': position.unrealized_pnl,
                    'pnl_percent': pnl_percent,
                    'last_updated': position.last_updated.isoformat()
                })

            return positions_list

        except Exception as e:
            logger.error(f"[ERROR] Error getting positions summary: {e}")
            return []

    def reset_account(self):
        """Reset account to initial state"""
        try:
            self.current_balance = self.initial_balance
            self.available_margin = self.initial_balance * self.margin_multiplier
            self.used_margin = 0.0
            self.unrealized_pnl = 0.0
            self.realized_pnl = 0.0
            self.trades.clear()
            self.positions.clear()
            self.daily_stats.clear()

            self._save_account_data()
            logger.info("[SUCCESS] Virtual account reset to initial state")

        except Exception as e:
            logger.error(f"[ERROR] Error resetting account: {e}")

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def format_currency(amount: float) -> str:
    """Format currency amount for display"""
    return f"Rs.{amount:,.2f}"

def calculate_returns(initial_value: float, current_value: float) -> float:
    """Calculate return percentage"""
    if initial_value == 0:
        return 0.0
    return ((current_value - initial_value) / initial_value) * 100
