#!/usr/bin/env python3
"""
Debug data loading step by step
"""

import polars as pl
import os
from datetime import datetime

def preprocess_market_data(df):
    """Preprocess market data to ensure correct format"""
    try:
        print(f"Input data: {len(df)} records, columns: {df.columns}")
        
        # Check if we need to combine date and time columns
        if 'timestamp' not in df.columns and 'date' in df.columns and 'time' in df.columns:
            print("Combining date and time columns into timestamp")
            
            # Combine date and time into timestamp
            df = df.with_columns([
                pl.concat_str([
                    pl.col('date'),
                    pl.lit(' '),
                    pl.col('time')
                ]).str.strptime(pl.Datetime, format='%d-%m-%Y %H:%M:%S').alias('timestamp')
            ])
            
            # Drop the original date and time columns
            df = df.drop(['date', 'time'])
            print("Date/time combination completed")
            
        # Ensure timestamp column exists
        if 'timestamp' not in df.columns:
            print("ERROR: No timestamp column found and cannot create one")
            return df
            
        # Ensure required columns exist
        required_columns = ['symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"ERROR: Missing required columns: {missing_columns}")
            return df
            
        # Convert timestamp to string format if it's not already
        if df['timestamp'].dtype != pl.Utf8:
            print("Converting timestamp to string format")
            df = df.with_columns([
                pl.col('timestamp').dt.strftime('%Y-%m-%d %H:%M:%S').alias('timestamp')
            ])
            print("Timestamp conversion completed")

        # Normalize symbol names by removing exchange suffixes (e.g., "-EQ", "-BE")
        print("Normalizing symbol names...")
        df = df.with_columns([
            pl.col('symbol').str.replace(r'-EQ$', '').str.replace(r'-BE$', '').alias('symbol')
        ])
        print("Symbol normalization completed")

        print(f"Data preprocessed successfully: {len(df)} records")
        print(f"Final columns: {df.columns}")
        print(f"Sample data:\n{df.head()}")
        return df
        
    except Exception as e:
        print(f"ERROR: Error preprocessing market data: {e}")
        import traceback
        traceback.print_exc()
        return df

def test_data_preprocessing():
    """Test data preprocessing"""
    print("🔍 Testing Data Preprocessing")
    
    # Load historical data
    historical_5min_path = "data/historical/historical_5min.parquet"
    if os.path.exists(historical_5min_path):
        print(f"Loading historical data from {historical_5min_path}")
        df_historical = pl.read_parquet(historical_5min_path)
        print(f"Loaded: {len(df_historical)} records")
        
        # Preprocess
        df_processed = preprocess_market_data(df_historical)
        
        # Test specific symbols
        test_symbols = ['INFY', 'RELIANCE', 'HDFCBANK']
        for symbol in test_symbols:
            symbol_data = df_processed.filter(pl.col('symbol') == symbol)
            print(f"{symbol}: {len(symbol_data)} records")
            if len(symbol_data) > 0:
                print(f"  Latest timestamp: {symbol_data.select('timestamp').tail(1).item()}")
        
        return df_processed
    else:
        print(f"Historical data file not found: {historical_5min_path}")
        return None

if __name__ == "__main__":
    test_data_preprocessing()
