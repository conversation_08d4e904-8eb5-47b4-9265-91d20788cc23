#!/usr/bin/env python3
"""
🚀 Continuous Trading Launcher
Simple launcher for the continuous live trading system
"""

import subprocess
import sys
import argparse
from pathlib import Path

def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(description="Launch Continuous Trading System")
    parser.add_argument("--mode", choices=["live", "paper", "demo"], 
                       default="paper", help="Trading mode (default: paper)")
    parser.add_argument("--max-trades", type=int, default=5,
                       help="Maximum trades per day (default: 5)")
    
    args = parser.parse_args()
    
    print("🚀 Launching Continuous Trading System...")
    print(f"📊 Mode: {args.mode}")
    print(f"🎯 Max trades: {args.max_trades}")
    
    # Build command
    cmd = [
        sys.executable,
        "run_continuous_live_trading.py",
        "--mode", args.mode,
        "--max-trades", str(args.max_trades)
    ]
    
    try:
        # Run the continuous trading system
        result = subprocess.run(cmd, check=True)
        print("✅ Trading system completed successfully")
        return result.returncode
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Trading system failed with exit code: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("\n🛑 Trading system interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Error launching trading system: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
