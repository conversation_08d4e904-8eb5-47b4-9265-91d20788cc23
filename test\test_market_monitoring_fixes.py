#!/usr/bin/env python3
"""
Test script to verify market monitoring agent fixes
"""

import asyncio
import polars as pl
import os
import sys
from datetime import datetime, timedelta
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_data_sorting_fix():
    """Test the data sorting fix"""
    logger.info("=== Testing Data Sorting Fix ===")
    
    try:
        # Check if live data exists
        live_path = 'data/live/live_5min.parquet'
        if not os.path.exists(live_path):
            logger.error("Live data file not found")
            return False
            
        # Read live data
        df = pl.read_parquet(live_path)
        logger.info(f"Loaded data: {df.shape}")
        
        # Test sorting for a few symbols
        symbols_to_test = df['symbol'].unique()[:5]
        
        for symbol in symbols_to_test:
            symbol_data = df.filter(pl.col('symbol') == symbol)
            
            # Check if sorted
            is_sorted = symbol_data['timestamp'].is_sorted()
            logger.info(f"{symbol}: sorted = {is_sorted}")
            
            if not is_sorted:
                logger.info(f"  Sorting {symbol} data...")
                # Test sorting
                sorted_data = symbol_data.sort('timestamp')
                logger.info(f"  After sorting: {sorted_data['timestamp'].is_sorted()}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing data sorting: {e}")
        return False

def test_timeframe_conversion():
    """Test timeframe conversion with sorted data"""
    logger.info("=== Testing Timeframe Conversion ===")
    
    try:
        # Create sample sorted data
        sample_data = {
            'timestamp': [
                '2025-01-01T09:15:00+05:30',
                '2025-01-01T09:20:00+05:30',
                '2025-01-01T09:25:00+05:30',
                '2025-01-01T09:30:00+05:30',
                '2025-01-01T09:35:00+05:30',
                '2025-01-01T09:40:00+05:30'
            ],
            'open': [100.0, 101.0, 102.0, 103.0, 104.0, 105.0],
            'high': [100.5, 101.5, 102.5, 103.5, 104.5, 105.5],
            'low': [99.5, 100.5, 101.5, 102.5, 103.5, 104.5],
            'close': [101.0, 102.0, 103.0, 104.0, 105.0, 106.0],
            'volume': [1000, 1100, 1200, 1300, 1400, 1500],
            'symbol': ['RELIANCE'] * 6,
            'exchange': ['NSE'] * 6
        }
        
        df_5min = pl.DataFrame(sample_data)
        logger.info(f"Created sample 5min data: {len(df_5min)} records")
        
        # Test conversion to 15min
        logger.info("Testing 15min conversion...")
        
        # Convert timestamp to datetime and sort
        df_sorted = df_5min.with_columns([
            pl.col('timestamp').str.strptime(pl.Datetime, "%Y-%m-%dT%H:%M:%S%z").alias('datetime')
        ]).sort('datetime')
        
        # Test group_by_dynamic
        resampled = df_sorted.group_by_dynamic(
            'datetime',
            every='15m',
            closed='left'
        ).agg([
            pl.col('open').first().alias('open'),
            pl.col('high').max().alias('high'),
            pl.col('low').min().alias('low'),
            pl.col('close').last().alias('close'),
            pl.col('volume').sum().alias('volume'),
            pl.col('symbol').first().alias('symbol'),
            pl.col('exchange').first().alias('exchange')
        ])
        
        logger.info(f"15min conversion successful: {len(resampled)} records")
        logger.info(f"Sample 15min data:\n{resampled}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing timeframe conversion: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_system_status():
    """Check if the system is running and responsive"""
    logger.info("=== Testing System Status ===")
    
    try:
        # Check if logs directory exists
        if os.path.exists('logs'):
            log_files = os.listdir('logs')
            logger.info(f"Log files found: {log_files}")
            
            # Check for recent log activity
            for log_file in log_files:
                if log_file.endswith('.log'):
                    log_path = os.path.join('logs', log_file)
                    stat = os.stat(log_path)
                    mod_time = datetime.fromtimestamp(stat.st_mtime)
                    age = datetime.now() - mod_time
                    logger.info(f"{log_file}: last modified {age.total_seconds():.1f} seconds ago")
        
        # Check if performance metrics exist
        if os.path.exists('logs/performance_metrics.log'):
            logger.info("Performance metrics log found")
            # Read last few lines
            with open('logs/performance_metrics.log', 'r') as f:
                lines = f.readlines()
                if lines:
                    logger.info(f"Last performance metric: {lines[-1].strip()}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error checking system status: {e}")
        return False

async def test_agent_import():
    """Test if we can import and create the agent"""
    logger.info("=== Testing Agent Import ===")
    
    try:
        # Try to import the agent
        sys.path.append('agents')
        from market_monitoring_agent import MarketMonitoringAgent
        
        logger.info("✅ Successfully imported MarketMonitoringAgent")
        
        # Try to create agent instance (without full setup)
        config_path = "config/market_monitoring_config.yaml"
        if os.path.exists(config_path):
            agent = MarketMonitoringAgent(config_path)
            logger.info("✅ Successfully created agent instance")
            return True
        else:
            logger.warning("Config file not found, but import successful")
            return True
            
    except Exception as e:
        logger.error(f"Error importing/creating agent: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all tests"""
    logger.info("🧪 Starting Market Monitoring Agent Tests")
    
    tests = [
        ("Data Sorting Fix", test_data_sorting_fix),
        ("Timeframe Conversion", test_timeframe_conversion),
        ("System Status", test_system_status),
        ("Agent Import", lambda: asyncio.run(test_agent_import()))
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"{test_name}: ❌ FAILED with exception: {e}")
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The fixes should resolve the issues.")
    else:
        logger.warning("⚠️  Some tests failed. Check the logs above for details.")

if __name__ == "__main__":
    main()
