JMK@DESKTOP-8T2MCGK MINGW64 ~/Documents/Equity (master)
$ python run_continuous_live_trading.py --mode paper
[SUCCESS] GPU acceleration available (PyTorch CUDA: True)
   🔥 CUDA device: NVIDIA GeForce RTX 3060 Ti
   💾 CUDA memory: 8.6 GB
2025-07-24 10:04:15,267 - agents.signal_enhancement_models - INFO - [INIT] Using device: cuda
[SUCCESS] GPU acceleration available (PyTorch CUDA: True)
   🔥 CUDA device: NVIDIA GeForce RTX 3060 Ti
   💾 CUDA memory: 8.6 GB
🚀 Starting Continuous Live Trading System...
📝 Paper trading mode - No real money will be used
2025-07-24 10:04:15,283 - __main__ - INFO - 🚀 Continuous Live Trading System initialized in paper mode
2025-07-24 10:04:15,283 - __main__ - INFO - 📊 Max daily trades: 5
2025-07-24 10:04:15,283 - __main__ - INFO - 🆔 Session ID: session_20250724_100415

================================================================================
🚀 CONTINUOUS LIVE TRADING SYSTEM
================================================================================
📊 Mode: PAPER
🎯 Max daily trades: 5
🆔 Session: session_20250724_100415
================================================================================

🤖 Step 1: Initializing AI Trading Agents...

🤖 Initializing AI Trading Agents...
   📊 Initializing Market Monitoring Agent...
2025-07-24 10:04:15,285 - utils.config_loader - INFO - [OK] Loaded environment variables from .env
2025-07-24 10:04:15,300 - utils.config_loader - INFO - [OK] Loaded environment configuration from config/environment_config.yaml
2025-07-24 10:04:15,314 - utils.config_loader - INFO - [SUCCESS] Loaded configuration for market_monitoring_config
[I 250724 10:04:15 smartConnect:124] in pool
   ✅ Market Monitoring Agent ready
   🧠 Initializing Signal Generation Agent...
[I 250724 10:04:16 smartConnect:124] in pool
   ✅ Signal Generation Agent ready
   🛡️ Initializing Risk Management Agent...
   ✅ Risk Management Agent ready
   ⚡ Initializing Execution Agent...
   ✅ Execution Agent ready for paper trading
   🔗 Agent coordination established

🎯 Step 2: Selecting Trading Universe...

🎯 Selecting Trading Universe...
[10:04:17] INFO     Rich logging enabled.                                               run_paper_trading_workflow.py:104╭─ Trading System Initialization ──╮
│ 🚀 Paper Trading Workflow System │
│ Mode: PAPER                      │
│ Started: 2025-07-24 10:04:17     │
│ System: Enhanced Logging Enabled │
╰──────────────────────────────────╯
           INFO     [SUCCESS] Loaded account data: Balance Rs.103,998.18, 6 trades                   paper_trading.py:183           INFO     [MONEY] Virtual Account initialized with balance: Rs.103,998.18                  paper_trading.py:156           INFO     🚀 Enhanced Paper Trading Workflow initialized in paper mode         run_enhanced_paper_trading.py:80           INFO     [SUCCESS] Loaded 500 stocks from existing config                              nse_500_universe.py:105   ✅ Loaded 500 stocks from NSE 500
   📊 Available sectors: 9
   ⭐ Nifty 50 stocks: 50
   🎯 Optimizing for continuous trading (more stocks, relaxed filters)...
   🔍 Analyzing live market data for stock selection...
   📊 Identified 12 candidate stocks for analysis
   📈 Market analysis: neutral trend, normal volatility
   🤖 Screening 12 stocks using ML models...
   🎯 Top signal stocks: TCS, KOTAKBANK, HINDUNILVR, ASIANPAINT, BHARTIARTL
   ✅ 3 stocks passed trading filters
   🎯 Selected 3 stocks across 3 sectors
   ✅ Selected 3 stocks based on live analysis
   📈 Top stocks: TCS, KOTAKBANK, HINDUNILVR...
   ⚠️ Only 3 stocks selected, adding more for continuous trading...
   📊 Found 9 additional Nifty 50 stocks to add
   ✅ Added 7 stocks: ASIANPAINT, BHARTIARTL, HDFCBANK, ICICIBANK, INFY, ITC, LT
   ✅ Enhanced to 10 stocks for better trading opportunities
   ✅ Selected 10 stocks for monitoring
   📈 Top stocks: TCS, KOTAKBANK, HINDUNILVR, ASIANPAINT, BHARTIARTL
   🔗 Starting WebSocket connection...
           INFO     [INIT] Starting Market Monitoring Agent background tasks...           market_monitoring_agent.py:3135           INFO     [HISTORICAL] Ensuring historical data for selected stocks...          market_monitoring_agent.py:3047           WARNING  [HISTORICAL] No selected stocks found, using default stocks           market_monitoring_agent.py:3053           INFO     [DEFAULT] Using default stocks: ['RELIANCE', 'HDFCBANK', 'INFY',      market_monitoring_agent.py:3125                    'TCS', 'ICICIBANK', 'BHARTIARTL']
           INFO     [HISTORICAL] Selected 6 stocks for monitoring: ['RELIANCE',           market_monitoring_agent.py:3056                    'HDFCBANK', 'INFY', 'TCS', 'ICICIBANK', 'BHARTIARTL']
           INFO     [HISTORICAL] Downloading historical data for 6 stocks...              market_monitoring_agent.py:3066           INFO     [DOWNLOAD] Downloading 35 days of historical data for 6 stocks...     market_monitoring_agent.py:3234           INFO     [DOWNLOAD] Downloading data from 2025-06-19 to 2025-07-24             market_monitoring_agent.py:3257           INFO     [DOWNLOAD] Processing RELIANCE...                                     market_monitoring_agent.py:3267           INFO     [WORKFLOW] Starting background monitoring...                                       risk_agent.py:1084           INFO     [WORKFLOW] Starting background monitoring...                                       risk_agent.py:1084           INFO     [SMARTAPI_DATA] Downloaded RELIANCE data range:                        market_monitoring_agent.py:866                    2025-06-19T10:05:00+05:30 to 2025-07-24T10:00:00+05:30 (1875 records)
[10:04:18] INFO     [SUCCESS] Downloaded RELIANCE: 1875 records                           market_monitoring_agent.py:3277[10:04:19] INFO     [DOWNLOAD] Processing HDFCBANK...                                     market_monitoring_agent.py:3267           INFO     [SMARTAPI_DATA] Downloaded HDFCBANK data range:                        market_monitoring_agent.py:866                    2025-06-19T10:05:00+05:30 to 2025-07-24T10:00:00+05:30 (1875 records)
[10:04:20] INFO     [SUCCESS] Downloaded HDFCBANK: 1875 records                           market_monitoring_agent.py:3277[10:04:21] INFO     [DOWNLOAD] Processing INFY...                                         market_monitoring_agent.py:3267           INFO     [SMARTAPI_DATA] Downloaded INFY data range: 2025-06-19T10:05:00+05:30  market_monitoring_agent.py:866                    to 2025-07-24T10:00:00+05:30 (1875 records)
[10:04:22] INFO     [SUCCESS] Downloaded INFY: 1875 records                               market_monitoring_agent.py:3277           INFO     [DOWNLOAD] Processing TCS...                                          market_monitoring_agent.py:3267           INFO     [SMARTAPI_DATA] Downloaded TCS data range: 2025-06-19T10:05:00+05:30   market_monitoring_agent.py:866                    to 2025-07-24T10:00:00+05:30 (1875 records)
[10:04:23] INFO     [SUCCESS] Downloaded TCS: 1875 records                                market_monitoring_agent.py:3277[10:04:24] INFO     [DOWNLOAD] Processing ICICIBANK...                                    market_monitoring_agent.py:3267           INFO     [SMARTAPI_DATA] Downloaded ICICIBANK data range:                       market_monitoring_agent.py:866                    2025-06-19T10:05:00+05:30 to 2025-07-24T10:00:00+05:30 (1875 records)
[10:04:25] INFO     [SUCCESS] Downloaded ICICIBANK: 1875 records                          market_monitoring_agent.py:3277[10:04:26] INFO     [DOWNLOAD] Processing BHARTIARTL...                                   market_monitoring_agent.py:3267           INFO     [SMARTAPI_DATA] Downloaded BHARTIARTL data range:                      market_monitoring_agent.py:866                    2025-06-19T10:05:00+05:30 to 2025-07-24T10:00:00+05:30 (1875 records)
[10:04:27] INFO     [SUCCESS] Downloaded BHARTIARTL: 1875 records                         market_monitoring_agent.py:3277           INFO     [SAVE_LIVE] Combining 6 live dataframes...                             market_monitoring_agent.py:934           INFO     [MERGE] Loading existing live data...                                 market_monitoring_agent.py:1083           INFO     [CLEANUP] Removing data older than 2025-06-19 10:04:27                market_monitoring_agent.py:1087           INFO     [INFO] Latest existing data: 2025-07-22T15:25:00+05:30                market_monitoring_agent.py:1093           INFO     [APPEND] Adding 11250 new records (including potential backfill)      market_monitoring_agent.py:1097           INFO     [RESULT] Final dataset: 15745 records, 26 symbols                     market_monitoring_agent.py:1123           INFO     [DATE_RANGE] Data spans from 2025-06-19T10:05:00+05:30 to             market_monitoring_agent.py:1124                    2025-07-24T10:00:00+05:30
           INFO     [SUCCESS] Live 5min data saved to data\live\live_5min.parquet          market_monitoring_agent.py:963           INFO     [SUMMARY] Records: 15,745, Symbols: 26, Size: 0.30 MB                  market_monitoring_agent.py:964           INFO     [SUCCESS] Downloaded data for 6/6 stocks                              market_monitoring_agent.py:3287           INFO     [DOWNLOAD] Historical data download completed successfully            market_monitoring_agent.py:3240           INFO     [LOAD] Loading historical data for 6 stocks into memory...            market_monitoring_agent.py:3300           INFO     [TIMEFRAMES] Generating higher timeframes from 5min live data...       market_monitoring_agent.py:984           INFO     [TIMEFRAMES] Generating 15min data...                                  market_monitoring_agent.py:995           INFO     [TIMEFRAMES] Generated 15min: 5273 records                            market_monitoring_agent.py:1019           INFO     [TIMEFRAMES] Generating 30min data...                                  market_monitoring_agent.py:995[10:04:28] INFO     [TIMEFRAMES] Generated 30min: 2650 records                            market_monitoring_agent.py:1019           INFO     [TIMEFRAMES] Generating 1hr data...                                    market_monitoring_agent.py:995           INFO     [TIMEFRAMES] Generated 1hr: 1338 records                              market_monitoring_agent.py:1019           INFO     [TIMEFRAMES] All timeframes generated and loaded to memory            market_monitoring_agent.py:1021           WARNING  [LOAD] Missing data for symbols: {'HDFCBANK', 'ICICIBANK',            market_monitoring_agent.py:3311                    'BHARTIARTL', 'INFY', 'TCS', 'RELIANCE'}
           INFO     Updated Enhanced WebSocket with 6 selected symbols: ['RELIANCE',    enhanced_websocket_service.py:185                    'HDFCBANK', 'INFY', 'TCS', 'ICICIBANK', 'BHARTIARTL']
           INFO     [WEBSOCKET] Updated WebSocket service with 6 selected stocks:         market_monitoring_agent.py:3328                    ['RELIANCE', 'HDFCBANK', 'INFY', 'TCS', 'ICICIBANK', 'BHARTIARTL']
           INFO     [CONNECT] Starting Enhanced WebSocket Service...                      market_monitoring_agent.py:3146           INFO     Enhanced WebSocket initialized successfully                         enhanced_websocket_service.py:274           INFO     ✅ Enhanced WebSocket service initialized successfully              enhanced_websocket_service.py:250 
           INFO     ✅ Enhanced WebSocket service started                               enhanced_websocket_service.py:295 
           INFO     [SUCCESS] Enhanced WebSocket Service started successfully             market_monitoring_agent.py:3150           INFO     [SUCCESS] Market Monitoring Agent background tasks started            market_monitoring_agent.py:3182   ✅ WebSocket connection established
           INFO     [INFO] WebSocket not connected, storing symbols for later             market_monitoring_agent.py:1961                    subscription
   📡 WebSocket subscriptions established

🔄 Step 4: Starting Continuous Trading...
📝 Paper trading mode - No real money used
⏰ Current time: 10:04:28
📊 Monitoring 10 stocks
🎯 Ready to execute up to 5 trades

🔄 Starting Continuous Trading Loop...
📊 Mode: PAPER
🎯 Max daily trades: 5
⏰ Trading window: 09:20 - 14:30
           INFO     Starting candle formation loop...                                   enhanced_websocket_service.py:310           INFO     ✅ Enhanced WebSocket connection opened                             enhanced_websocket_service.py:375           INFO     Enhanced WebSocket subscribed to 6 symbols                          enhanced_websocket_service.py:445[10:04:39] INFO     [ENHANCED_TICK] Received 100 ticks. Latest: RELIANCE @ 1415.6         market_monitoring_agent.py:1884[10:04:51] INFO     [ENHANCED_TICK] Received 200 ticks. Latest: INFY @ 1560.0             market_monitoring_agent.py:1884[10:05:00] INFO     [CANDLE_FORMED] RELIANCE 5min: O=1415.70 H=1415.70 L=1415.50        enhanced_websocket_service.py:361                    C=1415.60 V=0 (ticks: 62)
           INFO     [CANDLE_FORMED] HDFCBANK 5min: O=2016.70 H=2016.70 L=2016.50        enhanced_websocket_service.py:361                    C=2016.50 V=0 (ticks: 18)
           INFO     [CANDLE_FORMED] INFY 5min: O=1560.20 H=1560.40 L=1559.60 C=1560.00  enhanced_websocket_service.py:361                    V=0 (ticks: 63)
           INFO     [CANDLE_FORMED] TCS 5min: O=3165.00 H=3165.10 L=3165.00 C=3165.00   enhanced_websocket_service.py:361                    V=0 (ticks: 60)
           INFO     [CANDLE_FORMED] ICICIBANK 5min: O=1482.50 H=1482.50 L=1482.40       enhanced_websocket_service.py:361                    C=1482.40 V=0 (ticks: 18)
           INFO     [CANDLE_FORMED] BHARTIARTL 5min: O=1961.10 H=1964.10 L=1960.80      enhanced_websocket_service.py:361                    C=1964.10 V=0 (ticks: 56)
[10:05:03] INFO     [ENHANCED_TICK] Received 300 ticks. Latest: BHARTIARTL @ 1963.3       market_monitoring_agent.py:1884[10:05:17] INFO     [ENHANCED_TICK] Received 400 ticks. Latest: BHARTIARTL @ 1964.0       market_monitoring_agent.py:1884[10:05:27] INFO     [CANDLE_LOOP] Running... Current time: 10:05:27                     enhanced_websocket_service.py:319[10:05:32] INFO     [ENHANCED_TICK] Received 500 ticks. Latest: BHARTIARTL @ 1965.2       market_monitoring_agent.py:1884[10:05:46] INFO     [ENHANCED_TICK] Received 600 ticks. Latest: TCS @ 3165.0              market_monitoring_agent.py:1884
🛑 Received signal 2. Initiating graceful shutdown...
✅ Continuous trading loop completed

📊 Step 5: Generating Final Report...

================================================================================
📊 TRADING SESSION SUMMARY
================================================================================
🕐 Duration: 1.7 minutes
📈 Signals Generated: 0
✅ Trades Executed: 0
❌ Trades Rejected: 0
📊 Success Rate: 0.0%
🎯 Trades Remaining: 5
================================================================================

🧹 Cleaning up resources...
[10:05:58] INFO     [STOP] Stopping Signal Generation Agent...                            signal_generation_agent.py:1354           INFO     [STATUS] Performance metrics saved to                                 signal_generation_agent.py:1403                    data/performance\signal_performance_20250724.json
           INFO     [SUCCESS] Signal Generation Agent stopped successfully                signal_generation_agent.py:1361           INFO     [WORKFLOW] Shutting down Risk Management Agent...                                  risk_agent.py:1474           INFO     [SUCCESS] Final report generated: reports/daily_risk\risk_report_20250724.json     risk_agent.py:1516           INFO     [SUCCESS] Risk Management Agent shutdown completed                                 risk_agent.py:1484           INFO     [INFO] No trade data to save                                                  execution_agent.py:1016           INFO     [SUCCESS] Execution Agent cleanup completed                                   execution_agent.py:1220           INFO     [STOP] Stopping Market Monitoring Agent...                            market_monitoring_agent.py:3336           INFO     [STOP] Cancelling 4 background tasks...                               market_monitoring_agent.py:3342           INFO     [STOP] Stopping Enhanced WebSocket Service...                         market_monitoring_agent.py:3381           ERROR    Error stopping Enhanced WebSocket service: 'SmartWebSocketV2'       enhanced_websocket_service.py:564                    object has no attribute 'close'
           INFO     [STOP] Enhanced WebSocket Service stopped                             market_monitoring_agent.py:3383           INFO     [STOP] Saving agent state...                                          market_monitoring_agent.py:3416           INFO     [SAVE] Agent state saved                                              market_monitoring_agent.py:3575           INFO     [STOP] Agent state saved                                              market_monitoring_agent.py:3418           INFO     [SUCCESS] Market Monitoring Agent stopped gracefully                  market_monitoring_agent.py:3426💾 Final report saved to: reports\continuous_trading\continuous_trading_paper_20250724_100558.json
✅ Cleanup completed

🎉 Continuous trading session completed successfully!