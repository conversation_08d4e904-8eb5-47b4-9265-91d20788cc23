# Simple Test Strategies for Debugging
strategies:
  # Simple RSI Strategy
  - name: Simple_RSI
    long: rsi_14 < 30 and close > ema_10
    short: rsi_14 > 70 and close < ema_10
    description: "Simple RSI reversal strategy"
    risk_level: "medium"
    market_conditions: ["ranging"]

  # Simple MACD Strategy
  - name: Simple_MACD
    long: macd > macd_signal and close > ema_20
    short: macd < macd_signal and close < ema_20
    description: "Simple MACD crossover strategy"
    risk_level: "medium"
    market_conditions: ["trending"]

  # Simple EMA Strategy
  - name: Simple_EMA
    long: ema_5 > ema_20 and close > ema_5
    short: ema_5 < ema_20 and close < ema_5
    description: "Simple EMA crossover strategy"
    risk_level: "low"
    market_conditions: ["trending"]