strategies:
- ai_tags:
  - early_entry
  - favorable_rr
  - scalping
  - low_iv_entry
  applicable_assets:
  - BANKNIFTY
  backtest: {}
  confidence_scoring:
    metrics:
    - past_accuracy
    - sharpe_ratio
    - drawdown
    min_threshold: 0.7
    type: ensemble
  confidence_type: backtested
  created_at: '2025-07-18T21:41:34.532706'
  description: Captures early breakout with low IV and increasing momentum in BANKNIFTY
  enabled: true
  event_types: []
  evolution: null
  expected_risk_reward: '1:2.5'
  expiry_types:
  - weekly
  - monthly
  holding_period: 15min-60min
  id: strat_001
  last_tested: null
  last_updated: '2025-07-18T21:41:34.532706'
  live_performance: {}
  market_regime:
  - bullish
  - volatile
  max_capital_risk_percent: 2.0
  max_drawdown_percent: 5.0
  min_confidence_threshold: 0.7
  name: Morning Breakout IV Expansion
  risk_model:
    expected_risk_reward: '1:2.5'
    max_capital_risk_percent: 2
    max_drawdown_percent: 5
    stop_loss_trigger: 1.2 * atr_14
  signal_logic:
    long_conditions:
    - close > high.shift(1)
    - volume > volume.rolling(20).mean() * 2
    - rsi_14 > 50
    - rsi_14 < 70
    short_conditions:
    - close < low.shift(1)
    - volume > volume.rolling(20).mean() * 2
    - rsi_14 < 50
    - rsi_14 > 30
  strategy_type:
  - breakout
  - iv_expansion
  timeframes:
  - 5min
  - 15min
  trigger_window: 09:15-10:15
  volatility_regime:
  - low_IV
- ai_tags:
  - mean_reversion
  - vwap_based
  - volume_confirmation
  applicable_assets:
  - NIFTY
  - BANKNIFTY
  backtest: {}
  confidence_scoring:
    metrics:
    - win_rate
    - profit_factor
    min_threshold: 0.65
    type: backtested
  confidence_type: backtested
  created_at: '2025-07-18T21:41:34.532706'
  description: Mean reversion strategy around VWAP with volume confirmation
  enabled: true
  event_types: []
  evolution: null
  expected_risk_reward: '1:2.5'
  expiry_types: []
  holding_period: 30min-120min
  id: strat_002
  last_tested: null
  last_updated: '2025-07-18T21:41:34.532706'
  live_performance: {}
  market_regime:
  - sideways
  - range_bound
  max_capital_risk_percent: 2.0
  max_drawdown_percent: 5.0
  min_confidence_threshold: 0.7
  name: VWAP Mean Reversion
  risk_model:
    expected_risk_reward: '1:2'
    max_capital_risk_percent: 1.5
    max_drawdown_percent: 3
    stop_loss_trigger: 0.8 * atr_14
  signal_logic:
    long_conditions:
    - close < vwap * 0.998
    - rsi_14 < 35
    - volume > volume.rolling(20).mean() * 1.2
    - close > low.rolling(5).min()
    short_conditions:
    - close > vwap * 1.002
    - rsi_14 > 65
    - volume > volume.rolling(20).mean() * 1.2
    - close < high.rolling(5).max()
  strategy_type:
  - mean_reversion
  timeframes:
  - 5min
  - 15min
  - 30min
  trigger_window: 10:15-14:30
  volatility_regime:
  - normal_IV
  - low_IV
- ai_tags:
  - gap_fill
  - event_driven
  - momentum
  applicable_assets:
  - NIFTY
  - BANKNIFTY
  backtest: {}
  confidence_scoring:
    metrics:
    - gap_size
    - volume_ratio
    - momentum
    min_threshold: 0.75
    type: ai_scored
  confidence_type: backtested
  created_at: '2025-07-18T21:41:34.532706'
  description: Trades gap fills with volume and momentum confirmation
  enabled: true
  event_types:
  - gap_up
  - gap_down
  evolution: null
  expected_risk_reward: '1:2.5'
  expiry_types: []
  holding_period: 15min-45min
  id: strat_003
  last_tested: null
  last_updated: '2025-07-18T21:41:34.532706'
  live_performance: {}
  market_regime:
  - bullish
  - bearish
  max_capital_risk_percent: 2.0
  max_drawdown_percent: 5.0
  min_confidence_threshold: 0.7
  name: Gap Fill Strategy
  risk_model:
    expected_risk_reward: '1:1.5'
    max_capital_risk_percent: 2.5
    max_drawdown_percent: 4
    stop_loss_trigger: gap_size * 0.5
  signal_logic:
    long_conditions:
    - gap_down_detected
    - close > open
    - volume > volume.rolling(20).mean() * 1.5
    - rsi_14 > 40
    short_conditions:
    - gap_up_detected
    - close < open
    - volume > volume.rolling(20).mean() * 1.5
    - rsi_14 < 60
  strategy_type:
  - gap_fill
  timeframes:
  - 5min
  - 15min
  trigger_window: 09:15-11:00
  volatility_regime: []
