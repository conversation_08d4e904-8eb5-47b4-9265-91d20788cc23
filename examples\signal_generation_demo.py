#!/usr/bin/env python3
"""
Signal Generation Agent Demo

This script demonstrates how to use the Signal Generation Agent for real-time
trading signal generation with the following features:

1. Setup and configuration
2. Market data simulation
3. Signal generation and validation
4. Integration with other agents
5. Performance monitoring
6. Output handling (notifications, logging, etc.)

Usage:
    python examples/signal_generation_demo.py
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.signal_generation_agent import (
    SignalGenerationAgent,
    MarketIndicators,
    OHLCV,
    MarketRegime
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MarketDataSimulator:
    """Simulate market data for demo purposes"""
    
    def __init__(self):
        self.symbols = ["RELIANCE", "TCS", "INFY", "HDFC", "ICICIBANK"]
        self.current_prices = {symbol: 100.0 + (i * 10) for i, symbol in enumerate(self.symbols)}
        self.base_time = datetime.now().replace(hour=9, minute=20, second=0, microsecond=0)
        
    def generate_ohlcv_data(self, symbol: str, num_candles: int = 100) -> List[OHLCV]:
        """Generate simulated OHLCV data"""
        data = []
        base_price = self.current_prices[symbol]
        
        for i in range(num_candles):
            timestamp = self.base_time + timedelta(minutes=i)
            
            # Simulate price movement with some randomness
            price_change = (i % 10 - 5) * 0.1  # Simple oscillation
            current_price = base_price + price_change + (i * 0.05)  # Slight upward trend
            
            ohlcv = OHLCV(
                symbol=symbol,
                timestamp=timestamp,
                timeframe="1min",
                open=current_price - 0.1,
                high=current_price + 0.3,
                low=current_price - 0.2,
                close=current_price,
                volume=10000 + (i * 100)
            )
            data.append(ohlcv)
        
        # Update current price
        self.current_prices[symbol] = current_price
        return data
    
    def generate_indicators(self, symbol: str) -> MarketIndicators:
        """Generate simulated technical indicators"""
        current_price = self.current_prices[symbol]
        
        return MarketIndicators(
            symbol=symbol,
            timestamp=datetime.now(),
            ema_5=current_price + 0.5,
            ema_10=current_price + 0.3,
            ema_13=current_price + 0.2,
            ema_20=current_price,
            ema_21=current_price - 0.1,
            ema_30=current_price - 0.3,
            ema_50=current_price - 0.5,
            ema_100=current_price - 1.0,
            sma_20=current_price + 0.1,
            rsi_5=65.0,
            rsi_14=58.5,
            macd=0.5,
            macd_signal=0.3,
            macd_histogram=0.2,
            stoch_k=70.0,
            stoch_d=68.0,
            cci=50.0,
            adx=25.0,
            mfi=55.0,
            bb_upper=current_price + 2.0,
            bb_lower=current_price - 2.0,
            bb_middle=current_price,
            atr=1.5,
            vwap=current_price + 0.2,
            supertrend=current_price - 1.0,
            supertrend_direction=1,
            donchian_high=current_price + 2.5,
            donchian_low=current_price - 2.5
        )
    
    def generate_market_regime(self) -> MarketRegime:
        """Generate simulated market regime"""
        return MarketRegime(
            regime='bull',
            confidence=0.8,
            volatility_level='medium',
            trend_strength=0.7,
            market_breadth=65.0,
            correlation_level=0.6,
            timestamp=datetime.now()
        )

async def demo_signal_generation():
    """Main demo function"""
    logger.info("🚀 Starting Signal Generation Agent Demo")
    
    try:
        # Initialize components
        simulator = MarketDataSimulator()
        agent = SignalGenerationAgent()
        
        # Setup agent
        logger.info("🔧 Setting up Signal Generation Agent...")
        await agent.setup()
        
        # Start agent
        logger.info("▶️  Starting Signal Generation Agent...")
        await agent.start()
        
        # Demo signal generation for multiple symbols
        logger.info("📊 Generating signals for demo symbols...")
        
        for symbol in simulator.symbols:
            logger.info(f"📈 Processing {symbol}...")
            
            # Generate market data
            ohlcv_data = simulator.generate_ohlcv_data(symbol)
            indicators = simulator.generate_indicators(symbol)
            market_regime = simulator.generate_market_regime()
            
            # Process market data and generate signals
            signals = await agent.process_market_data(symbol, ohlcv_data, indicators, market_regime)
            
            if signals:
                logger.info(f"✅ Generated {len(signals)} signals for {symbol}")
                for signal in signals:
                    logger.info(f"   📊 {signal.strategy_name}: {signal.action} at ₹{signal.entry_price:.2f}")
            else:
                logger.info(f"ℹ️  No signals generated for {symbol}")
        
        # Display performance metrics
        logger.info("📈 Performance Metrics:")
        metrics = agent.get_performance_metrics()
        logger.info(f"   Signals Generated: {metrics['signals_generated']}")
        logger.info(f"   Signals Validated: {metrics['signals_validated']}")
        logger.info(f"   Signals Rejected: {metrics['signals_rejected']}")
        
        if metrics['execution_time_ms']:
            avg_time = sum(metrics['execution_time_ms']) / len(metrics['execution_time_ms'])
            logger.info(f"   Average Processing Time: {avg_time:.2f}ms")
        
        # Display active signals
        active_signals = agent.get_active_signals()
        if active_signals:
            logger.info(f"📋 Active Signals ({len(active_signals)}):")
            for signal in active_signals:
                logger.info(f"   {signal.symbol} | {signal.strategy_name} | {signal.action} | "
                          f"Confidence: {signal.confidence:.1%} | R:R: {signal.risk_reward_ratio:.1f}")
        else:
            logger.info("📋 No active signals")
        
        # Simulate real-time processing for a few minutes
        logger.info("⏱️  Simulating real-time processing for 2 minutes...")
        
        for minute in range(2):
            await asyncio.sleep(60)  # Wait 1 minute
            
            # Process updated data for one symbol
            symbol = simulator.symbols[minute % len(simulator.symbols)]
            logger.info(f"🔄 Processing updated data for {symbol}...")
            
            ohlcv_data = simulator.generate_ohlcv_data(symbol, 50)  # Shorter history
            indicators = simulator.generate_indicators(symbol)
            market_regime = simulator.generate_market_regime()
            
            signals = await agent.process_market_data(symbol, ohlcv_data, indicators, market_regime)
            
            if signals:
                logger.info(f"🆕 New signals generated for {symbol}: {len(signals)}")
        
        logger.info("✅ Demo completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        raise
    
    finally:
        # Cleanup
        if 'agent' in locals() and agent.is_running:
            logger.info("🛑 Stopping Signal Generation Agent...")
            await agent.stop()

async def demo_signal_validation():
    """Demo signal validation features"""
    logger.info("🔍 Demonstrating Signal Validation Features")
    
    try:
        agent = SignalGenerationAgent()
        await agent.setup()
        
        # Test different validation scenarios
        scenarios = [
            {
                'name': 'High Confidence Signal',
                'confidence': 0.85,
                'time': datetime.now().replace(hour=10, minute=30),
                'expected': True
            },
            {
                'name': 'Low Confidence Signal',
                'confidence': 0.45,
                'time': datetime.now().replace(hour=10, minute=30),
                'expected': False
            },
            {
                'name': 'Outside Market Hours',
                'confidence': 0.75,
                'time': datetime.now().replace(hour=8, minute=30),
                'expected': False
            },
            {
                'name': 'Weekend Signal',
                'confidence': 0.75,
                'time': datetime(2024, 1, 13, 10, 30),  # Saturday
                'expected': False
            }
        ]
        
        for scenario in scenarios:
            logger.info(f"🧪 Testing: {scenario['name']}")
            
            # Create test signal (simplified)
            from agents.signal_generation_agent import TradingSignal
            
            signal = TradingSignal(
                signal_id=f"TEST_{scenario['name'].replace(' ', '_')}",
                symbol="TEST",
                strategy_name="test_strategy",
                signal_type=1,
                action="BUY",
                entry_price=100.0,
                stop_loss=98.0,
                take_profit=104.0,
                quantity=10,
                risk_reward_ratio=2.0,
                confidence=scenario['confidence'],
                market_regime="bull",
                timestamp=scenario['time'],
                capital_allocated=1000.0,
                risk_amount=200.0,
                position_size_method="fixed_fraction",
                liquidity_check=False,
                time_filter_check=False,
                risk_check=False,
                cooldown_check=True,
                context={},
                indicators_snapshot={}
            )
            
            validation_result = await agent._validate_signal(signal)
            
            result_icon = "✅" if validation_result.is_valid == scenario['expected'] else "❌"
            logger.info(f"   {result_icon} Expected: {scenario['expected']}, Got: {validation_result.is_valid}")
            
            if not validation_result.is_valid:
                logger.info(f"   📝 Rejection Reason: {validation_result.rejection_reason}")
        
    except Exception as e:
        logger.error(f"❌ Validation demo failed: {e}")

async def demo_position_sizing():
    """Demo position sizing calculations"""
    logger.info("💰 Demonstrating Position Sizing Features")
    
    try:
        agent = SignalGenerationAgent()
        await agent.setup()
        
        # Test different position sizing scenarios
        scenarios = [
            {'symbol': 'RELIANCE', 'price': 2500.0, 'method': 'fixed_fraction'},
            {'symbol': 'TCS', 'price': 3500.0, 'method': 'kelly'},
            {'symbol': 'INFY', 'price': 1500.0, 'method': 'volatility_scaled'},
        ]
        
        for scenario in scenarios:
            logger.info(f"📊 Testing position sizing for {scenario['symbol']} at ₹{scenario['price']}")
            
            capital_info = {
                'total_capital': 100000,
                'max_position_size_percent': 2.0,
                'max_risk_per_trade_percent': 1.0,
                'intraday_margin_multiplier': 3.5
            }
            
            # Temporarily set method
            original_method = agent.config.position_sizing.get('default_method', 'fixed_fraction')
            agent.config.position_sizing['default_method'] = scenario['method']
            
            result = await agent._calculate_position_sizing(
                scenario['symbol'], 'test_strategy', scenario['price'], capital_info
            )
            
            logger.info(f"   📦 Quantity: {result.quantity}")
            logger.info(f"   💵 Capital Allocated: ₹{result.capital_allocated:,.0f}")
            logger.info(f"   ⚠️  Risk Amount: ₹{result.risk_amount:,.0f}")
            logger.info(f"   🔧 Method Used: {result.method_used}")
            
            # Restore original method
            agent.config.position_sizing['default_method'] = original_method
        
    except Exception as e:
        logger.error(f"❌ Position sizing demo failed: {e}")

async def main():
    """Main demo function"""
    logger.info("🎯 Signal Generation Agent Comprehensive Demo")
    logger.info("=" * 60)
    
    try:
        # Run different demo scenarios
        await demo_signal_generation()
        
        logger.info("\n" + "=" * 60)
        await demo_signal_validation()
        
        logger.info("\n" + "=" * 60)
        await demo_position_sizing()
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 All demos completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Demo suite failed: {e}")
        raise

if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())
