#!/usr/bin/env python3
"""
[INIT] Advanced Signal Enhancement Models
Sophisticated ML models for trading signal enhancement and noise reduction

Features:
- Transformer-based attention mechanisms for signal processing
- Advanced ensemble methods (stacking, blending, voting)
- Signal filtering and noise reduction algorithms
- Dynamic model selection based on market conditions
- Multi-timeframe signal fusion
- Confidence-weighted predictions
"""

import os
import sys
import logging
import warnings
import asyncio
import numpy as np
import pandas as pd
import polars as pl
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from sklearn.ensemble import VotingRegressor, VotingClassifier, StackingRegressor, StackingClassifier
from sklearn.linear_model import Ridge, LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)

# Check for GPU availability
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
logger.info(f"[INIT] Using device: {DEVICE}")

@dataclass
class SignalEnhancementConfig:
    """Configuration for signal enhancement models"""
    
    # Transformer Configuration
    transformer_enabled: bool = True
    attention_heads: int = 8
    transformer_layers: int = 4
    hidden_dim: int = 256
    sequence_length: int = 60
    
    # Ensemble Configuration
    stacking_enabled: bool = True
    blending_enabled: bool = True
    voting_enabled: bool = True
    meta_learner: str = "ridge"  # ridge, logistic, lightgbm
    
    # Signal Processing
    noise_reduction_enabled: bool = True
    signal_smoothing: bool = True
    outlier_detection: bool = True
    confidence_weighting: bool = True
    
    # Multi-timeframe
    multi_timeframe_fusion: bool = True
    timeframes: List[str] = None
    
    # Training Configuration
    batch_size: int = 64
    learning_rate: float = 0.001
    epochs: int = 100
    patience: int = 10
    
    def __post_init__(self):
        if self.timeframes is None:
            self.timeframes = ["1min", "5min", "15min", "1h"]

class TransformerSignalEnhancer(nn.Module):
    """
    Transformer-based signal enhancement model
    Uses attention mechanisms to identify important patterns in trading signals
    """
    
    def __init__(self, config: SignalEnhancementConfig, input_dim: int, output_dim: int = 1):
        super().__init__()
        self.config = config
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # Input projection
        self.input_projection = nn.Linear(input_dim, config.hidden_dim)
        
        # Positional encoding
        self.positional_encoding = self._create_positional_encoding(
            config.sequence_length, config.hidden_dim
        )
        
        # Transformer encoder layers
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=config.hidden_dim,
            nhead=config.attention_heads,
            dim_feedforward=config.hidden_dim * 4,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=config.transformer_layers
        )
        
        # Output layers
        self.output_projection = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(config.hidden_dim // 2, output_dim)
        )
        
        # Confidence estimation head
        self.confidence_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(config.hidden_dim // 4, 1),
            nn.Sigmoid()
        )
    
    def _create_positional_encoding(self, seq_len: int, d_model: int) -> torch.Tensor:
        """Create positional encoding for transformer"""
        pe = torch.zeros(seq_len, d_model)
        position = torch.arange(0, seq_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        return pe.unsqueeze(0)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass
        
        Args:
            x: Input tensor of shape (batch_size, sequence_length, input_dim)
            
        Returns:
            Tuple of (predictions, confidence_scores)
        """
        batch_size, seq_len, _ = x.shape
        
        # Project input to hidden dimension
        x = self.input_projection(x)
        
        # Add positional encoding
        pos_encoding = self.positional_encoding[:, :seq_len, :].to(x.device)
        x = x + pos_encoding
        
        # Apply transformer
        transformer_output = self.transformer(x)
        
        # Use the last token for prediction (or could use mean/max pooling)
        last_hidden = transformer_output[:, -1, :]
        
        # Generate predictions and confidence
        predictions = self.output_projection(last_hidden)
        confidence = self.confidence_head(last_hidden)
        
        return predictions, confidence

class AdvancedEnsembleManager:
    """
    Advanced ensemble manager with multiple ensemble strategies
    """
    
    def __init__(self, config: SignalEnhancementConfig):
        self.config = config
        self.base_models = {}
        self.ensemble_models = {}
        self.model_weights = {}
        
    def add_base_model(self, name: str, model: Any, weight: float = 1.0):
        """Add a base model to the ensemble"""
        self.base_models[name] = model
        self.model_weights[name] = weight
        logger.info(f"[ENSEMBLE] Added base model: {name} (weight: {weight})")
    
    def create_stacking_ensemble(self, X_train: np.ndarray, y_train: np.ndarray,
                                task_type: str = "regression") -> Any:
        """
        Create stacking ensemble
        
        Args:
            X_train: Training features
            y_train: Training targets
            task_type: "regression" or "classification"
            
        Returns:
            Trained stacking ensemble
        """
        try:
            if not self.base_models:
                raise ValueError("No base models available for stacking")
            
            # Prepare base estimators
            estimators = [(name, model) for name, model in self.base_models.items()]
            
            # Choose meta-learner
            if self.config.meta_learner == "ridge":
                meta_learner = Ridge(alpha=1.0)
            elif self.config.meta_learner == "logistic":
                meta_learner = LogisticRegression(max_iter=1000)
            else:
                # Default to Ridge for regression, LogisticRegression for classification
                meta_learner = Ridge(alpha=1.0) if task_type == "regression" else LogisticRegression(max_iter=1000)
            
            # Create stacking ensemble
            if task_type == "regression":
                stacking_model = StackingRegressor(
                    estimators=estimators,
                    final_estimator=meta_learner,
                    cv=5,
                    n_jobs=-1
                )
            else:
                stacking_model = StackingClassifier(
                    estimators=estimators,
                    final_estimator=meta_learner,
                    cv=5,
                    n_jobs=-1
                )
            
            # Train the stacking ensemble
            logger.info("[ENSEMBLE] Training stacking ensemble...")
            stacking_model.fit(X_train, y_train)
            
            self.ensemble_models["stacking"] = stacking_model
            logger.info("[ENSEMBLE] Stacking ensemble trained successfully")
            
            return stacking_model
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to create stacking ensemble: {e}")
            return None
    
    def create_voting_ensemble(self, task_type: str = "regression") -> Any:
        """
        Create voting ensemble
        
        Args:
            task_type: "regression" or "classification"
            
        Returns:
            Voting ensemble
        """
        try:
            if not self.base_models:
                raise ValueError("No base models available for voting")
            
            # Prepare estimators with weights
            estimators = [(name, model) for name, model in self.base_models.items()]
            weights = [self.model_weights.get(name, 1.0) for name, _ in estimators]
            
            # Create voting ensemble
            if task_type == "regression":
                voting_model = VotingRegressor(
                    estimators=estimators,
                    weights=weights,
                    n_jobs=-1
                )
            else:
                voting_model = VotingClassifier(
                    estimators=estimators,
                    weights=weights,
                    voting='soft',  # Use soft voting for probability-based voting
                    n_jobs=-1
                )
            
            self.ensemble_models["voting"] = voting_model
            logger.info("[ENSEMBLE] Voting ensemble created successfully")
            
            return voting_model
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to create voting ensemble: {e}")
            return None
    
    def create_blending_ensemble(self, X_val: np.ndarray, y_val: np.ndarray,
                               task_type: str = "regression") -> Dict[str, float]:
        """
        Create blending ensemble by optimizing weights on validation set
        
        Args:
            X_val: Validation features
            y_val: Validation targets
            task_type: "regression" or "classification"
            
        Returns:
            Optimized blending weights
        """
        try:
            if not self.base_models:
                raise ValueError("No base models available for blending")
            
            # Get predictions from all base models
            predictions = {}
            for name, model in self.base_models.items():
                if hasattr(model, 'predict'):
                    pred = model.predict(X_val)
                    predictions[name] = pred
            
            if not predictions:
                raise ValueError("No valid predictions from base models")
            
            # Optimize blending weights using grid search or optimization
            from scipy.optimize import minimize
            
            def objective(weights):
                weights = weights / np.sum(weights)  # Normalize weights
                blended_pred = np.zeros_like(list(predictions.values())[0])
                
                for i, (name, pred) in enumerate(predictions.items()):
                    blended_pred += weights[i] * pred
                
                if task_type == "regression":
                    return np.mean((blended_pred - y_val) ** 2)  # MSE
                else:
                    # For classification, use negative accuracy
                    return -accuracy_score(y_val, np.round(blended_pred))
            
            # Initial weights (equal)
            n_models = len(predictions)
            initial_weights = np.ones(n_models) / n_models
            
            # Constraints: weights sum to 1 and are non-negative
            constraints = {'type': 'eq', 'fun': lambda w: np.sum(w) - 1}
            bounds = [(0, 1) for _ in range(n_models)]
            
            # Optimize weights
            result = minimize(
                objective, 
                initial_weights, 
                method='SLSQP',
                bounds=bounds,
                constraints=constraints
            )
            
            if result.success:
                optimized_weights = result.x
                weight_dict = {name: weight for name, weight in 
                             zip(predictions.keys(), optimized_weights)}
                
                self.model_weights.update(weight_dict)
                logger.info(f"[ENSEMBLE] Blending weights optimized: {weight_dict}")
                
                return weight_dict
            else:
                logger.warning("[ENSEMBLE] Weight optimization failed, using equal weights")
                return {name: 1.0/n_models for name in predictions.keys()}
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to create blending ensemble: {e}")
            return {}

class SignalProcessor:
    """
    Advanced signal processing for noise reduction and enhancement
    """

    def __init__(self, config: SignalEnhancementConfig):
        self.config = config

    def apply_kalman_filter(self, signals: np.ndarray,
                          process_variance: float = 1e-5,
                          measurement_variance: float = 1e-1) -> np.ndarray:
        """
        Apply Kalman filter for signal smoothing and noise reduction

        Args:
            signals: Input signals array
            process_variance: Process noise variance
            measurement_variance: Measurement noise variance

        Returns:
            Filtered signals
        """
        try:
            from pykalman import KalmanFilter

            # Initialize Kalman filter
            kf = KalmanFilter(
                transition_matrices=np.array([[1]]),
                observation_matrices=np.array([[1]]),
                initial_state_mean=signals[0] if len(signals) > 0 else 0,
                n_dim_state=1
            )

            # Fit and transform
            state_means, _ = kf.em(signals.reshape(-1, 1)).smooth()

            return state_means.flatten()

        except ImportError:
            logger.warning("[SIGNAL] pykalman not available, using simple moving average")
            return self.apply_moving_average_filter(signals)
        except Exception as e:
            logger.error(f"[ERROR] Kalman filter failed: {e}")
            return signals

    def apply_moving_average_filter(self, signals: np.ndarray, window: int = 5) -> np.ndarray:
        """Apply moving average filter for signal smoothing"""
        try:
            import pandas as pd
            return pd.Series(signals).rolling(window=window, center=True).mean().fillna(method='bfill').fillna(method='ffill').values
        except Exception as e:
            logger.error(f"[ERROR] Moving average filter failed: {e}")
            return signals

    def detect_outliers(self, signals: np.ndarray, method: str = "iqr",
                       threshold: float = 1.5) -> np.ndarray:
        """
        Detect outliers in signals

        Args:
            signals: Input signals
            method: "iqr", "zscore", or "isolation"
            threshold: Threshold for outlier detection

        Returns:
            Boolean array indicating outliers
        """
        try:
            if method == "iqr":
                Q1 = np.percentile(signals, 25)
                Q3 = np.percentile(signals, 75)
                IQR = Q3 - Q1
                lower_bound = Q1 - threshold * IQR
                upper_bound = Q3 + threshold * IQR
                return (signals < lower_bound) | (signals > upper_bound)

            elif method == "zscore":
                z_scores = np.abs((signals - np.mean(signals)) / np.std(signals))
                return z_scores > threshold

            elif method == "isolation":
                from sklearn.ensemble import IsolationForest
                iso_forest = IsolationForest(contamination=0.1, random_state=42)
                outliers = iso_forest.fit_predict(signals.reshape(-1, 1))
                return outliers == -1

            else:
                logger.warning(f"[SIGNAL] Unknown outlier detection method: {method}")
                return np.zeros(len(signals), dtype=bool)

        except Exception as e:
            logger.error(f"[ERROR] Outlier detection failed: {e}")
            return np.zeros(len(signals), dtype=bool)

    def remove_outliers(self, signals: np.ndarray, method: str = "iqr") -> np.ndarray:
        """Remove outliers from signals using interpolation"""
        try:
            outliers = self.detect_outliers(signals, method)
            cleaned_signals = signals.copy()

            if np.any(outliers):
                # Use linear interpolation to replace outliers
                import pandas as pd
                series = pd.Series(cleaned_signals)
                series[outliers] = np.nan
                cleaned_signals = series.interpolate(method='linear').fillna(method='bfill').fillna(method='ffill').values

                logger.info(f"[SIGNAL] Removed {np.sum(outliers)} outliers ({np.sum(outliers)/len(signals)*100:.2f}%)")

            return cleaned_signals

        except Exception as e:
            logger.error(f"[ERROR] Outlier removal failed: {e}")
            return signals

    def apply_wavelet_denoising(self, signals: np.ndarray, wavelet: str = 'db4',
                              threshold_mode: str = 'soft') -> np.ndarray:
        """
        Apply wavelet denoising for signal enhancement

        Args:
            signals: Input signals
            wavelet: Wavelet type
            threshold_mode: 'soft' or 'hard'

        Returns:
            Denoised signals
        """
        try:
            import pywt

            # Decompose signal
            coeffs = pywt.wavedec(signals, wavelet, level=4)

            # Estimate noise level using median absolute deviation
            sigma = np.median(np.abs(coeffs[-1])) / 0.6745

            # Calculate threshold
            threshold = sigma * np.sqrt(2 * np.log(len(signals)))

            # Apply thresholding
            coeffs_thresh = list(coeffs)
            coeffs_thresh[1:] = [pywt.threshold(detail, threshold, mode=threshold_mode)
                               for detail in coeffs_thresh[1:]]

            # Reconstruct signal
            denoised = pywt.waverec(coeffs_thresh, wavelet)

            return denoised[:len(signals)]  # Ensure same length

        except ImportError:
            logger.warning("[SIGNAL] PyWavelets not available, using moving average")
            return self.apply_moving_average_filter(signals)
        except Exception as e:
            logger.error(f"[ERROR] Wavelet denoising failed: {e}")
            return signals

    def enhance_signals(self, signals: np.ndarray) -> np.ndarray:
        """
        Apply comprehensive signal enhancement pipeline

        Args:
            signals: Input signals

        Returns:
            Enhanced signals
        """
        try:
            enhanced = signals.copy()

            # Step 1: Remove outliers
            if self.config.outlier_detection:
                enhanced = self.remove_outliers(enhanced)

            # Step 2: Apply noise reduction
            if self.config.noise_reduction_enabled:
                enhanced = self.apply_wavelet_denoising(enhanced)

            # Step 3: Apply smoothing
            if self.config.signal_smoothing:
                enhanced = self.apply_kalman_filter(enhanced)

            logger.info("[SIGNAL] Signal enhancement pipeline completed")
            return enhanced

        except Exception as e:
            logger.error(f"[ERROR] Signal enhancement failed: {e}")
            return signals

class MultiTimeframeSignalFusion:
    """
    Fuse signals from multiple timeframes for enhanced prediction accuracy
    """

    def __init__(self, config: SignalEnhancementConfig):
        self.config = config
        self.timeframe_weights = {}

    def calculate_timeframe_weights(self, performance_metrics: Dict[str, float]) -> Dict[str, float]:
        """
        Calculate optimal weights for different timeframes based on performance

        Args:
            performance_metrics: Dictionary of timeframe -> performance score

        Returns:
            Normalized weights for each timeframe
        """
        try:
            if not performance_metrics:
                # Equal weights if no performance data
                n_timeframes = len(self.config.timeframes)
                return {tf: 1.0/n_timeframes for tf in self.config.timeframes}

            # Use softmax to convert performance scores to weights
            scores = np.array(list(performance_metrics.values()))
            weights = np.exp(scores) / np.sum(np.exp(scores))

            weight_dict = {tf: weight for tf, weight in
                          zip(performance_metrics.keys(), weights)}

            self.timeframe_weights = weight_dict
            logger.info(f"[FUSION] Timeframe weights calculated: {weight_dict}")

            return weight_dict

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate timeframe weights: {e}")
            return {}

    def fuse_signals(self, timeframe_signals: Dict[str, np.ndarray]) -> np.ndarray:
        """
        Fuse signals from multiple timeframes

        Args:
            timeframe_signals: Dictionary of timeframe -> signals array

        Returns:
            Fused signals
        """
        try:
            if not timeframe_signals:
                raise ValueError("No timeframe signals provided")

            # Get weights (use equal weights if not calculated)
            if not self.timeframe_weights:
                self.timeframe_weights = {tf: 1.0/len(timeframe_signals)
                                        for tf in timeframe_signals.keys()}

            # Ensure all signals have the same length
            min_length = min(len(signals) for signals in timeframe_signals.values())

            # Fuse signals using weighted average
            fused_signals = np.zeros(min_length)
            total_weight = 0

            for timeframe, signals in timeframe_signals.items():
                weight = self.timeframe_weights.get(timeframe, 0)
                if weight > 0:
                    fused_signals += weight * signals[:min_length]
                    total_weight += weight

            if total_weight > 0:
                fused_signals /= total_weight

            logger.info(f"[FUSION] Fused signals from {len(timeframe_signals)} timeframes")
            return fused_signals

        except Exception as e:
            logger.error(f"[ERROR] Signal fusion failed: {e}")
            # Return the first available signal as fallback
            return list(timeframe_signals.values())[0] if timeframe_signals else np.array([])

class ConfidenceWeightedPredictor:
    """
    Make predictions with confidence weighting for improved accuracy
    """

    def __init__(self, config: SignalEnhancementConfig):
        self.config = config
        self.confidence_threshold = 0.6

    def predict_with_confidence(self, models: Dict[str, Any],
                              features: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Make predictions with confidence scores

        Args:
            models: Dictionary of model_name -> model
            features: Input features

        Returns:
            Tuple of (predictions, confidence_scores)
        """
        try:
            predictions = []
            confidences = []

            for name, model in models.items():
                if hasattr(model, 'predict'):
                    pred = model.predict(features)

                    # Calculate confidence based on prediction consistency
                    if hasattr(model, 'predict_proba'):
                        proba = model.predict_proba(features)
                        conf = np.max(proba, axis=1)
                    else:
                        # For regression, use prediction stability as confidence
                        conf = np.ones(len(pred)) * 0.7  # Default confidence

                    predictions.append(pred)
                    confidences.append(conf)

            if not predictions:
                return np.array([]), np.array([])

            # Weighted average based on confidence
            predictions = np.array(predictions)
            confidences = np.array(confidences)

            # Normalize confidences
            conf_weights = confidences / np.sum(confidences, axis=0, keepdims=True)

            # Weighted predictions
            weighted_pred = np.sum(predictions * conf_weights, axis=0)
            avg_confidence = np.mean(confidences, axis=0)

            return weighted_pred, avg_confidence

        except Exception as e:
            logger.error(f"[ERROR] Confidence-weighted prediction failed: {e}")
            return np.array([]), np.array([])

    def filter_low_confidence_predictions(self, predictions: np.ndarray,
                                        confidences: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Filter out predictions with low confidence

        Args:
            predictions: Model predictions
            confidences: Confidence scores

        Returns:
            Filtered predictions and confidences
        """
        try:
            high_conf_mask = confidences >= self.confidence_threshold

            filtered_pred = predictions[high_conf_mask]
            filtered_conf = confidences[high_conf_mask]

            logger.info(f"[CONFIDENCE] Filtered {np.sum(~high_conf_mask)} low-confidence predictions")

            return filtered_pred, filtered_conf

        except Exception as e:
            logger.error(f"[ERROR] Confidence filtering failed: {e}")
            return predictions, confidences
