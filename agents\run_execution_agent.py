#!/usr/bin/env python3
"""
Execution Agent Runner
Production runner for the Execution Agent with comprehensive monitoring and management

Features:
[INIT] 1. Production-ready execution agent startup
[STATUS] 2. Real-time monitoring and health checks
⚙️ 3. Graceful shutdown and error recovery
[CONFIG] 4. Configuration management and validation
[METRICS] 5. Performance monitoring and reporting
"""

import os
import sys
import asyncio
import signal
import logging
import argparse
import yaml
from datetime import datetime
from typing import Optional
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from execution_agent import ExecutionAgent, SignalPayload

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/execution_agent_runner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ExecutionAgentRunner:
    """Production runner for Execution Agent"""
    
    def __init__(self, config_path: str = "config/execution_config.yaml"):
        self.config_path = config_path
        self.agent: Optional[ExecutionAgent] = None
        self.running = False
        self.shutdown_event = asyncio.Event()
        
        # Performance tracking
        self.start_time = None
        self.last_health_check = None
        self.health_check_interval = 60  # seconds
        
    async def initialize(self) -> bool:
        """Initialize the execution agent"""
        try:
            logger.info("[INIT] Initializing Execution Agent Runner...")
            
            # Validate configuration
            if not self._validate_config():
                return False
            
            # Initialize execution agent
            self.agent = ExecutionAgent(self.config_path)
            success = await self.agent.initialize()
            
            if not success:
                logger.error("[ERROR] Failed to initialize Execution Agent")
                return False
            
            # Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            self.start_time = datetime.now()
            logger.info("[SUCCESS] Execution Agent Runner initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Error initializing runner: {e}")
            return False
    
    def _validate_config(self) -> bool:
        """Validate configuration file"""
        try:
            if not os.path.exists(self.config_path):
                logger.error(f"[ERROR] Configuration file not found: {self.config_path}")
                return False
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            # Check required sections
            required_sections = ['angel_one_api', 'execution', 'market_hours']
            for section in required_sections:
                if section not in config:
                    logger.error(f"[ERROR] Missing required config section: {section}")
                    return False
            
            # Check Angel One API credentials
            api_config = config.get('angel_one_api', {})
            if not api_config.get('enabled', False):
                logger.warning("[WARN]  Angel One API is disabled in configuration")
            
            required_api_fields = ['api_key', 'username', 'password', 'totp_token']
            for field in required_api_fields:
                if not api_config.get(field):
                    logger.warning(f"[WARN]  Missing Angel One API field: {field}")
            
            logger.info("[SUCCESS] Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Error validating configuration: {e}")
            return False
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"[SIGNAL] Received signal {signum}, initiating graceful shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def health_check(self) -> bool:
        """Perform health check on the execution agent"""
        try:
            if not self.agent:
                return False
            
            # Check if agent is responsive
            summary = await self.agent.get_execution_summary()
            if not summary:
                logger.warning("[WARN]  Health check: Agent not responding")
                return False
            
            # Check for excessive errors
            stats = summary.get('statistics', {})
            total_orders = stats.get('total_orders', 0)
            failed_orders = stats.get('failed_orders', 0)
            
            if total_orders > 0:
                failure_rate = (failed_orders / total_orders) * 100
                if failure_rate > 20:  # More than 20% failure rate
                    logger.warning(f"[WARN]  High failure rate detected: {failure_rate:.2f}%")
                    return False
            
            # Check retry queue size
            retry_queue_size = summary.get('retry_queue_size', 0)
            if retry_queue_size > 10:
                logger.warning(f"[WARN]  Large retry queue detected: {retry_queue_size} items")
            
            self.last_health_check = datetime.now()
            logger.debug("[SUCCESS] Health check passed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Health check failed: {e}")
            return False
    
    async def monitor_performance(self):
        """Monitor and log performance metrics"""
        try:
            if not self.agent:
                return
            
            summary = await self.agent.get_execution_summary()
            stats = summary.get('statistics', {})
            
            # Log performance metrics
            uptime = datetime.now() - self.start_time if self.start_time else None
            
            logger.info("[STATUS] Performance Report:")
            logger.info(f"   Uptime: {uptime}")
            logger.info(f"   Total Orders: {stats.get('total_orders', 0)}")
            logger.info(f"   Successful Orders: {stats.get('successful_orders', 0)}")
            logger.info(f"   Failed Orders: {stats.get('failed_orders', 0)}")
            logger.info(f"   Active Orders: {summary.get('active_orders_count', 0)}")
            logger.info(f"   Avg Execution Time: {stats.get('avg_execution_time_ms', 0):.2f}ms")
            logger.info(f"   Avg Slippage: {stats.get('avg_slippage_percent', 0):.3f}%")
            
        except Exception as e:
            logger.error(f"[ERROR] Error monitoring performance: {e}")
    
    async def run(self):
        """Main execution loop"""
        try:
            self.running = True
            logger.info("[RUNNING] Execution Agent Runner started")
            
            # Start background monitoring tasks
            health_check_task = asyncio.create_task(self._health_check_loop())
            performance_monitor_task = asyncio.create_task(self._performance_monitor_loop())
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
            # Cancel background tasks
            health_check_task.cancel()
            performance_monitor_task.cancel()
            
            try:
                await health_check_task
            except asyncio.CancelledError:
                pass
            
            try:
                await performance_monitor_task
            except asyncio.CancelledError:
                pass
            
            logger.info("[SUCCESS] Execution Agent Runner stopped")
            
        except Exception as e:
            logger.error(f"[ERROR] Error in main execution loop: {e}")
        finally:
            self.running = False
    
    async def _health_check_loop(self):
        """Background health check loop"""
        while self.running:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                if not await self.health_check():
                    logger.error("[ERROR] Health check failed, considering restart...")
                    # Could implement auto-restart logic here
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Error in health check loop: {e}")
    
    async def _performance_monitor_loop(self):
        """Background performance monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(300)  # Every 5 minutes
                await self.monitor_performance()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Error in performance monitor loop: {e}")
    
    async def process_signal_from_queue(self, signal_data: dict):
        """Process signal from external queue/webhook"""
        try:
            # Convert dict to SignalPayload
            signal = SignalPayload(**signal_data)
            
            # Process the signal
            success, message, trade_execution = await self.agent.process_signal(signal)
            
            if success:
                logger.info(f"[SUCCESS] Signal processed: {signal.symbol} - {message}")
                return {"status": "success", "message": message, "order_id": trade_execution.entry_order.order_id if trade_execution.entry_order else None}
            else:
                logger.warning(f"[WARN]  Signal failed: {signal.symbol} - {message}")
                return {"status": "failed", "message": message}
                
        except Exception as e:
            logger.error(f"[ERROR] Error processing signal from queue: {e}")
            return {"status": "error", "message": str(e)}
    
    async def shutdown(self):
        """Graceful shutdown"""
        try:
            logger.info("[STOP] Initiating graceful shutdown...")
            
            # Stop accepting new signals
            self.running = False
            
            # Save final data
            if self.agent:
                await self.agent.save_trade_data()
                await self.agent.cleanup()
            
            # Signal shutdown complete
            self.shutdown_event.set()
            
            logger.info("[SUCCESS] Graceful shutdown completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Error during shutdown: {e}")

async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Execution Agent Runner")
    parser.add_argument(
        "--config", 
        default="config/execution_config.yaml",
        help="Path to configuration file"
    )
    parser.add_argument(
        "--demo", 
        action="store_true",
        help="Run in demo mode with sample signals"
    )
    parser.add_argument(
        "--health-check-only", 
        action="store_true",
        help="Run health check only and exit"
    )
    
    args = parser.parse_args()
    
    # Initialize runner
    runner = ExecutionAgentRunner(args.config)
    
    if not await runner.initialize():
        logger.error("[ERROR] Failed to initialize runner")
        sys.exit(1)
    
    if args.health_check_only:
        # Run health check and exit
        healthy = await runner.health_check()
        if healthy:
            logger.info("[SUCCESS] Health check passed")
            sys.exit(0)
        else:
            logger.error("[ERROR] Health check failed")
            sys.exit(1)
    
    if args.demo:
        # Run demo mode
        logger.info("[DEMO] Running in demo mode...")
        from examples.execution_agent_demo import ExecutionAgentDemo
        demo = ExecutionAgentDemo()
        await demo.run_full_demo()
    else:
        # Run production mode
        logger.info("[PROD] Running in production mode...")
        await runner.run()
    
    await runner.shutdown()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("[EXIT] Execution Agent Runner interrupted by user")
    except Exception as e:
        logger.error(f"[ERROR] Fatal error: {e}")
        sys.exit(1)
