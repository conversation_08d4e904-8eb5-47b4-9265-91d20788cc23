#!/usr/bin/env python3
"""
Working WebSocket Example for Angel One SmartAPI
Based on MarketCalls tutorial with proper token mapping
"""

import os
import sys
import threading
import time
import logging
from datetime import datetime
import pytz
import pyotp

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# SmartAPI imports
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
    SMARTAPI_AVAILABLE = True
except ImportError:
    print("[ERROR] SmartAPI not installed. Install with: pip install smartapi-python")
    SmartConnect = None
    SmartWebSocketV2 = None
    SMARTAPI_AVAILABLE = False

# Local imports
from utils.instrument_master import InstrumentMaster

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WorkingWebSocketExample:
    """Working WebSocket example that successfully receives real-time data"""
    
    def __init__(self):
        # SmartAPI credentials
        self.api_key = os.getenv('SMARTAPI_API_KEY')
        self.username = os.getenv('SMARTAPI_USERNAME')
        self.password = os.getenv('SMARTAPI_PASSWORD')
        self.totp_token = os.getenv('SMARTAPI_TOTP_TOKEN')
        
        # SmartAPI clients
        self.smartapi_client = None
        self.websocket_client = None
        self.auth_token = None
        self.feed_token = None
        
        # Instrument master
        self.instrument_master = InstrumentMaster()
        
        # Data tracking
        self.data_received_count = 0
        self.start_time = None
        
        logger.info("[INIT] Working WebSocket Example initialized")
    
    def authenticate(self) -> bool:
        """Authenticate with SmartAPI"""
        try:
            if not SMARTAPI_AVAILABLE:
                logger.error("[ERROR] SmartAPI library not available")
                return False
            
            if not all([self.api_key, self.username, self.password, self.totp_token]):
                logger.error("[ERROR] Missing authentication credentials")
                return False
            
            logger.info("[AUTH] Starting authentication...")
            
            # Initialize SmartConnect
            self.smartapi_client = SmartConnect(api_key=self.api_key)
            
            # Generate TOTP
            totp = pyotp.TOTP(self.totp_token)
            totp_code = totp.now()
            logger.info(f"[AUTH] Generated TOTP: {totp_code}")
            
            # Attempt login
            data = self.smartapi_client.generateSession(
                clientCode=self.username,
                password=self.password,
                totp=totp_code
            )
            
            if data.get('status'):
                self.auth_token = data['data']['jwtToken']
                self.feed_token = self.smartapi_client.getfeedToken()
                logger.info("[SUCCESS] Authentication successful")
                logger.info(f"[AUTH] JWT Token: {self.auth_token[:20]}...")
                logger.info(f"[AUTH] Feed Token: {self.feed_token}")
                return True
            else:
                error_msg = data.get('message', 'Unknown authentication error')
                logger.error(f"[ERROR] Authentication failed: {error_msg}")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] Authentication process failed: {e}")
            return False
    
    def setup_websocket(self) -> bool:
        """Setup WebSocket client with proper configuration"""
        try:
            if not self.auth_token or not self.feed_token:
                logger.error("[ERROR] Missing authentication tokens")
                return False
            
            logger.info("[SETUP] Setting up WebSocket client...")
            
            # Initialize WebSocket with proper parameters
            self.websocket_client = SmartWebSocketV2(
                self.auth_token,
                self.api_key,
                self.username,
                self.feed_token,
                max_retry_attempt=5
            )
            
            # Set up callbacks
            self.websocket_client.on_open = self.on_websocket_open
            self.websocket_client.on_data = self.on_websocket_data
            self.websocket_client.on_error = self.on_websocket_error
            self.websocket_client.on_close = self.on_websocket_close
            
            logger.info("[SUCCESS] WebSocket client setup complete")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] WebSocket setup failed: {e}")
            return False
    
    def on_websocket_open(self, wsapp):
        """Handle WebSocket connection opened"""
        logger.info("[CONNECT] WebSocket connection opened successfully!")
        
        # Subscribe to test symbols immediately
        self.subscribe_to_test_symbols()
    
    def on_websocket_data(self, wsapp, message):
        """Handle incoming WebSocket data"""
        try:
            self.data_received_count += 1
            
            # Convert timestamp from milliseconds to seconds
            timestamp = message['exchange_timestamp'] / 1000
            utc_time = datetime.utcfromtimestamp(timestamp)
            
            # Convert to IST
            timezone = pytz.timezone('Asia/Kolkata')
            local_time = utc_time.replace(tzinfo=pytz.utc).astimezone(timezone)
            formatted_timestamp = local_time.strftime('%Y-%m-%d %H:%M:%S')
            
            # Get symbol info
            token = str(message['token'])
            symbol_info = self.instrument_master.get_symbol(token)
            symbol_name = symbol_info['symbol'] if symbol_info else f"Token_{token}"
            
            # Format the data
            ltp = message['last_traded_price'] / 100  # Divide by 100 as per Angel One format
            
            formatted_data = (
                f"[DATA] {symbol_name}: ₹{ltp:.2f} | "
                f"Exchange: {message['exchange_type']} | "
                f"Time: {formatted_timestamp} | "
                f"Count: {self.data_received_count}"
            )
            
            logger.info(formatted_data)
            
            # Print summary every 10 messages
            if self.data_received_count % 10 == 0:
                elapsed = time.time() - self.start_time if self.start_time else 0
                rate = self.data_received_count / elapsed if elapsed > 0 else 0
                logger.info(f"[STATS] Received {self.data_received_count} messages in {elapsed:.1f}s (Rate: {rate:.1f} msg/s)")
                
        except Exception as e:
            logger.error(f"[ERROR] Data processing error: {e}")
    
    def on_websocket_error(self, wsapp, error):
        """Handle WebSocket errors"""
        logger.error(f"[ERROR] WebSocket error: {error}")
    
    def on_websocket_close(self, wsapp):
        """Handle WebSocket connection closed"""
        logger.warning("[CONNECT] WebSocket connection closed")
    
    def subscribe_to_test_symbols(self):
        """Subscribe to test symbols using proper token format"""
        try:
            logger.info("[SUBSCRIBE] Subscribing to test symbols...")
            
            # Get tokens for test symbols
            test_symbols = ['RELIANCE', 'TCS', 'INFY', 'HDFCBANK', 'ICICIBANK']
            tokens = []
            
            for symbol in test_symbols:
                token = self.instrument_master.get_token(symbol)
                if token:
                    tokens.append(token)
                    logger.info(f"[TOKEN] {symbol}: {token}")
                else:
                    logger.warning(f"[WARN] Token not found for {symbol}")
            
            if not tokens:
                logger.error("[ERROR] No valid tokens found for subscription")
                return False
            
            # Create subscription data in correct format
            correlation_id = "test_subscription"
            mode = 1  # LTP mode
            token_list = [{
                "exchangeType": 1,  # NSE
                "tokens": tokens
            }]
            
            logger.info(f"[SUBSCRIBE] Subscribing to {len(tokens)} tokens: {tokens}")
            
            # Subscribe using the correct method
            self.websocket_client.subscribe(correlation_id, mode, token_list)
            
            logger.info("[SUCCESS] Subscription request sent")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Subscription failed: {e}")
            return False
    
    def start_websocket_connection(self):
        """Start WebSocket connection in a separate thread"""
        try:
            logger.info("[CONNECT] Starting WebSocket connection...")
            self.start_time = time.time()
            
            # Start connection in separate thread (as per MarketCalls example)
            connection_thread = threading.Thread(target=self.websocket_client.connect, daemon=True)
            connection_thread.start()
            
            logger.info("[SUCCESS] WebSocket connection thread started")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start WebSocket connection: {e}")
            return False
    
    def run_test(self, duration_seconds: int = 60):
        """Run the complete WebSocket test"""
        try:
            print("\n" + "=" * 80)
            print("🚀 WORKING WEBSOCKET TEST")
            print("=" * 80)
            
            # Step 1: Load instrument master
            print("\n📋 Step 1: Loading instrument master...")
            if not self.instrument_master.load_instruments():
                print("❌ Failed to load instrument master")
                return False
            print("✅ Instrument master loaded successfully")
            
            # Step 2: Authenticate
            print("\n🔐 Step 2: Authenticating with SmartAPI...")
            if not self.authenticate():
                print("❌ Authentication failed")
                return False
            print("✅ Authentication successful")
            
            # Step 3: Setup WebSocket
            print("\n📡 Step 3: Setting up WebSocket...")
            if not self.setup_websocket():
                print("❌ WebSocket setup failed")
                return False
            print("✅ WebSocket setup complete")
            
            # Step 4: Start connection
            print("\n🔄 Step 4: Starting WebSocket connection...")
            if not self.start_websocket_connection():
                print("❌ Failed to start WebSocket connection")
                return False
            print("✅ WebSocket connection started")
            
            # Step 5: Wait and monitor
            print(f"\n⏳ Step 5: Monitoring for {duration_seconds} seconds...")
            print("   (You should see real-time market data below)")
            print("-" * 80)
            
            # Wait for the specified duration
            time.sleep(duration_seconds)
            
            # Step 6: Results
            print("\n" + "=" * 80)
            print("📊 TEST RESULTS")
            print("=" * 80)
            
            elapsed = time.time() - self.start_time if self.start_time else 0
            rate = self.data_received_count / elapsed if elapsed > 0 else 0
            
            print(f"✅ Test Duration: {elapsed:.1f} seconds")
            print(f"📈 Messages Received: {self.data_received_count}")
            print(f"⚡ Average Rate: {rate:.1f} messages/second")
            
            if self.data_received_count > 0:
                print("\n🎉 SUCCESS: WebSocket is working properly!")
                print("   Real-time data is being received successfully.")
                return True
            else:
                print("\n⚠️ WARNING: No data received")
                print("   Connection opened but no market data received.")
                print("   This might be normal if market is closed.")
                return False
                
        except KeyboardInterrupt:
            print("\n⏹️ Test interrupted by user")
            return False
        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            logger.error(f"Test error: {e}", exc_info=True)
            return False
        finally:
            # Cleanup
            if self.websocket_client:
                try:
                    self.websocket_client.close_connection()
                    logger.info("[CLEANUP] WebSocket connection closed")
                except:
                    pass

def main():
    """Main test execution"""
    try:
        print("🔍 Working WebSocket Test for Angel One SmartAPI")
        print("This test will verify that WebSocket connection and data reception works properly.")
        print("\nPress Ctrl+C to stop at any time...")
        
        # Create and run test
        test = WorkingWebSocketExample()
        success = test.run_test(duration_seconds=30)  # 30 second test
        
        if success:
            print("\n✅ WebSocket test completed successfully!")
            print("   Your WebSocket connection is working properly.")
        else:
            print("\n⚠️ WebSocket test completed with issues.")
            print("   Check the logs above for details.")
            
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    main()
