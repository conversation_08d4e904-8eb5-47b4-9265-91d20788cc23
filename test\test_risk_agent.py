#!/usr/bin/env python3
"""
Risk Management Agent Tests
Comprehensive test suite for the Risk Management Agent

Test Categories:
- Configuration loading and validation
- Angel One API integration
- Capital allocation and position sizing
- Pre-trade risk filters
- Live trade supervision
- Portfolio tracking and metrics
- Risk alerts and notifications
"""

import os
import sys
import pytest
import asyncio
import yaml
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

# Add parent directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import modules to test
from agents.risk_agent import RiskManagementAgent
from utils.angel_api import AngelOneAPIClient, MarginRequirement, RMSLimits, PositionData, FundData
from utils.risk_models import (
    TradeRequest, ValidationResult, RiskMetrics, Position, Portfolio,
    RiskAlert, DrawdownEvent, PerformanceSnapshot,
    TradeDirection, ProductType, OrderType, RiskLevel, ValidationStatus
)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TEST FIXTURES
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.fixture
def sample_config():
    """Sample configuration for testing"""
    return {
        'angel_one_api': {
            'api_key': 'test_api_key',
            'username': 'test_user',
            'password': 'test_pass',
            'totp_token': 'test_totp',
            'timeout': 10,
            'max_retries': 3
        },
        'capital_allocation': {
            'total_capital': 100000,
            'max_risk_per_trade_percent': 1.0,
            'max_daily_risk_percent': 5.0,
            'max_portfolio_risk_percent': 10.0,
            'position_sizing': {
                'default_method': 'fixed_fraction',
                'fixed_fraction': {
                    'default_percent': 2.0,
                    'max_percent': 5.0
                }
            },
            'intraday_margin': {
                'multiplier': 3.5,
                'margin_buffer_percent': 10.0
            }
        },
        'pre_trade_filters': {
            'concurrent_trades': {
                'max_total_trades': 5,
                'max_trades_per_symbol': 2,
                'max_trades_per_strategy': 3
            },
            'risk_reward': {
                'min_rr_ratio': 1.5,
                'max_stop_loss_percent': 3.0
            },
            'time_filters': {
                'market_hours_only': True,
                'market_open_time': '09:20',
                'market_close_time': '15:00',
                'no_trades_after': '14:30'
            }
        },
        'live_supervision': {
            'drawdown_controls': {
                'max_daily_drawdown_percent': 10.0,
                'max_total_drawdown_percent': 15.0
            },
            'position_monitoring': {
                'check_interval_seconds': 30
            }
        },
        'logging_reporting': {
            'trade_logging': {
                'enable': True,
                'log_level': 'INFO'
            }
        }
    }

@pytest.fixture
def sample_trade_request():
    """Sample trade request for testing"""
    return TradeRequest(
        signal_id="test_signal_001",
        symbol="RELIANCE",
        exchange="NSE",
        strategy_name="test_strategy",
        direction=TradeDirection.LONG,
        entry_price=2500.0,
        stop_loss=2450.0,
        take_profit=2600.0,
        quantity=10,
        product_type=ProductType.MIS,
        order_type=OrderType.LIMIT,
        risk_amount=500.0,
        capital_allocated=25000.0,
        risk_reward_ratio=2.0,
        confidence=0.8,
        timestamp=datetime.now()
    )

@pytest.fixture
def mock_angel_api():
    """Mock Angel One API client"""
    mock_api = Mock(spec=AngelOneAPIClient)
    mock_api.authenticate = AsyncMock(return_value=True)
    mock_api.get_positions = AsyncMock(return_value=[])
    mock_api.get_funds = AsyncMock(return_value=FundData(
        available_cash=100000.0,
        utilized_margin=0.0,
        available_margin=100000.0,
        collateral_value=0.0,
        m2m_unrealized=0.0,
        m2m_realized=0.0,
        opening_balance=100000.0,
        payin=0.0,
        payout=0.0,
        timestamp=datetime.now()
    ))
    mock_api.validate_trade_margin = AsyncMock(return_value=(True, "Valid", MarginRequirement(
        symbol="RELIANCE",
        exchange="NSE",
        quantity=10,
        price=2500.0,
        product_type="MIS",
        transaction_type="BUY",
        margin_required=7500.0,
        available_margin=100000.0,
        is_allowed=True,
        limit_used_percent=7.5
    )))
    return mock_api

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 CONFIGURATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestRiskAgentConfiguration:
    """Test configuration loading and validation"""
    
    def test_config_loading(self, sample_config, tmp_path):
        """Test configuration loading from YAML file"""
        # Create temporary config file
        config_file = tmp_path / "test_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)
        
        # Test loading
        with patch('agents.risk_agent.RiskManagementAgent._setup_logging'):
            with patch('utils.angel_api.AngelOneAPIClient'):
                agent = RiskManagementAgent(str(config_file))
                assert agent.config == sample_config
                assert agent.capital_config == sample_config['capital_allocation']
    
    def test_default_config_values(self, tmp_path):
        """Test default configuration values"""
        # Create minimal config
        minimal_config = {
            'angel_one_api': {
                'api_key': 'test',
                'username': 'test',
                'password': 'test',
                'totp_token': 'test'
            }
        }
        
        config_file = tmp_path / "minimal_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(minimal_config, f)
        
        with patch('agents.risk_agent.RiskManagementAgent._setup_logging'):
            with patch('utils.angel_api.AngelOneAPIClient'):
                agent = RiskManagementAgent(str(config_file))
                
                # Check default values are applied
                assert agent.capital_config.get('total_capital', 100000) == 100000
                assert agent.pre_trade_config.get('concurrent_trades', {}).get('max_total_trades', 5) == 5

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 POSITION SIZING TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestPositionSizing:
    """Test position sizing calculations"""
    
    @pytest.mark.asyncio
    async def test_fixed_fraction_sizing(self, sample_config, sample_trade_request):
        """Test fixed fraction position sizing"""
        with patch('agents.risk_agent.RiskManagementAgent._setup_logging'):
            with patch('utils.angel_api.AngelOneAPIClient'):
                agent = RiskManagementAgent()
                agent.config = sample_config
                agent.capital_config = sample_config['capital_allocation']
                
                # Test calculation
                quantity, capital, method = agent.calculate_position_size(sample_trade_request, 100000.0)
                
                assert method == "fixed_fraction"
                assert quantity > 0
                assert capital > 0
                assert capital <= 100000.0 * 0.05  # Max 5% position size
    
    @pytest.mark.asyncio
    async def test_risk_based_sizing(self, sample_config, sample_trade_request):
        """Test risk-based position sizing"""
        with patch('agents.risk_agent.RiskManagementAgent._setup_logging'):
            with patch('utils.angel_api.AngelOneAPIClient'):
                agent = RiskManagementAgent()
                agent.config = sample_config
                agent.capital_config = sample_config['capital_allocation']
                
                # Test risk-based calculation
                entry_price = 2500.0
                stop_loss = 2450.0
                available_capital = 100000.0
                risk_percent = 1.0
                
                quantity, capital, method = agent._calculate_risk_based_size(
                    entry_price, stop_loss, available_capital, risk_percent
                )
                
                assert method == "risk_based"
                assert quantity > 0
                
                # Verify risk amount is approximately 1% of capital
                risk_amount = (entry_price - stop_loss) * quantity
                expected_risk = available_capital * 0.01
                assert abs(risk_amount - expected_risk) < 100  # Allow small variance

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TRADE VALIDATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestTradeValidation:
    """Test trade validation logic"""
    
    @pytest.mark.asyncio
    async def test_valid_trade_validation(self, sample_config, sample_trade_request, mock_angel_api):
        """Test validation of a valid trade"""
        with patch('agents.risk_agent.RiskManagementAgent._setup_logging'):
            agent = RiskManagementAgent()
            agent.config = sample_config
            agent.capital_config = sample_config['capital_allocation']
            agent.pre_trade_config = sample_config['pre_trade_filters']
            agent.angel_api = mock_angel_api
            agent.portfolio = Portfolio(
                portfolio_id="test",
                name="test",
                total_capital=100000.0
            )
            agent.active_positions = {}
            agent.daily_risk_amount = 0.0
            agent.current_drawdown = 0.0
            
            # Test validation
            result = await agent.validate_trade(sample_trade_request)
            
            assert result.is_valid == True
            assert result.validation_status == ValidationStatus.PASSED
            assert len(result.failed_checks) == 0
    
    @pytest.mark.asyncio
    async def test_concurrent_trades_limit(self, sample_config, sample_trade_request, mock_angel_api):
        """Test concurrent trades limit validation"""
        with patch('agents.risk_agent.RiskManagementAgent._setup_logging'):
            agent = RiskManagementAgent()
            agent.config = sample_config
            agent.pre_trade_config = sample_config['pre_trade_filters']
            agent.angel_api = mock_angel_api
            agent.portfolio = Portfolio(
                portfolio_id="test",
                name="test",
                total_capital=100000.0
            )
            
            # Create maximum number of active positions
            max_trades = sample_config['pre_trade_filters']['concurrent_trades']['max_total_trades']
            agent.active_positions = {
                f"pos_{i}": Position(
                    position_id=f"pos_{i}",
                    symbol=f"STOCK{i}",
                    exchange="NSE",
                    strategy_name="test",
                    direction=TradeDirection.LONG,
                    quantity=10,
                    entry_price=1000.0,
                    current_price=1000.0,
                    stop_loss=950.0,
                    take_profit=1100.0,
                    market_value=10000.0,
                    pnl=0.0,
                    pnl_percent=0.0,
                    unrealized_pnl=0.0,
                    realized_pnl=0.0,
                    risk_amount=500.0,
                    capital_allocated=10000.0,
                    margin_required=3000.0,
                    is_open=True,
                    entry_time=datetime.now(),
                    last_update_time=datetime.now()
                ) for i in range(max_trades)
            }
            
            # Test validation should fail
            result = await agent.validate_trade(sample_trade_request)
            
            assert result.is_valid == False
            assert "Maximum concurrent trades exceeded" in result.rejection_reason
    
    @pytest.mark.asyncio
    async def test_risk_reward_ratio_validation(self, sample_config, mock_angel_api):
        """Test risk-reward ratio validation"""
        with patch('agents.risk_agent.RiskManagementAgent._setup_logging'):
            agent = RiskManagementAgent()
            agent.config = sample_config
            agent.pre_trade_config = sample_config['pre_trade_filters']
            agent.angel_api = mock_angel_api
            agent.portfolio = Portfolio(
                portfolio_id="test",
                name="test",
                total_capital=100000.0
            )
            agent.active_positions = {}
            
            # Create trade with poor risk-reward ratio
            poor_rr_trade = TradeRequest(
                signal_id="test_signal_002",
                symbol="RELIANCE",
                exchange="NSE",
                strategy_name="test_strategy",
                direction=TradeDirection.LONG,
                entry_price=2500.0,
                stop_loss=2450.0,
                take_profit=2520.0,  # Poor RR ratio: 20/50 = 0.4
                quantity=10,
                product_type=ProductType.MIS,
                order_type=OrderType.LIMIT,
                risk_amount=500.0,
                capital_allocated=25000.0,
                risk_reward_ratio=0.4,
                confidence=0.8,
                timestamp=datetime.now()
            )
            
            # Test validation should fail
            result = await agent.validate_trade(poor_rr_trade)
            
            assert result.is_valid == False
            assert "Risk-reward ratio too low" in result.rejection_reason

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 PORTFOLIO MANAGEMENT TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestPortfolioManagement:
    """Test portfolio tracking and management"""
    
    @pytest.mark.asyncio
    async def test_add_position(self, sample_config, sample_trade_request):
        """Test adding a position to portfolio"""
        with patch('agents.risk_agent.RiskManagementAgent._setup_logging'):
            with patch('utils.angel_api.AngelOneAPIClient'):
                agent = RiskManagementAgent()
                agent.config = sample_config
                agent.portfolio = Portfolio(
                    portfolio_id="test",
                    name="test",
                    total_capital=100000.0
                )
                agent.active_positions = {}
                agent.daily_trades_count = 0
                agent.daily_risk_amount = 0.0
                
                # Add position
                position_id = await agent.add_position(sample_trade_request, 2500.0)
                
                assert position_id is not None
                assert position_id in agent.active_positions
                assert len(agent.portfolio.positions) == 1
                assert agent.daily_trades_count == 1
                assert agent.daily_risk_amount == sample_trade_request.risk_amount
    
    @pytest.mark.asyncio
    async def test_close_position(self, sample_config, sample_trade_request):
        """Test closing a position"""
        with patch('agents.risk_agent.RiskManagementAgent._setup_logging'):
            with patch('utils.angel_api.AngelOneAPIClient'):
                agent = RiskManagementAgent()
                agent.config = sample_config
                agent.portfolio = Portfolio(
                    portfolio_id="test",
                    name="test",
                    total_capital=100000.0
                )
                agent.active_positions = {}
                
                # Add position first
                position_id = await agent.add_position(sample_trade_request, 2500.0)
                
                # Close position with profit
                exit_price = 2600.0
                success = await agent.close_position(position_id, exit_price, "take_profit")
                
                assert success == True
                assert position_id not in agent.active_positions
                assert len(agent.portfolio.closed_positions) == 1
                
                # Check PnL calculation
                closed_position = agent.portfolio.closed_positions[0]
                expected_pnl = (exit_price - 2500.0) * sample_trade_request.quantity
                assert closed_position.pnl == expected_pnl
                assert closed_position.pnl > 0  # Profitable trade

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 INTEGRATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestIntegration:
    """Test integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_full_trade_lifecycle(self, sample_config, mock_angel_api):
        """Test complete trade lifecycle from validation to closure"""
        with patch('agents.risk_agent.RiskManagementAgent._setup_logging'):
            agent = RiskManagementAgent()
            agent.config = sample_config
            agent.capital_config = sample_config['capital_allocation']
            agent.pre_trade_config = sample_config['pre_trade_filters']
            agent.angel_api = mock_angel_api
            agent.portfolio = Portfolio(
                portfolio_id="test",
                name="test",
                total_capital=100000.0
            )
            agent.active_positions = {}
            agent.daily_risk_amount = 0.0
            agent.current_drawdown = 0.0
            
            # Create trade request
            trade_request = TradeRequest(
                signal_id="integration_test_001",
                symbol="RELIANCE",
                exchange="NSE",
                strategy_name="integration_test",
                direction=TradeDirection.LONG,
                entry_price=2500.0,
                stop_loss=2450.0,
                take_profit=2600.0,
                quantity=10,
                product_type=ProductType.MIS,
                order_type=OrderType.LIMIT,
                risk_amount=500.0,
                capital_allocated=25000.0,
                risk_reward_ratio=2.0,
                confidence=0.8,
                timestamp=datetime.now()
            )
            
            # 1. Validate trade
            validation_result = await agent.validate_trade(trade_request)
            assert validation_result.is_valid == True
            
            # 2. Add position (simulate execution)
            position_id = await agent.add_position(trade_request, 2500.0)
            assert position_id is not None
            
            # 3. Update position (simulate price movement)
            position = agent.active_positions[position_id]
            position.current_price = 2550.0
            position.unrealized_pnl = (2550.0 - 2500.0) * 10  # 500 profit
            
            # 4. Close position
            success = await agent.close_position(position_id, 2580.0, "partial_profit")
            assert success == True
            
            # 5. Verify final state
            assert len(agent.active_positions) == 0
            assert len(agent.portfolio.closed_positions) == 1
            assert agent.portfolio.total_trades == 1
            assert agent.portfolio.winning_trades == 1
            assert agent.portfolio.realized_pnl > 0

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
