#!/usr/bin/env python3
"""
[INIT] Enhanced AI Training Agent Runner
Orchestrates the complete multi-task learning pipeline
"""

import asyncio
import argparse
import logging
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import yaml
import json

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from agents.ai_training_agent import AITrainingAgent, AITrainingConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/enhanced_ai_training.log')
    ]
)
logger = logging.getLogger(__name__)

class EnhancedAITrainingRunner:
    """Runner for Enhanced AI Training Agent"""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize runner with configuration"""
        self.config_file = config_file or "config/ai_training_config.yaml"
        self.config = self._load_config()
        self.agent = AITrainingAgent(self.config)
        
        logger.info("🧠 Enhanced AI Training Runner initialized")
    
    def _load_config(self) -> AITrainingConfig:
        """Load configuration from YAML file"""
        try:
            config_path = Path(self.config_file)
            
            if not config_path.exists():
                logger.warning(f"[WARN] Config file not found: {config_path}, using defaults")
                return AITrainingConfig()
            
            with open(config_path, 'r') as f:
                yaml_config = yaml.safe_load(f)
            
            # Create config object with YAML values
            config = AITrainingConfig()
            
            # Update config with YAML values
            if 'data' in yaml_config:
                data_config = yaml_config['data']
                config.data_dir = data_config.get('data_dir', config.data_dir)
                config.input_file = data_config.get('input_file', config.input_file)
                config.models_dir = data_config.get('models_dir', config.models_dir)
                config.registry_dir = data_config.get('registry_dir', config.registry_dir)
            
            if 'multi_task_objectives' in yaml_config:
                config.multi_task_objectives = yaml_config['multi_task_objectives']
            
            if 'features' in yaml_config:
                features = yaml_config['features']
                config.feature_columns = features.get('base_features', config.feature_columns)
                config.technical_indicators = features.get('technical_indicators', config.technical_indicators)
                config.option_features = features.get('option_features', config.option_features)
                config.time_features = features.get('time_features', config.time_features)
                config.regime_features = features.get('regime_features', config.regime_features)
                config.strategy_features = features.get('strategy_features', config.strategy_features)
            
            if 'models' in yaml_config:
                models = yaml_config['models']
                config.enabled_models = models.get('enabled_models', config.enabled_models)
                config.ensemble_weights = models.get('ensemble_weights', config.ensemble_weights)
                config.model_architectures = models.get('architectures', config.model_architectures)
            
            if 'training' in yaml_config:
                training = yaml_config['training']
                config.test_size = training.get('test_size', config.test_size)
                config.validation_size = training.get('validation_size', config.validation_size)
                config.cv_folds = training.get('cv_folds', config.cv_folds)
                config.cv_strategy = training.get('cv_strategy', config.cv_strategy)
                config.use_gpu = training.get('use_gpu', config.use_gpu)
                config.n_jobs = training.get('n_jobs', config.n_jobs)
            
            if 'hyperparameter_tuning' in yaml_config:
                tuning = yaml_config['hyperparameter_tuning']
                config.optuna_trials = tuning.get('optuna_trials', config.optuna_trials)
                config.optuna_timeout = tuning.get('optuna_timeout', config.optuna_timeout)
            
            if 'explainability' in yaml_config:
                explainability = yaml_config['explainability']
                config.shap_enabled = explainability.get('shap_enabled', config.shap_enabled)
                config.lime_enabled = explainability.get('lime_enabled', config.lime_enabled)
            
            if 'model_registry' in yaml_config:
                registry = yaml_config['model_registry']
                config.model_versioning = registry.get('versioning_enabled', config.model_versioning)
                config.auto_model_backup = registry.get('auto_backup', config.auto_model_backup)
                config.max_model_versions = registry.get('max_versions', config.max_model_versions)
            
            if 'llm_integration' in yaml_config:
                llm = yaml_config['llm_integration']
                config.llm_insights_enabled = llm.get('insights_enabled', config.llm_insights_enabled)
                config.generate_model_summaries = llm.get('generate_summaries', config.generate_model_summaries)
            
            logger.info(f"[SUCCESS] Configuration loaded from: {config_path}")
            return config
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load config: {e}")
            logger.info("[WORKFLOW] Using default configuration")
            return AITrainingConfig()
    
    async def run_training(self, data_file: Optional[str] = None, 
                          save_results: bool = True) -> Dict[str, Any]:
        """Run the complete training pipeline"""
        
        logger.info("[INIT] Starting Enhanced AI Training Pipeline...")
        
        try:
            # Run enhanced training
            results = await self.agent.train_enhanced_models(data_file)
            
            if results.get("status") == "success":
                logger.info("🎉 Training completed successfully!")
                
                # Print summary
                self._print_training_summary(results)
                
                # Save results if requested
                if save_results:
                    await self._save_results(results)
                
            else:
                logger.error(f"[ERROR] Training failed: {results.get('error', 'Unknown error')}")
            
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Training pipeline failed: {e}")
            return {"status": "error", "error": str(e)}
    
    def _print_training_summary(self, results: Dict[str, Any]):
        """Print training summary"""
        
        print("\n" + "="*80)
        print("🧠 ENHANCED AI TRAINING RESULTS SUMMARY")
        print("="*80)
        
        system_info = results.get("system_info", {})
        print(f"[STATUS] Tasks Trained: {system_info.get('tasks_trained', 0)}")
        print(f"[AGENT] Models per Task: {system_info.get('models_per_task', 0)}")
        print(f"[FAST] GPU Enabled: {system_info.get('gpu_enabled', False)}")
        print(f"[WORKFLOW] CV Strategy: {system_info.get('cv_strategy', 'Unknown')}")
        print(f"[TARGET] Ensemble Enabled: {system_info.get('ensemble_enabled', False)}")
        print(f"🧠 Meta-Learning: {system_info.get('meta_learning_enabled', False)}")
        
        # Print LLM insights if available
        llm_insights = results.get("llm_insights", {})
        if "system_summary" in llm_insights:
            print("\n[LIST] SYSTEM SUMMARY:")
            print(llm_insights["system_summary"])
        
        # Print feature insights
        if "feature_insights" in llm_insights:
            print("\n[DEBUG] FEATURE INSIGHTS:")
            print(llm_insights["feature_insights"])
        
        # Print model registry info
        registry_entries = results.get("model_registry", {})
        if registry_entries:
            print(f"\n💾 MODEL REGISTRY:")
            for task, version_id in registry_entries.items():
                print(f"  • {task}: {version_id}")
        
        print("="*80)
    
    async def _save_results(self, results: Dict[str, Any]):
        """Save training results to file"""
        
        try:
            # Create results directory
            results_dir = Path("data/training_results")
            results_dir.mkdir(parents=True, exist_ok=True)
            
            # Save results with timestamp
            timestamp = results.get("timestamp", "unknown").replace(":", "-")
            results_file = results_dir / f"training_results_{timestamp}.json"
            
            # Convert results to JSON-serializable format
            json_results = self._convert_to_json_serializable(results)
            
            with open(results_file, 'w') as f:
                json.dump(json_results, f, indent=2, default=str)
            
            logger.info(f"💾 Results saved to: {results_file}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save results: {e}")
    
    def _convert_to_json_serializable(self, obj):
        """Convert object to JSON-serializable format"""
        
        if isinstance(obj, dict):
            return {k: self._convert_to_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            return str(obj)
        else:
            return obj
    
    async def run_prediction_demo(self, features: Dict[str, Any]):
        """Run prediction demonstration"""
        
        logger.info("🔮 Running prediction demonstration...")
        
        try:
            predictions = await self.agent.predict_live(features)
            
            print("\n" + "="*60)
            print("🔮 LIVE PREDICTION RESULTS")
            print("="*60)
            
            for task_name, prediction in predictions.items():
                if "error" not in prediction:
                    print(f"\n[TARGET] Task: {task_name}")
                    
                    if "ensemble" in prediction and prediction["ensemble"]:
                        ensemble_pred = prediction["ensemble"]
                        print(f"  [STATUS] Prediction: {ensemble_pred.get('prediction', 'N/A')}")
                        print(f"  [TARGET] Confidence: {ensemble_pred.get('confidence', 'N/A'):.3f}")
                    
                    print(f"  [AGENT] Models: {len(prediction.get('individual_models', {}))}")
                else:
                    print(f"\n[ERROR] {task_name}: {prediction['error']}")
            
            print("="*60)
            
            return predictions
            
        except Exception as e:
            logger.error(f"[ERROR] Prediction demo failed: {e}")
            return {"error": str(e)}

async def main():
    """Main function"""
    
    parser = argparse.ArgumentParser(description="Enhanced AI Training Agent Runner")
    parser.add_argument("--config", type=str, help="Configuration file path")
    parser.add_argument("--data", type=str, help="Training data file path")
    parser.add_argument("--demo", action="store_true", help="Run prediction demo after training")
    parser.add_argument("--no-save", action="store_true", help="Don't save training results")
    
    args = parser.parse_args()
    
    # Initialize runner
    runner = EnhancedAITrainingRunner(args.config)
    
    # Run training
    results = await runner.run_training(
        data_file=args.data,
        save_results=not args.no_save
    )
    
    # Run demo if requested
    if args.demo and results.get("status") == "success":
        # Sample features for demo
        demo_features = {
            'sma_20': 150.0,
            'rsi_14': 65.0,
            'delta': 0.5,
            'gamma': 0.05,
            'theta': -0.02,
            'vega': 0.3,
            'implied_volatility': 0.25,
            'hour_of_day': 10,
            'days_to_expiry': 7,
            'n_trades': 50,
            'volatility': 0.2,
            'liquidity': 0.8
        }
        
        await runner.run_prediction_demo(demo_features)

if __name__ == "__main__":
    asyncio.run(main())
