import requests
import polars as pl
import pandas as pd
from datetime import datetime, timedelta
import os
import time
import random

# =========================================
# CONFIG
TIMEFRAME = "minute"
TKN = "B5Jv8IlDI9RuRxy11xwQGfXdX+d2KACBnRlo8irOuQlgVxQKq5xEjQKoiBILGNdtrIkm0gTyluLq68WRdwBw3BKRxBMPigYWcjfwrJAT5NmXLSYcqYitOw=="

HEADERS = {
    "Authorization": f"enctoken {TKN}"
}

BASE_URL = "https://kite.zerodha.com/oms/instruments/historical/{token}/{timeframe}"

# Retry and sleep configuration
MAX_RETRIES = 3
SLEEP_MIN = 0.07
SLEEP_MAX = 0.1

# =========================================
# CREATE DIRECTORY IF IT DOESN'T EXIST
os.makedirs("../data/historical", exist_ok=True)

# =========================================
# LOAD FILES
fno_list = pd.read_csv(r"C:\Users\<USER>\Documents\Equity\data\lists\fno_list.csv")
instruments = pd.read_csv(r"C:\Users\<USER>\Documents\Equity\data\lists\instruments.csv")

# Ensure consistent casing
instruments['tradingsymbol'] = instruments['tradingsymbol'].str.strip().str.upper()
fno_list['SYMBOL'] = fno_list['SYMBOL'].str.strip().str.upper()

# Filter NSE Equity instruments
instruments = instruments[(instruments['exchange'] == 'NSE') & (instruments['instrument_type'] == 'EQ')]

# =========================================
# FUNCTION TO RESAMPLE 1MIN DATA TO OTHER TIMEFRAMES

def resample_to_timeframes(df_1min, symbol):
    """Convert 1-minute data to 3min, 5min, 15min timeframes"""
    
    # Convert timestamp to datetime if it's not already
    df_resampled = df_1min.with_columns([
        pl.col("timestamp").str.to_datetime("%Y-%m-%dT%H:%M:%S%z").alias("datetime")
    ])
    
    timeframes = {
        "3min": "3m",
        "5min": "5m", 
        "15min": "15m"
    }
    
    for tf_name, tf_code in timeframes.items():
        try:
            # Group by time intervals and aggregate OHLCV data (excluding OI)
            resampled = df_resampled.group_by_dynamic(
                "datetime", 
                every=tf_code,
                closed="left"
            ).agg([
                pl.col("open").first().alias("open"),
                pl.col("high").max().alias("high"),
                pl.col("low").min().alias("low"),
                pl.col("close").last().alias("close"),
                pl.col("volume").sum().alias("volume")
            ]).sort("datetime")
            
            # Convert datetime back to timestamp string format
            resampled = resampled.with_columns([
                pl.col("datetime").dt.strftime("%Y-%m-%dT%H:%M:%S+05:30").alias("timestamp")
            ]).drop("datetime")
            
            # Reorder columns to match original format (without OI)
            resampled = resampled.select(["timestamp", "open", "high", "low", "close", "volume"])
            
            # Save the resampled data
            save_path = f"../data/historical/{symbol}_{tf_name}.parquet"
            resampled.write_parquet(save_path, compression="brotli")
            print(f"[{symbol}] Created {tf_name} data: {len(resampled)} records -> {save_path}")
            
        except Exception as e:
            print(f"[{symbol}] Error creating {tf_name} data: {e}")

# =========================================
# FUNCTION TO DOWNLOAD AND SAVE DATA

def fetch_chunk_simple(url, headers, params, symbol, start_date, end_date):
    """Simple fetch function similar to original code"""
    for attempt in range(MAX_RETRIES):
        try:
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                candles = response.json()["data"]["candles"]
                if candles:
                    return candles
                else:
                    print(f"[{symbol}] No candles data for {start_date} to {end_date}")
                    return None
            else:
                print(f"[{symbol}] Attempt {attempt + 1}/{MAX_RETRIES} failed | Status: {response.status_code} | {start_date} to {end_date}")
                
        except Exception as e:
            print(f"[{symbol}] Attempt {attempt + 1}/{MAX_RETRIES} failed with error: {e}")
        
        # Wait before retry (except on last attempt)
        if attempt < MAX_RETRIES - 1:
            sleep_time = random.uniform(0.5, 1.5)
            time.sleep(sleep_time)
    
    return None

def fetch_and_save_data(symbol, token, from_date, to_date):
    current_start = from_date
    all_chunks = []
    chunk_count = 0
    
    print(f"\n[{symbol}] Starting download from {from_date.date()} to {to_date.date()}")
    
    while current_start <= to_date:
        current_end = min(current_start + timedelta(days=54), to_date)
        chunk_count += 1
        
        url = BASE_URL.format(token=token, timeframe=TIMEFRAME)
        params = {
            "oi": 1,
            "from": current_start.strftime("%Y-%m-%d"),
            "to": current_end.strftime("%Y-%m-%d")
        }
        
        print(f"[{symbol}] Fetching chunk {chunk_count}: {current_start.date()} to {current_end.date()}")
        
        candles = fetch_chunk_simple(url, HEADERS, params, symbol, current_start.date(), current_end.date())
        
        if candles:
            # Fix the polars DataFrame creation with explicit orientation (excluding OI)
            df = pl.DataFrame(
                candles, 
                schema=["timestamp", "open", "high", "low", "close", "volume", "oi"],
                orient="row"
            ).drop("oi")
            
            # Ensure consistent data types to avoid schema conflicts during concat
            df = df.with_columns([
                pl.col("timestamp").cast(pl.String),
                pl.col("open").cast(pl.Float64),
                pl.col("high").cast(pl.Float64),
                pl.col("low").cast(pl.Float64),
                pl.col("close").cast(pl.Float64),
                pl.col("volume").cast(pl.Float64)
            ])
            
            all_chunks.append(df)
            print(f"[{symbol}] Chunk {chunk_count} successful - {len(candles)} records")
        
        current_start = current_end + timedelta(days=1)
        
        # Add sleep between chunks
        if current_start <= to_date:
            sleep_time = random.uniform(SLEEP_MIN, SLEEP_MAX)
            time.sleep(sleep_time)
    
    if all_chunks:
        final_df = pl.concat(all_chunks)
        
        # Save 1-minute data
        save_path_1min = f"../data/historical/{symbol}_1min.parquet"
        final_df.write_parquet(save_path_1min, compression="brotli")
        total_records = len(final_df)
        print(f"[{symbol}] ✓ 1min data saved - {total_records} records to {save_path_1min}")
        
        # Create and save resampled timeframes
        print(f"[{symbol}] Creating resampled timeframes...")
        resample_to_timeframes(final_df, symbol)
        
        print(f"[{symbol}] ✓ COMPLETED - All timeframes saved")
        return True
    else:
        print(f"[{symbol}] ✗ FAILED - No data fetched")
        return False

# =========================================
# MAIN LOOP

print("=" * 60)
print("HISTORICAL DATA DOWNLOADER")
print("=" * 60)

# Get to_date once (common for all stocks)
to_date_input = input("Enter to_date (DDMMYYYY) - common for all stocks: ")
to_date = datetime.strptime(to_date_input, "%d%m%Y")

successful_downloads = 0
failed_downloads = 0

for i, symbol in enumerate(fno_list['SYMBOL'], 1):
    print(f"\n{'='*60}")
    print(f"PROCESSING STOCK {i}/{len(fno_list)} - {symbol}")
    print(f"{'='*60}")
    
    # Get from_date for each stock
    from_date_input = input(f"Enter from_date (DDMMYYYY) for {symbol}: ")
    from_date = datetime.strptime(from_date_input, "%d%m%Y")
    
    # Find token for the symbol
    match = instruments[instruments['tradingsymbol'] == symbol]
    
    if not match.empty:
        token = str(match.iloc[0]['instrument_token'])
        print(f"[{symbol}] Token found: {token}")
        
        success = fetch_and_save_data(symbol, token, from_date, to_date)
        
        if success:
            successful_downloads += 1
        else:
            failed_downloads += 1
            
    else:
        print(f"[{symbol}] ✗ Token not found in instruments file.")
        failed_downloads += 1
    
    # Small delay between stocks
    if i < len(fno_list):
        time.sleep(0.5)

print(f"\n{'='*60}")
print("DOWNLOAD SUMMARY")
print(f"{'='*60}")
print(f"Total stocks processed: {len(fno_list)}")
print(f"Successful downloads: {successful_downloads}")
print(f"Failed downloads: {failed_downloads}")
print(f"Success rate: {(successful_downloads/len(fno_list)*100):.1f}%")
print("Files created per successful stock:")
print("- symbol_1min.parquet (original 1-minute data)")
print("- symbol_3min.parquet (3-minute resampled)")
print("- symbol_5min.parquet (5-minute resampled)")
print("- symbol_15min.parquet (15-minute resampled)")
print(f"{'='*60}")