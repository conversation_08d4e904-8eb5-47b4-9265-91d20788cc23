#!/usr/bin/env python3
"""
Docker vs Native Performance Comparison
Comprehensive performance testing and comparison between Docker and native execution
"""

import asyncio
import json
import logging
import os
import psutil
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/performance_comparison.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Monitor system performance during execution"""
    
    def __init__(self):
        self.metrics = []
        self.monitoring = False
        
    async def start_monitoring(self, interval: float = 1.0):
        """Start performance monitoring"""
        self.monitoring = True
        self.metrics = []
        
        while self.monitoring:
            try:
                # CPU and Memory metrics
                cpu_percent = psutil.cpu_percent(interval=None)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                
                # GPU metrics (if available)
                gpu_metrics = self._get_gpu_metrics()
                
                metric = {
                    'timestamp': datetime.now().isoformat(),
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_used_gb': memory.used / (1024**3),
                    'memory_available_gb': memory.available / (1024**3),
                    'disk_percent': disk.percent,
                    'gpu_metrics': gpu_metrics
                }
                
                self.metrics.append(metric)
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"[ERROR] Monitoring error: {e}")
                break
                
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring = False
        
    def _get_gpu_metrics(self) -> Dict[str, Any]:
        """Get GPU metrics using nvidia-smi"""
        try:
            result = subprocess.run([
                'nvidia-smi', '--query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                values = result.stdout.strip().split(', ')
                return {
                    'gpu_utilization': float(values[0]),
                    'memory_used_mb': float(values[1]),
                    'memory_total_mb': float(values[2]),
                    'temperature_c': float(values[3])
                }
        except Exception:
            pass
            
        return {}
        
    def get_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        if not self.metrics:
            return {}
            
        cpu_values = [m['cpu_percent'] for m in self.metrics]
        memory_values = [m['memory_percent'] for m in self.metrics]
        
        summary = {
            'duration_seconds': len(self.metrics),
            'cpu_avg': sum(cpu_values) / len(cpu_values),
            'cpu_max': max(cpu_values),
            'memory_avg': sum(memory_values) / len(memory_values),
            'memory_max': max(memory_values),
            'sample_count': len(self.metrics)
        }
        
        # GPU summary if available
        gpu_metrics = [m['gpu_metrics'] for m in self.metrics if m['gpu_metrics']]
        if gpu_metrics:
            gpu_util = [g['gpu_utilization'] for g in gpu_metrics]
            summary['gpu_avg'] = sum(gpu_util) / len(gpu_util)
            summary['gpu_max'] = max(gpu_util)
            
        return summary

class PerformanceComparison:
    """Compare Docker vs Native performance"""
    
    def __init__(self):
        self.results = {
            'native': {},
            'docker': {},
            'comparison': {},
            'test_info': {
                'timestamp': datetime.now().isoformat(),
                'system_info': self._get_system_info()
            }
        }
        
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        return {
            'cpu_count': psutil.cpu_count(),
            'memory_total_gb': psutil.virtual_memory().total / (1024**3),
            'platform': sys.platform,
            'python_version': sys.version,
            'docker_available': self._check_docker_available()
        }
        
    def _check_docker_available(self) -> bool:
        """Check if Docker is available"""
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, timeout=5)
            return result.returncode == 0
        except Exception:
            return False
            
    async def test_native_execution(self) -> Dict[str, Any]:
        """Test native execution performance"""
        logger.info("[TEST] Starting native execution test...")
        
        monitor = PerformanceMonitor()
        
        # Start monitoring
        monitor_task = asyncio.create_task(monitor.start_monitoring())
        
        start_time = time.time()
        
        try:
            # Test execution agent
            result = await self._run_native_agent('execution')
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Stop monitoring
            monitor.stop_monitoring()
            await monitor_task
            
            performance_summary = monitor.get_summary()
            
            test_result = {
                'success': result,
                'execution_time': execution_time,
                'performance': performance_summary,
                'test_type': 'native'
            }
            
            self.results['native'] = test_result
            logger.info(f"[SUCCESS] Native test completed in {execution_time:.2f}s")
            
            return test_result
            
        except Exception as e:
            monitor.stop_monitoring()
            logger.error(f"[ERROR] Native test failed: {e}")
            return {'success': False, 'error': str(e)}
            
    async def test_docker_execution(self) -> Dict[str, Any]:
        """Test Docker execution performance"""
        logger.info("[TEST] Starting Docker execution test...")
        
        if not self.results['test_info']['system_info']['docker_available']:
            logger.error("[ERROR] Docker not available")
            return {'success': False, 'error': 'Docker not available'}
            
        monitor = PerformanceMonitor()
        
        # Start monitoring
        monitor_task = asyncio.create_task(monitor.start_monitoring())
        
        start_time = time.time()
        
        try:
            # Build Docker image first
            build_result = await self._build_docker_image()
            if not build_result:
                return {'success': False, 'error': 'Docker build failed'}
                
            # Test Docker execution
            result = await self._run_docker_agent('execution')
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Stop monitoring
            monitor.stop_monitoring()
            await monitor_task
            
            performance_summary = monitor.get_summary()
            
            test_result = {
                'success': result,
                'execution_time': execution_time,
                'performance': performance_summary,
                'test_type': 'docker'
            }
            
            self.results['docker'] = test_result
            logger.info(f"[SUCCESS] Docker test completed in {execution_time:.2f}s")
            
            return test_result
            
        except Exception as e:
            monitor.stop_monitoring()
            logger.error(f"[ERROR] Docker test failed: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _run_native_agent(self, agent_name: str) -> bool:
        """Run agent natively"""
        try:
            process = await asyncio.create_subprocess_exec(
                'python', 'main.py', '--agent', agent_name, '--trading-mode', 'paper',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # Wait for 30 seconds then terminate
            try:
                await asyncio.wait_for(process.wait(), timeout=30.0)
                return process.returncode == 0
            except asyncio.TimeoutError:
                process.terminate()
                await process.wait()
                return True  # Consider timeout as success for this test
                
        except Exception as e:
            logger.error(f"[ERROR] Native execution failed: {e}")
            return False
            
    async def _build_docker_image(self) -> bool:
        """Build Docker image"""
        try:
            logger.info("[DOCKER] Building Docker image...")
            process = await asyncio.create_subprocess_exec(
                'docker', 'build', '-t', 'intraday-ai:test', '.',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await process.wait()
            return process.returncode == 0
            
        except Exception as e:
            logger.error(f"[ERROR] Docker build failed: {e}")
            return False
            
    async def _run_docker_agent(self, agent_name: str) -> bool:
        """Run agent in Docker"""
        try:
            process = await asyncio.create_subprocess_exec(
                'docker', 'run', '--rm', '--env-file', '.env',
                'intraday-ai:test', 'python', 'main.py', 
                '--agent', agent_name, '--trading-mode', 'paper',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # Wait for 30 seconds then terminate
            try:
                await asyncio.wait_for(process.wait(), timeout=30.0)
                return process.returncode == 0
            except asyncio.TimeoutError:
                process.terminate()
                await process.wait()
                return True  # Consider timeout as success for this test
                
        except Exception as e:
            logger.error(f"[ERROR] Docker execution failed: {e}")
            return False
            
    def generate_comparison(self):
        """Generate performance comparison"""
        if not self.results['native'] or not self.results['docker']:
            logger.warning("[WARN] Incomplete test results for comparison")
            return
            
        native = self.results['native']
        docker = self.results['docker']
        
        comparison = {
            'execution_time_ratio': docker['execution_time'] / native['execution_time'],
            'cpu_overhead_percent': 0,
            'memory_overhead_percent': 0,
            'recommendations': []
        }
        
        # Performance comparison
        if 'performance' in native and 'performance' in docker:
            native_perf = native['performance']
            docker_perf = docker['performance']
            
            if 'cpu_avg' in native_perf and 'cpu_avg' in docker_perf:
                comparison['cpu_overhead_percent'] = (
                    (docker_perf['cpu_avg'] - native_perf['cpu_avg']) / native_perf['cpu_avg'] * 100
                )
                
            if 'memory_avg' in native_perf and 'memory_avg' in docker_perf:
                comparison['memory_overhead_percent'] = (
                    (docker_perf['memory_avg'] - native_perf['memory_avg']) / native_perf['memory_avg'] * 100
                )
                
        # Generate recommendations
        if comparison['execution_time_ratio'] < 1.1:
            comparison['recommendations'].append("Docker overhead is minimal - good for production")
        elif comparison['execution_time_ratio'] < 1.5:
            comparison['recommendations'].append("Docker has moderate overhead - acceptable for most use cases")
        else:
            comparison['recommendations'].append("Docker has significant overhead - consider native deployment")
            
        if comparison['cpu_overhead_percent'] < 10:
            comparison['recommendations'].append("CPU overhead is acceptable")
        else:
            comparison['recommendations'].append("High CPU overhead - optimize Docker configuration")
            
        self.results['comparison'] = comparison
        
    def save_results(self, filename: str = 'performance_comparison_results.json'):
        """Save results to file"""
        try:
            with open(filename, 'w') as f:
                json.dump(self.results, f, indent=2)
            logger.info(f"[SUCCESS] Results saved to {filename}")
        except Exception as e:
            logger.error(f"[ERROR] Failed to save results: {e}")
            
    def print_summary(self):
        """Print performance summary"""
        print("\n" + "="*60)
        print("DOCKER vs NATIVE PERFORMANCE COMPARISON")
        print("="*60)
        
        # System info
        sys_info = self.results['test_info']['system_info']
        print(f"System: {sys_info['cpu_count']} CPUs, {sys_info['memory_total_gb']:.1f}GB RAM")
        print(f"Docker Available: {sys_info['docker_available']}")
        
        # Test results
        if self.results['native']:
            native = self.results['native']
            print(f"\nNative Execution:")
            print(f"  Success: {native['success']}")
            print(f"  Time: {native.get('execution_time', 0):.2f}s")
            if 'performance' in native:
                perf = native['performance']
                print(f"  CPU Avg: {perf.get('cpu_avg', 0):.1f}%")
                print(f"  Memory Avg: {perf.get('memory_avg', 0):.1f}%")
                
        if self.results['docker']:
            docker = self.results['docker']
            print(f"\nDocker Execution:")
            print(f"  Success: {docker['success']}")
            print(f"  Time: {docker.get('execution_time', 0):.2f}s")
            if 'performance' in docker:
                perf = docker['performance']
                print(f"  CPU Avg: {perf.get('cpu_avg', 0):.1f}%")
                print(f"  Memory Avg: {perf.get('memory_avg', 0):.1f}%")
                
        # Comparison
        if self.results['comparison']:
            comp = self.results['comparison']
            print(f"\nComparison:")
            print(f"  Execution Time Ratio: {comp.get('execution_time_ratio', 0):.2f}x")
            print(f"  CPU Overhead: {comp.get('cpu_overhead_percent', 0):.1f}%")
            print(f"  Memory Overhead: {comp.get('memory_overhead_percent', 0):.1f}%")
            
            print(f"\nRecommendations:")
            for rec in comp.get('recommendations', []):
                print(f"  - {rec}")
                
        print("="*60)

async def main():
    """Main function"""
    logger.info("[START] Docker vs Native Performance Comparison")
    
    comparison = PerformanceComparison()
    
    try:
        # Test native execution
        await comparison.test_native_execution()
        
        # Test Docker execution
        await comparison.test_docker_execution()
        
        # Generate comparison
        comparison.generate_comparison()
        
        # Save and display results
        comparison.save_results()
        comparison.print_summary()
        
        logger.info("[SUCCESS] Performance comparison completed")
        
    except Exception as e:
        logger.error(f"[ERROR] Performance comparison failed: {e}")
        return 1
        
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
