#!/usr/bin/env python3
"""
Optimized Feature Engineering for Historical Data
Uses polars and pyarrow for high-performance feature generation
Works with timestamp, OHLCV data from the Fixed Historical Data Downloader
"""

import os
import sys
import asyncio
import logging
import polars as pl
import pyarrow as pa
from pathlib import Path
from typing import List, Dict, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Technical analysis libraries
try:
    import polars_talib as plta
    POLARS_TALIB_AVAILABLE = True
except ImportError:
    POLARS_TALIB_AVAILABLE = False
    print("Warning: polars-talib not available. Install with: pip install polars-talib")

try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OptimizedFeatureEngineering:
    """
    Optimized Feature Engineering using Polars
    Works with historical data files containing: timestamp, open, high, low, close, volume
    """

    def __init__(self, input_dir: str, output_dir: str, chunk_size: int = 100000):
        """Initialize the feature engineering processor"""
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.chunk_size = chunk_size

        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Check library availability
        if POLARS_TALIB_AVAILABLE:
            logger.info("Using polars-talib for technical indicators")
        else:
            logger.info("Using pure Polars implementations for technical indicators")

        logger.info(f"Feature Engineering initialized - Chunk size: {chunk_size:,}")
    
    def _parse_timestamp_and_create_metadata(self, df: pl.DataFrame, filename: str) -> pl.DataFrame:
        """Parse timestamp and create date, time, stock_name, symboltoken columns"""
        try:
            # Extract stock symbol from filename (e.g., "RELIANCE_1min.parquet" -> "RELIANCE")
            stock_name = filename.replace('.parquet', '').split('_')[0]
            
            # Parse timestamp to datetime
            df = df.with_columns([
                pl.col("timestamp").str.to_datetime("%Y-%m-%dT%H:%M:%S%z").alias("datetime")
            ])
            
            # Create required metadata columns
            df = df.with_columns([
                pl.col("datetime").dt.date().cast(pl.String).alias("date"),
                pl.col("datetime").dt.time().cast(pl.String).alias("time"),
                pl.col("datetime").dt.hour().alias("hour"), # Add hour column
                pl.lit(stock_name).alias("stock_name"),
                pl.lit(0).alias("symboltoken")  # Dummy symboltoken
            ])
            
            return df
        except Exception as e:
            logger.error(f"Error parsing timestamp and creating metadata: {e}")
            return df
    
    def _add_basic_features(self, df: pl.DataFrame) -> pl.DataFrame:
        """Add basic features like lag and returns"""
        try:
            df = df.with_columns([
                # Close lag
                pl.col("close").shift(1).alias("close_lag_1"),

                # Log return
                (pl.col("close") / pl.col("close").shift(1)).log().alias("log_return"),

                # Regime (simplified - based on price vs moving average)
                (pl.when(pl.col("close") > pl.col("close").rolling_mean(window_size=20))
                 .then(1)
                 .otherwise(0)
                 .alias("regime"))
            ])

            return df
        except Exception as e:
            logger.error(f"Error adding basic features: {e}")
            return df

    def _add_moving_averages(self, df: pl.DataFrame) -> pl.DataFrame:
        """Add moving average indicators using polars-talib or pure polars"""
        try:
            if POLARS_TALIB_AVAILABLE:
                # Add moving averages using polars-talib
                df = df.with_columns([
                    # EMAs using polars-talib
                    pl.col("close").ta.ema(5).alias("ema_5"),
                    pl.col("close").ta.ema(10).alias("ema_10"),
                    pl.col("close").ta.ema(13).alias("ema_13"),
                    pl.col("close").ta.ema(20).alias("ema_20"),
                    pl.col("close").ta.ema(21).alias("ema_21"),
                    pl.col("close").ta.ema(30).alias("ema_30"),
                    pl.col("close").ta.ema(50).alias("ema_50"),
                    pl.col("close").ta.ema(100).alias("ema_100"),

                    # SMA using polars-talib
                    pl.col("close").ta.sma(20).alias("sma_20")
                ])
            else:
                # Fallback to pure polars
                df = df.with_columns([
                    # EMAs using polars ewm_mean
                    pl.col("close").ewm_mean(span=5).alias("ema_5"),
                    pl.col("close").ewm_mean(span=10).alias("ema_10"),
                    pl.col("close").ewm_mean(span=13).alias("ema_13"),
                    pl.col("close").ewm_mean(span=20).alias("ema_20"),
                    pl.col("close").ewm_mean(span=21).alias("ema_21"),
                    pl.col("close").ewm_mean(span=30).alias("ema_30"),
                    pl.col("close").ewm_mean(span=50).alias("ema_50"),
                    pl.col("close").ewm_mean(span=100).alias("ema_100"),

                    # SMA using polars rolling_mean
                    pl.col("close").rolling_mean(window_size=20).alias("sma_20")
                ])

            return df
        except Exception as e:
            logger.error(f"Error adding moving averages: {e}")
            return df
    
    def _add_momentum_indicators(self, df: pl.DataFrame) -> pl.DataFrame:
        """Add momentum indicators using polars-talib or pure polars"""
        try:
            if POLARS_TALIB_AVAILABLE:
                df = df.with_columns([
                    # RSI
                    pl.col("close").ta.rsi(5).alias("rsi_5"),
                    pl.col("close").ta.rsi(14).alias("rsi_14"),

                    # MACD
                    pl.col("close").ta.macd(12, 26, 9).struct.field("macd").alias("macd"),
                    pl.col("close").ta.macd(12, 26, 9).struct.field("macdsignal").alias("macd_signal"),

                    # Stochastic
                    plta.stoch(pl.col("high"), pl.col("low"), pl.col("close"),
                              fastk_period=14, slowk_period=3, slowd_period=3).struct.field("slowk").alias("stoch_k"),
                    plta.stoch(pl.col("high"), pl.col("low"), pl.col("close"),
                              fastk_period=14, slowk_period=3, slowd_period=3).struct.field("slowd").alias("stoch_d"),

                    # CCI
                    plta.cci(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=20).alias("cci"),

                    # ADX
                    plta.adx(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=14).alias("adx"),

                    # MFI
                    plta.mfi(pl.col("high"), pl.col("low"), pl.col("close"), pl.col("volume"), timeperiod=14).alias("mfi"),

                    # ATR
                    plta.atr(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=14).alias("atr")
                ])
                
                # Add ATR SMA (required by strategies) - must be done after ATR is calculated
                df = df.with_columns([
                    pl.col("atr").rolling_mean(window_size=14).alias("atr_sma_14")
                ])
            else:
                # Fallback to pure polars implementations
                # RSI using Wilder's smoothing method
                for period in [5, 14]:
                    delta = pl.col("close").diff()
                    gain = delta.clip_min(0)
                    loss = (-delta).clip_min(0)

                    # Use Wilder's smoothing (alpha = 1/period)
                    alpha = 1.0 / period
                    avg_gain = gain.ewm_mean(alpha=alpha)
                    avg_loss = loss.ewm_mean(alpha=alpha)

                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))

                    df = df.with_columns([rsi.alias(f"rsi_{period}")])

                # MACD
                ema_12 = pl.col("close").ewm_mean(span=12)
                ema_26 = pl.col("close").ewm_mean(span=26)
                macd_line = ema_12 - ema_26
                macd_signal = macd_line.ewm_mean(span=9)

                df = df.with_columns([
                    macd_line.alias("macd"),
                    macd_signal.alias("macd_signal")
                ])

                # Stochastic
                high_14 = pl.col("high").rolling_max(window_size=14)
                low_14 = pl.col("low").rolling_min(window_size=14)
                stoch_k = 100 * (pl.col("close") - low_14) / (high_14 - low_14)
                stoch_d = stoch_k.rolling_mean(window_size=3)

                df = df.with_columns([
                    stoch_k.alias("stoch_k"),
                    stoch_d.alias("stoch_d")
                ])

                # CCI
                typical_price = (pl.col("high") + pl.col("low") + pl.col("close")) / 3
                sma_tp = typical_price.rolling_mean(window_size=20)
                mean_dev = (typical_price - sma_tp).abs().rolling_mean(window_size=20)
                cci = (typical_price - sma_tp) / (0.015 * mean_dev)

                df = df.with_columns([cci.alias("cci")])

                # ADX (simplified)
                high_low = pl.col("high") - pl.col("low")
                high_close = (pl.col("high") - pl.col("close").shift(1)).abs()
                low_close = (pl.col("low") - pl.col("close").shift(1)).abs()
                tr = pl.max_horizontal([high_low, high_close, low_close])
                atr = tr.ewm_mean(span=14)

                # Simplified ADX calculation
                plus_dm = (pl.when(pl.col("high").diff() > pl.col("low").diff().abs())
                          .then(pl.col("high").diff().clip_min(0))
                          .otherwise(0))
                minus_dm = (pl.when(pl.col("low").diff().abs() > pl.col("high").diff())
                           .then(pl.col("low").diff().abs())
                           .otherwise(0))

                plus_di = 100 * plus_dm.ewm_mean(span=14) / atr
                minus_di = 100 * minus_dm.ewm_mean(span=14) / atr
                dx = 100 * (plus_di - minus_di).abs() / (plus_di + minus_di)
                adx = dx.ewm_mean(span=14)

                # Add ATR SMA (required by strategies)
                atr_sma_14 = atr.rolling_mean(window_size=14)

                df = df.with_columns([
                    atr.alias("atr"),
                    atr_sma_14.alias("atr_sma_14"),
                    adx.alias("adx")
                ])

                # MFI (simplified)
                typical_price = (pl.col("high") + pl.col("low") + pl.col("close")) / 3
                money_flow = typical_price * pl.col("volume")

                # Positive and negative money flow
                pos_mf = (pl.when(typical_price > typical_price.shift(1))
                         .then(money_flow)
                         .otherwise(0))
                neg_mf = (pl.when(typical_price < typical_price.shift(1))
                         .then(money_flow)
                         .otherwise(0))

                pos_mf_sum = pos_mf.rolling_sum(window_size=14)
                neg_mf_sum = neg_mf.rolling_sum(window_size=14)

                mfi = 100 - (100 / (1 + pos_mf_sum / neg_mf_sum))

                df = df.with_columns([mfi.alias("mfi")])

            return df
        except Exception as e:
            logger.error(f"Error adding momentum indicators: {e}")
            return df
    
    def _add_volatility_indicators(self, df: pl.DataFrame) -> pl.DataFrame:
        """Add volatility indicators using polars-talib or pure polars"""
        try:
            if POLARS_TALIB_AVAILABLE:
                # Use polars-talib for Bollinger Bands
                bb_result = plta.bbands(pl.col("close"), timeperiod=20, nbdevup=2, nbdevdn=2)
                df = df.with_columns([
                    bb_result.struct.field("upperband").alias("bb_upper"),
                    bb_result.struct.field("middleband").alias("bb_middle"), # Added bb_middle
                    bb_result.struct.field("lowerband").alias("bb_lower")
                ])
            else:
                # Fallback to pure polars
                sma_20 = pl.col("close").rolling_mean(window_size=20)
                std_20 = pl.col("close").rolling_std(window_size=20)

                df = df.with_columns([
                    (sma_20 + 2 * std_20).alias("bb_upper"),
                    sma_20.alias("bb_middle"), # Added bb_middle
                    (sma_20 - 2 * std_20).alias("bb_lower")
                ])

            return df
        except Exception as e:
            logger.error(f"Error adding volatility indicators: {e}")
            return df
    
    def _add_volume_indicators(self, df: pl.DataFrame) -> pl.DataFrame:
        """Add volume-based indicators"""
        try:
            # VWAP - Volume Weighted Average Price
            typical_price = (pl.col("high") + pl.col("low") + pl.col("close")) / 3

            # Calculate VWAP using rolling window to avoid cumulative issues
            # Use a 20-period rolling VWAP
            vwap = (typical_price * pl.col("volume")).rolling_sum(window_size=20) / pl.col("volume").rolling_sum(window_size=20)

            # Add volume moving average (required by strategies)
            sma_20_volume = pl.col("volume").rolling_mean(window_size=20)

            df = df.with_columns([
                vwap.alias("vwap"),
                sma_20_volume.alias("sma_20_volume")
            ])

            return df
        except Exception as e:
            logger.error(f"Error adding volume indicators: {e}")
            return df
    
    def _add_custom_indicators(self, df: pl.DataFrame) -> pl.DataFrame:
        """Add custom indicators matching the exact structure"""
        try:
            # SuperTrend (simplified)
            hl2 = (pl.col("high") + pl.col("low")) / 2
            atr = pl.col("atr") if "atr" in df.columns else pl.lit(1.0)

            # SuperTrend calculation
            multiplier = 3.0
            upper_band = hl2 + (multiplier * atr)
            lower_band = hl2 - (multiplier * atr)

            # Simplified SuperTrend signal
            supertrend = (pl.when(pl.col("close") > upper_band.shift(1))
                         .then(lower_band)
                         .when(pl.col("close") < lower_band.shift(1))
                         .then(upper_band)
                         .otherwise(pl.col("close")))

            df = df.with_columns([supertrend.alias("supertrend")])

            # Donchian Channels
            donchian_high = pl.col("high").rolling_max(window_size=20)
            donchian_low = pl.col("low").rolling_min(window_size=20)

            df = df.with_columns([
                donchian_high.alias("donchian_high"),
                donchian_low.alias("donchian_low")
            ])

            # Pivot Points
            pivot = (pl.col("high") + pl.col("low") + pl.col("close")) / 3

            # CPR (Central Pivot Range)
            cpr_top = (pl.col("high") + pl.col("low")) / 2
            cpr_bottom = (pivot - (pl.col("high") - pl.col("low")))

            # Support and Resistance
            support = pivot - (pl.col("high") - pl.col("low"))
            resistance = pivot + (pl.col("high") - pl.col("low"))

            df = df.with_columns([
                pivot.alias("pivot"),
                cpr_top.alias("cpr_top"),
                cpr_bottom.alias("cpr_bottom"),
                support.alias("support"),
                resistance.alias("resistance")
            ])

            # Trendline (simplified - using linear regression slope)
            # Calculate slope of close prices over 20 periods
            trendline = pl.col("close").rolling_mean(window_size=20).rolling_mean(window_size=5)

            df = df.with_columns([trendline.alias("trendline")])

            # VCP Pattern (simplified - based on volume and price action)
            volume_avg = pl.col("volume").rolling_mean(window_size=20)
            price_range = pl.col("high") - pl.col("low")
            price_range_avg = price_range.rolling_mean(window_size=20)

            vcp_pattern = (pl.when(
                (pl.col("volume") < volume_avg * 0.8) &
                (price_range < price_range_avg * 0.8)
            ).then(1).otherwise(0))

            df = df.with_columns([vcp_pattern.alias("vcp_pattern")])

            # Candle patterns
            upward_candle = pl.when(pl.col("close") > pl.col("open")).then(1).otherwise(0)
            downward_candle = pl.when(pl.col("close") < pl.col("open")).then(1).otherwise(0)

            df = df.with_columns([
                upward_candle.alias("upward_candle"),
                downward_candle.alias("downward_candle")
            ])

            return df
        except Exception as e:
            logger.error(f"Error adding custom indicators: {e}")
            return df

    def _process_single_file(self, df: pl.DataFrame, filename: str) -> pl.DataFrame:
        """Process a single file's data with all technical indicators"""
        try:
            # Parse timestamp and create metadata columns
            df = self._parse_timestamp_and_create_metadata(df, filename)
            
            # Sort by datetime for proper technical indicator calculation
            df = df.sort("datetime")

            # Add all features
            df = self._add_basic_features(df)
            df = self._add_moving_averages(df)
            df = self._add_momentum_indicators(df)
            df = self._add_volatility_indicators(df)
            df = self._add_volume_indicators(df)
            df = self._add_custom_indicators(df)

            return df

        except Exception as e:
            logger.error(f"Error processing file {filename}: {e}")
            # Return original dataframe with null columns for missing features
            return self._add_missing_columns(df, filename)

    def _add_missing_columns(self, df: pl.DataFrame, filename: str) -> pl.DataFrame:
        """Add missing feature columns with null values"""
        try:
            # First add metadata if missing
            if "stock_name" not in df.columns:
                df = self._parse_timestamp_and_create_metadata(df, filename)
            
            expected_columns = [
                'date', 'time', 'hour', 'stock_name', 'symboltoken', 'open', 'high', 'low', 'close', 'volume', 'datetime',
                'close_lag_1', 'log_return', 'regime', 'ema_5', 'ema_10', 'ema_13', 'ema_20', 'ema_21', 'ema_30',
                'ema_50', 'ema_100', 'sma_20', 'rsi_5', 'rsi_14', 'macd', 'macd_signal', 'stoch_k', 'stoch_d',
                'cci', 'adx', 'mfi', 'bb_upper', 'bb_middle', 'bb_lower', 'atr', 'vwap', 'sma_20_volume', 'atr_sma_14', 'supertrend', 'donchian_high',
                'donchian_low', 'pivot', 'cpr_top', 'cpr_bottom', 'support', 'resistance', 'trendline',
                'vcp_pattern', 'upward_candle', 'downward_candle'
            ]

            # Add missing columns with null values
            for col in expected_columns:
                if col not in df.columns:
                    df = df.with_columns([pl.lit(None).alias(col)])

            return df
        except Exception as e:
            logger.error(f"Error adding missing columns: {e}")
            return df
    
    async def process_file(self, input_file: Path) -> bool:
        """Process a single file"""
        try:
            logger.info(f"Processing file: {input_file.name}")

            # Read file
            df = pl.read_parquet(input_file)
            logger.info(f"Loaded {len(df):,} rows from {input_file.name}")

            # Check if file has required columns
            required_cols = ["timestamp", "open", "high", "low", "close", "volume"]
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                logger.error(f"Missing required columns: {missing_cols}")
                return False

            # Process the file
            logger.info("Processing features...")
            processed_df = self._process_single_file(df, input_file.name)

            # Ensure all expected columns are present
            expected_columns = [
                'date', 'time', 'hour', 'stock_name', 'symboltoken', 'open', 'high', 'low', 'close', 'volume', 'datetime',
                'close_lag_1', 'log_return', 'regime', 'ema_5', 'ema_10', 'ema_13', 'ema_20', 'ema_21', 'ema_30',
                'ema_50', 'ema_100', 'sma_20', 'rsi_5', 'rsi_14', 'macd', 'macd_signal', 'stoch_k', 'stoch_d',
                'cci', 'adx', 'mfi', 'bb_upper', 'bb_middle', 'bb_lower', 'atr', 'vwap', 'sma_20_volume', 'atr_sma_14', 'supertrend', 'donchian_high',
                'donchian_low', 'pivot', 'cpr_top', 'cpr_bottom', 'support', 'resistance', 'trendline',
                'vcp_pattern', 'upward_candle', 'downward_candle'
            ]

            # Add missing columns with null values
            for col in expected_columns:
                if col not in processed_df.columns:
                    processed_df = processed_df.with_columns([pl.lit(None).alias(col)])

            # Select only expected columns in correct order
            final_df = processed_df.select(expected_columns)

            # Save output with features_ prefix
            output_file = self.output_dir / f"features_{input_file.name}"
            final_df.write_parquet(output_file, compression="brotli")

            logger.info(f"[SUCCESS] Saved {len(final_df):,} rows with {len(final_df.columns)} features to {output_file}")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Error processing file {input_file}: {e}")
            return False
    
    async def process_all_files(self) -> bool:
        """Process all parquet files in input directory (excluding features_ files)"""
        try:
            # Find all parquet files that don't start with "features_"
            parquet_files = [f for f in self.input_dir.glob("*.parquet") if not f.name.startswith("features_")]

            if not parquet_files:
                logger.error(f"No historical parquet files found in {self.input_dir}")
                return False

            logger.info(f"Found {len(parquet_files)} files to process")

            # Process each file
            success_count = 0
            for file_path in parquet_files:
                success = await self.process_file(file_path)
                if success:
                    success_count += 1

            logger.info(f"[SUCCESS] Successfully processed {success_count}/{len(parquet_files)} files")
            return success_count > 0

        except Exception as e:
            logger.error(f"[ERROR] Error processing files: {e}")
            return False

async def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(
        description='Optimized Feature Engineering using Polars',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python optimized_feature_engineering.py --input-dir data/historical --output-dir data/historical
  python optimized_feature_engineering.py --input-dir data/historical --output-dir data/historical --chunk-size 200000
        """
    )
    parser.add_argument('--input-dir', type=str, default='data/historical',
                       help='Input directory containing parquet files (default: data/historical)')
    parser.add_argument('--output-dir', type=str, default='data/features',
                       help='Output directory for feature files (default: data/historical)')
    parser.add_argument('--chunk-size', type=int, default=100000,
                       help='Chunk size for processing large files (default: 100000)')

    args = parser.parse_args()

    logger.info("[INIT] Starting Optimized Feature Engineering")
    logger.info(f"[FOLDER] Input directory: {args.input_dir}")
    logger.info(f"[FOLDER] Output directory: {args.output_dir}")
    logger.info(f"[STATUS] Chunk size: {args.chunk_size:,}")
    logger.info(f"[CONFIG] Polars-TAlib available: {POLARS_TALIB_AVAILABLE}")

    # Create processor and run
    processor = OptimizedFeatureEngineering(args.input_dir, args.output_dir, args.chunk_size)
    success = await processor.process_all_files()

    if success:
        logger.info("[SUCCESS] Feature engineering completed successfully!")
        logger.info("[METRICS] Features generated with comprehensive technical indicators")
    else:
        logger.error("[ERROR] Feature engineering failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
