"""
Tests for Feature 3: Enhanced Performance Metrics
"""
import pytest
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import patch

from agents.enhanced_backtesting_polars import AdvancedMetricsCalculator


class TestAdvancedMetricsCalculator:
    """Test suite for AdvancedMetricsCalculator class"""
    
    @pytest.fixture
    def calculator(self):
        """Create AdvancedMetricsCalculator instance"""
        return AdvancedMetricsCalculator()
    
    @pytest.mark.asyncio
    async def test_initialization(self, calculator):
        """Test proper initialization of AdvancedMetricsCalculator"""
        assert calculator.risk_free_rate == 0.06  # 6% default
        assert calculator.benchmark_return == 0.12  # 12% default
    
    @pytest.mark.asyncio
    async def test_calculate_comprehensive_metrics(self, calculator, sample_trades, sample_market_data):
        """Test comprehensive metrics calculation"""
        metrics = await calculator.calculate_comprehensive_metrics(
            sample_trades, sample_market_data, initial_capital=100000
        )
        
        # Check that all expected metrics are present
        expected_metrics = [
            'total_return', 'total_trades', 'win_rate', 'profit_factor',
            'sharpe_ratio', 'sortino_ratio', 'calmar_ratio', 'max_drawdown',
            'volatility', 'var_95', 'cvar_95', 'ulcer_index', 'pain_index',
            'recovery_factor', 'average_win', 'average_loss', 'largest_win',
            'largest_loss', 'consecutive_wins', 'consecutive_losses'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics, f"Missing metric: {metric}"
        
        # Check metric value ranges
        assert metrics['win_rate'] >= 0 and metrics['win_rate'] <= 1
        assert metrics['max_drawdown'] >= 0
        assert metrics['total_trades'] == len(sample_trades)
    
    @pytest.mark.asyncio
    async def test_basic_metrics_calculation(self, calculator, sample_trades):
        """Test basic metrics calculation"""
        metrics = await calculator._calculate_basic_metrics(sample_trades, 100000)
        
        assert 'total_return' in metrics
        assert 'total_trades' in metrics
        assert 'win_rate' in metrics
        assert 'profit_factor' in metrics
        
        # Verify calculations
        assert metrics['total_trades'] == len(sample_trades)
        
        winning_trades = [t for t in sample_trades if t['pnl_pct'] > 0]
        expected_win_rate = len(winning_trades) / len(sample_trades)
        assert abs(metrics['win_rate'] - expected_win_rate) < 1e-10
    
    @pytest.mark.asyncio
    async def test_risk_metrics_calculation(self, calculator, sample_trades):
        """Test risk metrics calculation"""
        returns = [t['pnl_pct'] / 100 for t in sample_trades]
        risk_metrics = await calculator._calculate_risk_metrics(returns)
        
        assert 'sharpe_ratio' in risk_metrics
        assert 'sortino_ratio' in risk_metrics
        assert 'max_drawdown' in risk_metrics
        assert 'volatility' in risk_metrics
        assert 'var_95' in risk_metrics
        assert 'cvar_95' in risk_metrics
        
        # Check that VaR and CVaR make sense
        assert risk_metrics['cvar_95'] >= risk_metrics['var_95']
        assert risk_metrics['max_drawdown'] >= 0
        assert risk_metrics['volatility'] >= 0
    
    @pytest.mark.asyncio
    async def test_sharpe_ratio_calculation(self, calculator):
        """Test Sharpe ratio calculation"""
        # Test with known values
        returns = [0.01, 0.02, -0.01, 0.015, 0.005]  # 5 daily returns
        
        sharpe = await calculator._calculate_sharpe_ratio(returns)
        
        # Manual calculation
        mean_return = np.mean(returns) * 252  # Annualized
        volatility = np.std(returns, ddof=1) * np.sqrt(252)  # Annualized
        expected_sharpe = (mean_return - 0.06) / volatility  # Risk-free rate = 6%
        
        assert abs(sharpe - expected_sharpe) < 1e-10
    
    @pytest.mark.asyncio
    async def test_sortino_ratio_calculation(self, calculator):
        """Test Sortino ratio calculation"""
        returns = [0.01, 0.02, -0.01, 0.015, -0.005]
        
        sortino = await calculator._calculate_sortino_ratio(returns)
        
        # Sortino should be higher than Sharpe for same returns (less penalty for upside volatility)
        sharpe = await calculator._calculate_sharpe_ratio(returns)
        assert sortino >= sharpe
    
    @pytest.mark.asyncio
    async def test_max_drawdown_calculation(self, calculator):
        """Test maximum drawdown calculation"""
        returns = [0.1, -0.05, 0.03, -0.08, 0.02, -0.12, 0.06]
        
        max_dd = await calculator._calculate_max_drawdown(returns)
        
        # Manual calculation of cumulative returns and drawdown
        cumulative = np.cumprod([1 + r for r in returns])
        running_max = np.maximum.accumulate(cumulative)
        drawdowns = (running_max - cumulative) / running_max
        expected_max_dd = np.max(drawdowns)
        
        assert abs(max_dd - expected_max_dd) < 1e-10
    
    @pytest.mark.asyncio
    async def test_var_calculation(self, calculator):
        """Test Value at Risk calculation"""
        returns = np.random.normal(0.001, 0.02, 1000)  # 1000 random returns
        
        var_95 = await calculator._calculate_var(returns, confidence_level=0.95)
        var_99 = await calculator._calculate_var(returns, confidence_level=0.99)
        
        # VaR should be positive (representing potential loss)
        assert var_95 > 0
        assert var_99 > 0
        
        # 99% VaR should be higher than 95% VaR
        assert var_99 > var_95
    
    @pytest.mark.asyncio
    async def test_cvar_calculation(self, calculator):
        """Test Conditional Value at Risk calculation"""
        returns = np.random.normal(0.001, 0.02, 1000)
        
        var_95 = await calculator._calculate_var(returns, confidence_level=0.95)
        cvar_95 = await calculator._calculate_cvar(returns, confidence_level=0.95)
        
        # CVaR should be higher than VaR
        assert cvar_95 >= var_95
    
    @pytest.mark.asyncio
    async def test_ulcer_index_calculation(self, calculator):
        """Test Ulcer Index calculation"""
        returns = [0.02, -0.01, 0.015, -0.03, 0.01, -0.02, 0.025]
        
        ulcer_index = await calculator._calculate_ulcer_index(returns)
        
        assert ulcer_index >= 0
        assert isinstance(ulcer_index, float)
    
    @pytest.mark.asyncio
    async def test_pain_index_calculation(self, calculator):
        """Test Pain Index calculation"""
        returns = [0.02, -0.01, 0.015, -0.03, 0.01, -0.02, 0.025]
        
        pain_index = await calculator._calculate_pain_index(returns)
        
        assert pain_index >= 0
        assert isinstance(pain_index, float)
    
    @pytest.mark.asyncio
    async def test_trade_analysis_metrics(self, calculator, sample_trades):
        """Test trade analysis metrics"""
        trade_metrics = await calculator._calculate_trade_analysis_metrics(sample_trades)
        
        assert 'average_win' in trade_metrics
        assert 'average_loss' in trade_metrics
        assert 'largest_win' in trade_metrics
        assert 'largest_loss' in trade_metrics
        assert 'consecutive_wins' in trade_metrics
        assert 'consecutive_losses' in trade_metrics
        
        # Check logical relationships
        assert trade_metrics['largest_win'] >= trade_metrics['average_win']
        assert trade_metrics['largest_loss'] <= trade_metrics['average_loss']  # Both should be negative
    
    @pytest.mark.asyncio
    async def test_statistical_significance_testing(self, calculator, sample_trades):
        """Test statistical significance testing"""
        returns = [t['pnl_pct'] / 100 for t in sample_trades]
        
        significance_results = await calculator._test_statistical_significance(returns)
        
        assert 't_statistic' in significance_results
        assert 'p_value' in significance_results
        assert 'is_significant' in significance_results
        assert 'confidence_level' in significance_results
        
        # p-value should be between 0 and 1
        assert 0 <= significance_results['p_value'] <= 1
    
    @pytest.mark.asyncio
    async def test_market_relative_metrics(self, calculator, sample_trades, sample_market_data):
        """Test market-relative metrics calculation"""
        returns = [t['pnl_pct'] / 100 for t in sample_trades]
        
        market_metrics = await calculator._calculate_market_relative_metrics(
            returns, sample_market_data
        )
        
        assert 'alpha' in market_metrics
        assert 'beta' in market_metrics
        assert 'information_ratio' in market_metrics
        assert 'tracking_error' in market_metrics
        
        # Beta should be a reasonable value
        assert -5 <= market_metrics['beta'] <= 5
    
    @pytest.mark.asyncio
    async def test_regime_based_analysis(self, calculator, sample_trades, sample_market_data):
        """Test regime-based performance analysis"""
        returns = [t['pnl_pct'] / 100 for t in sample_trades]
        
        regime_analysis = await calculator._analyze_regime_performance(
            returns, sample_market_data
        )
        
        assert 'bull_market_performance' in regime_analysis
        assert 'bear_market_performance' in regime_analysis
        assert 'sideways_market_performance' in regime_analysis
        assert 'high_volatility_performance' in regime_analysis
        assert 'low_volatility_performance' in regime_analysis
    
    @pytest.mark.asyncio
    async def test_empty_trades_handling(self, calculator, sample_market_data):
        """Test handling of empty trades list"""
        metrics = await calculator.calculate_comprehensive_metrics(
            [], sample_market_data, initial_capital=100000
        )
        
        # Should return default values for empty trades
        assert metrics['total_return'] == 0
        assert metrics['total_trades'] == 0
        assert metrics['win_rate'] == 0
        assert metrics['sharpe_ratio'] == 0
    
    @pytest.mark.asyncio
    async def test_single_trade_handling(self, calculator, sample_market_data):
        """Test handling of single trade"""
        single_trade = [{
            'entry_time': datetime(2023, 1, 1),
            'exit_time': datetime(2023, 1, 2),
            'pnl': 500,
            'pnl_pct': 5.0,
            'side': 'long'
        }]
        
        metrics = await calculator.calculate_comprehensive_metrics(
            single_trade, sample_market_data, initial_capital=10000
        )
        
        assert metrics['total_trades'] == 1
        assert metrics['total_return'] == 5.0
        assert metrics['win_rate'] == 1.0
        assert metrics['consecutive_wins'] == 1
        assert metrics['consecutive_losses'] == 0
    
    @pytest.mark.asyncio
    async def test_negative_returns_handling(self, calculator):
        """Test handling of all negative returns"""
        negative_returns = [-0.01, -0.02, -0.015, -0.005, -0.03]
        
        risk_metrics = await calculator._calculate_risk_metrics(negative_returns)
        
        # Sharpe ratio should be negative
        assert risk_metrics['sharpe_ratio'] < 0
        
        # Sortino ratio should also be negative
        assert risk_metrics['sortino_ratio'] < 0
        
        # Max drawdown should be positive
        assert risk_metrics['max_drawdown'] > 0
