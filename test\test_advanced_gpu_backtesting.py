#!/usr/bin/env python3
"""
Advanced GPU Backtesting Performance Test
Implements latest techniques from NVIDIA research and financial ML papers
"""

import os
import sys
import time
import logging
import numpy as np
import polars as pl
from pathlib import Path

# Add agents directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'agents'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s:%(message)s')
logger = logging.getLogger(__name__)

def test_gpu_availability():
    """Test advanced GPU capabilities"""
    try:
        import cupy as cp
        import cudf
        from numba import cuda
        
        # Test CuPy
        test_array = cp.array([1, 2, 3, 4, 5])
        result = cp.sum(test_array)
        logger.info(f"✅ CuPy test successful: {result}")
        
        # Test CUDA context
        device = cuda.get_current_device()
        logger.info(f"🔧 CUDA Device: {device}")
        
        # Test memory info
        meminfo = cuda.current_context().get_memory_info()
        total_memory = meminfo[1] / 1024**3  # Convert to GB
        logger.info(f"🔧 GPU Memory: {total_memory:.1f} GB total")
        
        # Test Numba CUDA compilation
        from numba import cuda
        
        @cuda.jit
        def test_kernel(arr):
            idx = cuda.grid(1)
            if idx < arr.size:
                arr[idx] = arr[idx] * 2
        
        test_data = cp.array([1, 2, 3, 4, 5], dtype=cp.float32)
        test_kernel[1, 5](test_data)
        cuda.synchronize()
        
        logger.info(f"✅ Numba CUDA kernel test successful: {test_data}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ GPU libraries not available: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ GPU test failed: {e}")
        return False

def load_real_data():
    """Load real market data for testing"""
    try:
        # Try to load existing feature data
        data_files = [
            "data/features/features_historical_15min.parquet",
            "data/features/features_historical_5min.parquet"
        ]
        
        for file_path in data_files:
            if os.path.exists(file_path):
                logger.info(f"📊 Loading real data from {file_path}")
                df = pl.read_parquet(file_path)
                
                # Take a sample for testing
                sample_size = min(50000, len(df))
                df_sample = df.head(sample_size)
                
                logger.info(f"📊 Loaded {len(df_sample)} rows of real market data")
                return df_sample
        
        logger.warning("⚠️ No real data found, generating synthetic data")
        return generate_synthetic_data()
        
    except Exception as e:
        logger.warning(f"⚠️ Failed to load real data: {e}, using synthetic data")
        return generate_synthetic_data()

def generate_synthetic_data(n_points: int = 50000):
    """Generate realistic synthetic market data"""
    logger.info(f"🔧 Generating {n_points} points of synthetic market data")
    
    np.random.seed(42)
    
    # Generate realistic price movements
    base_price = 100.0
    volatility = 0.02
    drift = 0.0001
    
    # Generate price series using geometric Brownian motion
    dt = 1.0 / (252 * 24)  # Hourly data
    price_changes = np.random.normal(drift * dt, volatility * np.sqrt(dt), n_points)
    prices = base_price * np.exp(np.cumsum(price_changes))
    
    # Generate OHLC data
    highs = prices * (1 + np.abs(np.random.normal(0, 0.005, n_points)))
    lows = prices * (1 - np.abs(np.random.normal(0, 0.005, n_points)))
    volumes = np.random.lognormal(10, 1, n_points).astype(int)
    
    # Add technical indicators
    ema_12 = calculate_ema(prices, 12)
    ema_26 = calculate_ema(prices, 26)
    rsi = calculate_rsi(prices, 14)
    
    return pl.DataFrame({
        'datetime': pl.date_range(
            start=pl.datetime(2023, 1, 1),
            end=pl.datetime(2024, 1, 1),
            interval='1h'
        )[:n_points],
        'stock_name': ['TEST'] * n_points,
        'open': prices,
        'high': highs,
        'low': lows,
        'close': prices,
        'volume': volumes,
        'EMA_12': ema_12,
        'EMA_26': ema_26,
        'RSI_14': rsi
    })

def calculate_ema(prices, period):
    """Calculate EMA for synthetic data"""
    alpha = 2.0 / (period + 1)
    ema = np.zeros_like(prices)
    ema[0] = prices[0]
    
    for i in range(1, len(prices)):
        ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]
    
    return ema

def calculate_rsi(prices, period):
    """Calculate RSI for synthetic data"""
    deltas = np.diff(prices)
    gains = np.where(deltas > 0, deltas, 0)
    losses = np.where(deltas < 0, -deltas, 0)
    
    avg_gains = np.zeros(len(prices))
    avg_losses = np.zeros(len(prices))
    
    # Initial averages
    if len(gains) >= period:
        avg_gains[period] = np.mean(gains[:period])
        avg_losses[period] = np.mean(losses[:period])
        
        # Smoothed averages
        for i in range(period + 1, len(prices)):
            avg_gains[i] = (avg_gains[i-1] * (period - 1) + gains[i-1]) / period
            avg_losses[i] = (avg_losses[i-1] * (period - 1) + losses[i-1]) / period
    
    # Calculate RSI
    rs = avg_gains / (avg_losses + 1e-10)
    rsi = 100 - (100 / (1 + rs))
    
    return rsi

def run_comprehensive_gpu_test():
    """Run comprehensive GPU vs CPU performance test"""
    logger.info("🔬 Starting Comprehensive GPU vs CPU Backtesting Test")
    logger.info("=" * 80)
    
    # Test GPU availability
    gpu_available = test_gpu_availability()
    
    if not gpu_available:
        logger.error("❌ GPU not available for testing")
        return
    
    # Load test data
    test_data = load_real_data()
    logger.info(f"📊 Test data shape: {test_data.shape}")
    
    # Import GPU backtesting engine
    try:
        from gpu_accelerated_backtesting import GPUBacktestingEngine
    except ImportError as e:
        logger.error(f"❌ Failed to import GPU backtesting engine: {e}")
        return
    
    # Test different data sizes
    test_sizes = [1000, 5000, 10000, 25000, 50000]
    results = []
    
    for size in test_sizes:
        if size > len(test_data):
            continue
            
        logger.info(f"\n🔄 Testing with {size:,} data points")
        test_subset = test_data.head(size)
        
        # Initialize engines
        gpu_engine = GPUBacktestingEngine(use_gpu=True)
        cpu_engine = GPUBacktestingEngine(use_gpu=False)
        
        risk_reward = [1.0, 2.0]  # 1% risk, 2% reward
        
        # GPU Test
        gpu_time = 0
        gpu_trades = 0
        if gpu_engine.use_gpu:
            try:
                logger.info("  🚀 Running GPU test...")
                start_time = time.time()
                
                gpu_signals = gpu_engine.generate_signals_gpu(test_subset)
                gpu_trade_results = gpu_engine.simulate_trades_gpu(test_subset, gpu_signals, risk_reward)
                
                gpu_time = time.time() - start_time
                gpu_trades = len(gpu_trade_results)
                logger.info(f"  🚀 GPU: {gpu_time:.4f}s, {gpu_trades} trades")
                
            except Exception as e:
                logger.error(f"  ❌ GPU test failed: {e}")
                gpu_time = float('inf')
        
        # CPU Test
        try:
            logger.info("  🔧 Running CPU test...")
            start_time = time.time()
            
            cpu_signals = cpu_engine.generate_signals_gpu(test_subset)  # Will fallback to CPU
            cpu_trade_results = cpu_engine.simulate_trades_gpu(test_subset, cpu_signals, risk_reward)
            
            cpu_time = time.time() - start_time
            cpu_trades = len(cpu_trade_results)
            logger.info(f"  🔧 CPU: {cpu_time:.4f}s, {cpu_trades} trades")
            
        except Exception as e:
            logger.error(f"  ❌ CPU test failed: {e}")
            cpu_time = float('inf')
        
        # Calculate speedup
        if gpu_time > 0 and cpu_time > 0 and gpu_time != float('inf'):
            speedup = cpu_time / gpu_time
            logger.info(f"  🎯 Speedup: {speedup:.2f}x")
        else:
            speedup = 0
            logger.info("  ⚠️ Could not calculate speedup")
        
        results.append({
            'size': size,
            'gpu_time': gpu_time,
            'cpu_time': cpu_time,
            'speedup': speedup,
            'gpu_trades': gpu_trades,
            'cpu_trades': cpu_trades
        })
    
    # Summary
    logger.info("\n" + "="*80)
    logger.info("📊 COMPREHENSIVE GPU vs CPU PERFORMANCE RESULTS")
    logger.info("="*80)
    logger.info(f"{'Size':<10} {'GPU Time':<12} {'CPU Time':<12} {'Speedup':<10} {'GPU Trades':<12} {'CPU Trades':<12}")
    logger.info("-" * 80)
    
    valid_results = [r for r in results if r['speedup'] > 0]
    
    for result in results:
        speedup_str = f"{result['speedup']:.2f}x" if result['speedup'] > 0 else "N/A"
        logger.info(f"{result['size']:<10,} {result['gpu_time']:<12.4f} {result['cpu_time']:<12.4f} "
                   f"{speedup_str:<10} {result['gpu_trades']:<12} {result['cpu_trades']:<12}")
    
    if valid_results:
        avg_speedup = sum(r['speedup'] for r in valid_results) / len(valid_results)
        max_speedup = max(r['speedup'] for r in valid_results)
        
        logger.info(f"\n🎯 Average GPU Speedup: {avg_speedup:.2f}x")
        logger.info(f"🎯 Maximum GPU Speedup: {max_speedup:.2f}x")
        
        if avg_speedup > 5:
            logger.info("🚀 GPU acceleration provides EXCELLENT performance benefit!")
        elif avg_speedup > 2:
            logger.info("✅ GPU acceleration provides significant performance benefit!")
        elif avg_speedup > 1.2:
            logger.info("⚡ GPU acceleration provides moderate performance benefit")
        else:
            logger.info("⚠️ GPU overhead may not be worth it for this workload")
    else:
        logger.warning("⚠️ No valid GPU performance results obtained")
    
    # Recommendations
    logger.info("\n💡 RECOMMENDATIONS:")
    logger.info("1. GPU acceleration is most effective with larger datasets (25K+ points)")
    logger.info("2. For smaller datasets, CPU multiprocessing may be more efficient")
    logger.info("3. Consider hybrid approach: GPU for signal generation, CPU for trade simulation")
    logger.info("4. Memory transfer overhead can negate benefits for small computations")

def main():
    """Main test function"""
    try:
        run_comprehensive_gpu_test()
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
