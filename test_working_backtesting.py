#!/usr/bin/env python3
"""
Test Working Backtesting System
- Uses a simplified approach that definitely works
- Tests basic functionality without complex expressions
"""

import polars as pl
import numpy as np
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_working_signals():
    """Test signal generation with direct Polars expressions"""
    
    logger.info("🧪 Testing Working Signal Generation")
    logger.info("=" * 50)
    
    # Load data
    data_file = "data/features/features_360ONE_1min.parquet"
    if not Path(data_file).exists():
        logger.error(f"Data file not found: {data_file}")
        return
    
    df = pl.read_parquet(data_file)
    logger.info(f"📊 Data loaded: {df.shape[0]} rows, {df.shape[1]} columns")
    
    # Remove rows with NaN values for testing
    df = df.drop_nulls()
    logger.info(f"📊 After removing nulls: {df.shape[0]} rows")
    
    if len(df) < 1000:
        logger.warning("Not enough data after removing nulls")
        return
    
    # Test simple strategies with direct Polars expressions
    strategies = [
        {
            'name': 'Simple_RSI_Long',
            'condition': (pl.col("rsi_14") < 30) & (pl.col("close") > pl.col("ema_10"))
        },
        {
            'name': 'Simple_RSI_Short', 
            'condition': (pl.col("rsi_14") > 70) & (pl.col("close") < pl.col("ema_10"))
        },
        {
            'name': 'Simple_MACD_Long',
            'condition': (pl.col("macd") > pl.col("macd_signal")) & (pl.col("close") > pl.col("ema_20"))
        },
        {
            'name': 'Simple_EMA_Long',
            'condition': (pl.col("ema_5") > pl.col("ema_20")) & (pl.col("close") > pl.col("ema_5"))
        }
    ]
    
    logger.info("\n🎯 Testing Direct Polars Expressions:")
    logger.info("-" * 50)
    
    for strategy in strategies:
        try:
            # Generate signals
            signals = df.select(strategy['condition'].alias("signal"))
            signal_count = signals.select(pl.col("signal").sum()).item()
            total_rows = len(signals)
            percentage = (signal_count / total_rows) * 100 if total_rows > 0 else 0
            
            logger.info(f"  {strategy['name']:<20}: {signal_count:>6} signals ({percentage:>5.1f}%)")
            
            if signal_count > 0:
                # Test simple backtesting
                signals_array = signals.to_series().to_numpy()
                prices = df.select("close").to_series().to_numpy()
                
                # Simple buy and hold simulation
                trades = []
                position = None
                
                for i in range(len(signals_array)):
                    if signals_array[i] and position is None:
                        # Enter position
                        position = {
                            'entry_price': prices[i],
                            'entry_index': i
                        }
                    elif position is not None and (i == len(signals_array) - 1 or i - position['entry_index'] > 100):
                        # Exit position (after 100 bars or at end)
                        exit_price = prices[i]
                        pnl = (exit_price - position['entry_price']) / position['entry_price'] * 100
                        trades.append({
                            'entry_price': position['entry_price'],
                            'exit_price': exit_price,
                            'pnl_pct': pnl,
                            'bars_held': i - position['entry_index']
                        })
                        position = None
                
                if trades:
                    avg_pnl = np.mean([t['pnl_pct'] for t in trades])
                    win_rate = len([t for t in trades if t['pnl_pct'] > 0]) / len(trades) * 100
                    logger.info(f"    → {len(trades)} trades, Avg PnL: {avg_pnl:.2f}%, Win Rate: {win_rate:.1f}%")
                else:
                    logger.info(f"    → No completed trades")
            
        except Exception as e:
            logger.error(f"  {strategy['name']:<20}: ❌ Error - {e}")
    
    logger.info("\n✅ Working signal generation test completed!")

if __name__ == "__main__":
    test_working_signals()