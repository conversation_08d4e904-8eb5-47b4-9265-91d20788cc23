#!/usr/bin/env python3
"""
Test script to download data for a single symbol to debug API issues
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scripts.simple_historical_downloader import SimpleHistoricalDownloader

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_single_symbol():
    """Test downloading data for a single symbol"""
    
    downloader = SimpleHistoricalDownloader()
    
    # Initialize API
    if not await downloader.initialize():
        logger.error("Failed to initialize API")
        return
    
    # Test with a single symbol
    symbol = "RELIANCE-EQ"
    token = "2885"
    exchange = "NSE"
    
    # Use a past date - July 19, 2024 (not 2025!)
    from_date = datetime(2024, 7, 19, 9, 15)
    to_date = datetime(2024, 7, 19, 15, 30)
    
    logger.info(f"Testing download for {symbol} (token: {token})")
    logger.info(f"Date range: {from_date} to {to_date}")
    
    # Download data
    df = await downloader.download_symbol_data(symbol, token, exchange, from_date, to_date)
    
    if df is not None:
        logger.info(f"SUCCESS: Downloaded {len(df)} records for {symbol}")
        logger.info(f"Columns: {df.columns}")
        logger.info(f"Sample data:\n{df.head()}")
    else:
        logger.error(f"FAILED: No data downloaded for {symbol}")

if __name__ == "__main__":
    asyncio.run(test_single_symbol())
