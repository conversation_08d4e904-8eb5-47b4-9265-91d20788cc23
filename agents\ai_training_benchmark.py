#!/usr/bin/env python3
"""
AI Training Agent Benchmark
Performance benchmarking and optimization analysis tool
"""

import os
import time
import psutil
import logging
import asyncio
import numpy as np
import polars as pl
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import json

# GPU monitoring
try:
    import GPUtil
    GPU_MONITORING = True
except ImportError:
    GPU_MONITORING = False

# Import our modules
from ai_training_agent import AITrainingAgent, AITrainingConfig
from ai_training_utils import get_system_info

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [RUNNING] BENCHMARK CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class BenchmarkConfig:
    """Configuration for benchmarking"""
    
    # Test data sizes
    test_data_sizes: List[int] = None
    
    # Model configurations to test
    test_configurations: List[Dict[str, Any]] = None
    
    # Performance metrics to track
    track_memory: bool = True
    track_gpu: bool = True
    track_cpu: bool = True
    
    # Benchmark settings
    warmup_runs: int = 1
    benchmark_runs: int = 3
    
    # Output settings
    results_file: str = "data/models/benchmark_results.json"
    detailed_logging: bool = True
    
    def __post_init__(self):
        """Initialize default values"""
        if self.test_data_sizes is None:
            self.test_data_sizes = [100, 500, 1000, 5000, 10000]
        
        if self.test_configurations is None:
            self.test_configurations = [
                {
                    'name': 'fast_config',
                    'optuna_trials': 5,
                    'lgb_num_boost_round': 50,
                    'tabnet_max_epochs': 20,
                    'use_gpu': False
                },
                {
                    'name': 'balanced_config',
                    'optuna_trials': 20,
                    'lgb_num_boost_round': 200,
                    'tabnet_max_epochs': 50,
                    'use_gpu': True
                },
                {
                    'name': 'high_quality_config',
                    'optuna_trials': 50,
                    'lgb_num_boost_round': 500,
                    'tabnet_max_epochs': 100,
                    'use_gpu': True
                }
            ]

class SystemMonitor:
    """Monitor system resources during benchmarking"""
    
    def __init__(self):
        self.monitoring = False
        self.metrics = []
    
    def start_monitoring(self):
        """Start resource monitoring"""
        self.monitoring = True
        self.metrics = []
    
    def stop_monitoring(self):
        """Stop resource monitoring"""
        self.monitoring = False
    
    def record_metrics(self):
        """Record current system metrics"""
        if not self.monitoring:
            return
        
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        
        metrics = {
            'timestamp': time.time(),
            'cpu_percent': cpu_percent,
            'memory_used_gb': memory.used / (1024**3),
            'memory_percent': memory.percent
        }
        
        # GPU metrics if available
        if GPU_MONITORING:
            try:
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu = gpus[0]  # Use first GPU
                    metrics.update({
                        'gpu_load': gpu.load * 100,
                        'gpu_memory_used': gpu.memoryUsed,
                        'gpu_memory_total': gpu.memoryTotal,
                        'gpu_temperature': gpu.temperature
                    })
            except:
                pass
        
        self.metrics.append(metrics)
    
    def get_summary(self) -> Dict[str, float]:
        """Get summary statistics of monitored metrics"""
        if not self.metrics:
            return {}
        
        # Calculate averages and peaks
        cpu_values = [m['cpu_percent'] for m in self.metrics]
        memory_values = [m['memory_used_gb'] for m in self.metrics]
        
        summary = {
            'avg_cpu_percent': np.mean(cpu_values),
            'max_cpu_percent': np.max(cpu_values),
            'avg_memory_gb': np.mean(memory_values),
            'max_memory_gb': np.max(memory_values)
        }
        
        # GPU summary if available
        gpu_load_values = [m.get('gpu_load', 0) for m in self.metrics if 'gpu_load' in m]
        if gpu_load_values:
            summary.update({
                'avg_gpu_load': np.mean(gpu_load_values),
                'max_gpu_load': np.max(gpu_load_values)
            })
        
        return summary

class AITrainingBenchmark:
    """
    Comprehensive benchmarking tool for AI Training Agent
    
    Features:
    - Performance testing across different data sizes
    - Configuration comparison
    - Resource utilization monitoring
    - Training speed analysis
    - Memory usage profiling
    - GPU utilization tracking
    """
    
    def __init__(self, config: Optional[BenchmarkConfig] = None):
        """Initialize benchmark tool"""
        self.config = config or BenchmarkConfig()
        self.results = []
        self.monitor = SystemMonitor()
        
        # Create output directory
        os.makedirs(os.path.dirname(self.config.results_file), exist_ok=True)
        
        logger.info("[RUNNING] AI Training Benchmark initialized")
    
    def generate_test_data(self, n_samples: int) -> pl.DataFrame:
        """Generate synthetic test data for benchmarking"""
        np.random.seed(42)  # Consistent data for fair comparison
        
        data = {
            'strategy_name': [f'Strategy_{i}' for i in range(n_samples)],
            'symbol': np.random.choice(['AAPL', 'GOOGL', 'MSFT'], n_samples),
            'timeframe': np.random.choice(['5min', '15min', '1h'], n_samples),
            'n_trades': np.random.randint(10, 200, n_samples),
            'ROI': np.random.normal(0.05, 0.2, n_samples),
            'accuracy': np.random.uniform(0.3, 0.9, n_samples),
            'expectancy': np.random.normal(0.02, 0.15, n_samples),
            'sharpe_ratio': np.random.normal(1.0, 0.8, n_samples),
            'max_drawdown': np.random.uniform(0.02, 0.4, n_samples),
            'profit_factor': np.random.uniform(0.5, 3.0, n_samples),
            'avg_holding_period': np.random.uniform(0.5, 15, n_samples),
            'risk_reward_ratio': np.random.uniform(0.3, 4.0, n_samples),
            'capital_at_risk': np.random.uniform(0.005, 0.15, n_samples),
            'liquidity': np.random.uniform(0.3, 1.0, n_samples),
            'volatility': np.random.uniform(0.05, 0.8, n_samples),
            'market_regime': np.random.choice(['bull', 'bear', 'sideways'], n_samples),
            'correlation_index': np.random.uniform(-0.8, 0.9, n_samples),
            'drawdown_duration': np.random.uniform(0.5, 30, n_samples),
            'winning_trades': np.random.randint(3, 120, n_samples),
            'losing_trades': np.random.randint(2, 80, n_samples),
            'avg_win': np.random.uniform(50, 2000, n_samples),
            'avg_loss': np.random.uniform(-2000, -50, n_samples),
            'total_pnl': np.random.normal(500, 8000, n_samples),
            'position_size_pct': np.random.uniform(0.005, 0.12, n_samples),
            'is_profitable': np.random.choice([True, False], n_samples)
        }
        
        return pl.DataFrame(data)
    
    def create_agent_config(self, test_config: Dict[str, Any]) -> AITrainingConfig:
        """Create agent configuration from test configuration"""
        config = AITrainingConfig()
        
        # Apply test configuration
        config.optuna_trials = test_config.get('optuna_trials', 10)
        config.use_gpu = test_config.get('use_gpu', False)
        
        # LightGBM settings
        config.lgb_params['num_boost_round'] = test_config.get('lgb_num_boost_round', 100)
        
        # TabNet settings
        config.tabnet_params['max_epochs'] = test_config.get('tabnet_max_epochs', 50)
        
        return config
    
    async def benchmark_training(self, data_size: int, test_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Benchmark training performance for specific data size and configuration
        
        Args:
            data_size: Number of samples in test data
            test_config: Configuration to test
            
        Returns:
            Benchmark results
        """
        logger.info(f"[RUNNING] Benchmarking {test_config['name']} with {data_size} samples")
        
        # Generate test data
        test_data = self.generate_test_data(data_size)
        
        # Create agent configuration
        agent_config = self.create_agent_config(test_config)
        
        # Initialize agent
        agent = AITrainingAgent(agent_config)
        
        # Start monitoring
        self.monitor.start_monitoring()
        
        # Record start metrics
        start_time = time.time()
        start_memory = psutil.virtual_memory().used / (1024**3)
        
        try:
            # Preprocess data
            features, targets, _ = agent.preprocess_data(test_data)
            
            # Split data
            X_train, X_val, X_test, y_train, y_val, y_test = agent.split_data(features, targets)
            
            # Train models (without hyperparameter optimization for consistent timing)
            lgb_start = time.time()
            lgb_models = agent.train_lightgbm(X_train, y_train, X_val, y_val)
            lgb_time = time.time() - lgb_start
            
            # Record metrics during training
            self.monitor.record_metrics()
            
            # TabNet training (mocked for speed)
            tabnet_start = time.time()
            # Mock TabNet training for benchmark
            tabnet_time = time.time() - tabnet_start + 5.0  # Simulate TabNet time
            
            # Make predictions
            pred_start = time.time()
            predictions, confidence = agent.predict(X_test)
            pred_time = time.time() - pred_start
            
            # Calculate performance metrics
            evaluation_metrics = agent.evaluate_models(X_test, y_test)
            
            # Stop monitoring
            self.monitor.stop_monitoring()
            
            # Calculate total time and memory
            total_time = time.time() - start_time
            end_memory = psutil.virtual_memory().used / (1024**3)
            memory_used = end_memory - start_memory
            
            # Get monitoring summary
            resource_summary = self.monitor.get_summary()
            
            # Compile results
            result = {
                'config_name': test_config['name'],
                'data_size': data_size,
                'success': True,
                'timing': {
                    'total_time_seconds': total_time,
                    'lgb_training_time': lgb_time,
                    'tabnet_training_time': tabnet_time,
                    'prediction_time': pred_time,
                    'samples_per_second': data_size / total_time
                },
                'memory': {
                    'memory_used_gb': memory_used,
                    'peak_memory_gb': resource_summary.get('max_memory_gb', 0)
                },
                'performance': {
                    'overall_r2': evaluation_metrics['overall']['r2'],
                    'overall_rmse': evaluation_metrics['overall']['rmse'],
                    'mean_confidence': evaluation_metrics['overall']['mean_confidence']
                },
                'resources': resource_summary,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"[SUCCESS] Benchmark completed in {total_time:.1f}s")
            
        except Exception as e:
            self.monitor.stop_monitoring()
            
            result = {
                'config_name': test_config['name'],
                'data_size': data_size,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.error(f"[ERROR] Benchmark failed: {str(e)}")
        
        return result
    
    async def run_comprehensive_benchmark(self) -> Dict[str, Any]:
        """Run comprehensive benchmark across all configurations and data sizes"""
        logger.info("[INIT] Starting comprehensive benchmark")
        
        all_results = []
        
        # Test each configuration
        for test_config in self.config.test_configurations:
            config_results = []
            
            # Test each data size
            for data_size in self.config.test_data_sizes:
                # Run multiple times for averaging
                run_results = []
                
                for run in range(self.config.benchmark_runs):
                    logger.info(f"Run {run + 1}/{self.config.benchmark_runs}")
                    
                    result = await self.benchmark_training(data_size, test_config)
                    run_results.append(result)
                    
                    # Brief pause between runs
                    await asyncio.sleep(1)
                
                # Average results
                if run_results and all(r['success'] for r in run_results):
                    avg_result = self.average_results(run_results)
                    config_results.append(avg_result)
                else:
                    # Use first result if averaging fails
                    config_results.append(run_results[0] if run_results else None)
            
            all_results.extend([r for r in config_results if r is not None])
        
        # Compile final results
        benchmark_summary = {
            'benchmark_timestamp': datetime.now().isoformat(),
            'system_info': get_system_info(),
            'configurations_tested': len(self.config.test_configurations),
            'data_sizes_tested': self.config.test_data_sizes,
            'total_runs': len(all_results),
            'results': all_results
        }
        
        # Save results
        self.save_results(benchmark_summary)
        
        logger.info("[SUCCESS] Comprehensive benchmark completed")
        return benchmark_summary
    
    def average_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Average multiple benchmark results"""
        if not results or not all(r['success'] for r in results):
            return results[0] if results else None
        
        # Average timing metrics
        avg_timing = {}
        for key in results[0]['timing']:
            values = [r['timing'][key] for r in results]
            avg_timing[key] = np.mean(values)
        
        # Average performance metrics
        avg_performance = {}
        for key in results[0]['performance']:
            values = [r['performance'][key] for r in results]
            avg_performance[key] = np.mean(values)
        
        # Use first result as template and update with averages
        avg_result = results[0].copy()
        avg_result['timing'] = avg_timing
        avg_result['performance'] = avg_performance
        avg_result['runs_averaged'] = len(results)
        
        return avg_result
    
    def save_results(self, results: Dict[str, Any]) -> None:
        """Save benchmark results to file"""
        try:
            with open(self.config.results_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"💾 Benchmark results saved to {self.config.results_file}")
        
        except Exception as e:
            logger.error(f"Failed to save benchmark results: {str(e)}")
    
    def print_summary(self, results: Dict[str, Any]) -> None:
        """Print benchmark summary"""
        print("\n[RUNNING] BENCHMARK SUMMARY")
        print("="*60)
        print(f"System: {results['system_info']['cpu_count']} cores, "
              f"{results['system_info']['memory_total_gb']:.1f}GB RAM")
        print(f"GPU: {'Available' if results['system_info']['gpu_available'] else 'Not Available'}")
        print(f"Configurations tested: {results['configurations_tested']}")
        print(f"Data sizes: {results['data_sizes_tested']}")
        
        # Group results by configuration
        config_groups = {}
        for result in results['results']:
            config_name = result['config_name']
            if config_name not in config_groups:
                config_groups[config_name] = []
            config_groups[config_name].append(result)
        
        # Print results for each configuration
        for config_name, config_results in config_groups.items():
            print(f"\n[STATUS] {config_name.upper()}")
            print("-" * 40)
            
            for result in config_results:
                if result['success']:
                    print(f"  {result['data_size']:5d} samples: "
                          f"{result['timing']['total_time_seconds']:6.1f}s, "
                          f"R²={result['performance']['overall_r2']:5.3f}, "
                          f"{result['timing']['samples_per_second']:6.0f} samples/s")
                else:
                    print(f"  {result['data_size']:5d} samples: FAILED")

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Run benchmark example"""
    
    # Create benchmark configuration
    config = BenchmarkConfig(
        test_data_sizes=[100, 500, 1000],  # Smaller sizes for demo
        benchmark_runs=2  # Fewer runs for demo
    )
    
    # Initialize benchmark
    benchmark = AITrainingBenchmark(config)
    
    print("[RUNNING] AI Training Agent Benchmark")
    print("="*50)
    
    # Run benchmark
    results = await benchmark.run_comprehensive_benchmark()
    
    # Print summary
    benchmark.print_summary(results)
    
    print(f"\n💾 Detailed results saved to: {config.results_file}")

if __name__ == "__main__":
    asyncio.run(main())
