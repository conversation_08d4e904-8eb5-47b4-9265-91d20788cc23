#!/usr/bin/env python3
"""
LLM Interface Agent <PERSON><PERSON>t

Comprehensive demonstration of the LLM Interface Agent capabilities including:
- Natural language query processing
- Model selection and routing
- Agent communication simulation
- Angel One API integration demo
- Interactive chat interface

Author: AI Assistant
Date: 2025-01-16
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.llm_interface_agent import LLMInterfaceAgent, QueryRequest, QueryResponse
from agents.model_selector import ModelSelector
from agents.query_router import QueryRouter

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class LLMInterfaceDemo:
    """Demo class for LLM Interface Agent"""
    
    def __init__(self):
        """Initialize demo"""
        self.agent = None
        self.selector = ModelSelector()
        self.router = QueryRouter()
        
    async def run_full_demo(self):
        """Run complete demonstration"""
        print("[INIT] LLM Interface Agent - Comprehensive Demo")
        print("=" * 60)
        
        # Demo 1: Model Selection
        await self.demo_model_selection()
        
        # Demo 2: Query Routing
        await self.demo_query_routing()
        
        # Demo 3: Agent Integration (Simulated)
        await self.demo_agent_integration()
        
        # Demo 4: Angel One API Integration (Mock)
        await self.demo_angel_one_integration()
        
        # Demo 5: Interactive Chat
        await self.demo_interactive_chat()
        
        print("\n🎉 Demo completed successfully!")
    
    async def demo_model_selection(self):
        """Demonstrate model selection capabilities"""
        print("\n🧠 Model Selection Demo")
        print("-" * 40)
        
        test_queries = [
            ("Generate a new RSI scalping strategy", "Code Generation"),
            ("Explain how MACD indicator works", "Code Explanation"),
            ("Fix error in my Python trading script", "Bug Fixing"),
            ("What's my portfolio performance?", "General Reasoning"),
            ("Quick system status check", "Quick Response")
        ]
        
        for query, expected_task in test_queries:
            model, confidence, metadata = self.selector.select_model(query)
            
            print(f"\n📝 Query: {query}")
            print(f"[TARGET] Selected Model: {model}")
            print(f"[STATUS] Confidence: {confidence:.2f}")
            print(f"🏷️ Task Type: {metadata['task_type']}")
            print(f"🌡️ Temperature: {metadata['recommended_temperature']:.2f}")
            print(f"📏 Max Tokens: {metadata['recommended_max_tokens']}")
    
    async def demo_query_routing(self):
        """Demonstrate query routing capabilities"""
        print("\n🔀 Query Routing Demo")
        print("-" * 40)
        
        test_queries = [
            "What's the ROI of Donchian strategy on RELIANCE last week?",
            "Buy 100 shares of ADANIPORTS at current market price",
            "Show me current market regime and active signals",
            "What's my portfolio status and available margin?",
            "Generate momentum breakout signals for NIFTY 50 stocks",
            "Fix the YAML configuration error in RSI strategy",
            "Explain the SuperTrend calculation algorithm",
            "System health check - are all agents running?"
        ]
        
        for query in test_queries:
            decision = self.router.route_query(query)
            
            print(f"\n📝 Query: {query}")
            print(f"[TARGET] Agent: {decision.agent_name}")
            print(f"[STATUS] Confidence: {decision.confidence:.2f}")
            print(f"🧠 Intent: {decision.intent_category.value}")
            print(f"🏷️ Query Type: {decision.query_type.value}")
            print(f"[FAST] Real-time: {decision.requires_real_time}")
            print(f"[CONFIG] Complexity: {decision.estimated_complexity:.2f}")
            
            # Show extracted entities
            entities = decision.extracted_entities
            if entities.get('symbols'):
                print(f"[METRICS] Symbols: {entities['symbols']}")
            if entities.get('strategies'):
                print(f"[TARGET] Strategies: {entities['strategies']}")
            if entities.get('indicators'):
                print(f"[STATUS] Indicators: {entities['indicators']}")
    
    async def demo_agent_integration(self):
        """Demonstrate agent integration (simulated)"""
        print("\n[AGENT] Agent Integration Demo (Simulated)")
        print("-" * 40)
        
        # Simulate different agent responses
        agent_responses = {
            "performance_analysis_agent": {
                "query": "Show me RSI strategy performance",
                "response": """[STATUS] **RSI Strategy Performance Analysis**
                
• Total ROI: +12.5% (Last 30 days)
• Win Rate: 68.2%
• Sharpe Ratio: 1.45
• Max Drawdown: -3.2%
• Total Trades: 47
• Average Trade Duration: 2.3 hours

**Top Performing Symbols:**
• RELIANCE: +15.2% ROI
• TCS: +11.8% ROI
• HDFC: +9.4% ROI"""
            },
            
            "market_monitoring_agent": {
                "query": "Current market regime analysis",
                "response": """[METRICS] **Market Regime Analysis**
                
• Current Regime: **BULLISH MOMENTUM**
• Confidence: 78%
• Trend Strength: Strong
• Volatility: Medium (VIX: 16.2)

**Key Indicators:**
• NIFTY 50: 22,150 (+0.8%)
• RSI: 65.2 (Bullish but not overbought)
• MACD: Positive crossover
• Volume: Above 20-day average

**Active Signals:** 12 momentum breakout signals detected"""
            },
            
            "execution_agent": {
                "query": "Order execution status",
                "response": """⚙️ **Order Execution Status**
                
**Today's Activity:**
• Total Orders: 8
• Executed: 6 [SUCCESS]
• Pending: 1 ⏳
• Cancelled: 1 [ERROR]

**Recent Executions:**
• ADANIPORTS BUY 100 @ Rs.1,245 (11:30 AM) [SUCCESS]
• TATASTEEL SELL 50 @ Rs.890 (10:45 AM) [SUCCESS]
• RELIANCE BUY 25 @ Rs.2,650 (09:30 AM) [SUCCESS]

**Execution Quality:**
• Average Fill Time: 1.2 seconds
• Slippage: 0.02% (Excellent)"""
            }
        }
        
        for agent_name, demo_data in agent_responses.items():
            print(f"\n[AGENT] Agent: {agent_name}")
            print(f"📝 Query: {demo_data['query']}")
            print(f"[LIST] Response:\n{demo_data['response']}")
    
    async def demo_angel_one_integration(self):
        """Demonstrate Angel One API integration (mock)"""
        print("\n[CONNECT] Angel One API Integration Demo (Mock)")
        print("-" * 40)
        
        api_queries = [
            ("What's my portfolio status?", "portfolio"),
            ("Show me available margin", "margin"),
            ("Today's trade summary", "trades"),
            ("Current price of RELIANCE", "market_data")
        ]
        
        mock_responses = {
            "portfolio": """💼 **Portfolio Status**
            
• Total Value: Rs.2,45,000
• Day's P&L: +Rs.3,250 (*****%)
• Unrealized P&L: +Rs.12,500 (*****%)
• Active Positions: 3

**Holdings:**
• RELIANCE: 50 shares @ Rs.2,650 (+2.5%)
• TCS: 25 shares @ Rs.3,200 (+1.8%)
• HDFC: 40 shares @ Rs.1,450 (-0.5%)""",
            
            "margin": """[MONEY] **Margin Status**
            
• Available Cash: Rs.1,85,000
• Used Margin: Rs.65,000
• Total Margin: Rs.2,50,000
• Utilization: 26%
• Intraday Leverage: 3.5x""",
            
            "trades": """[STATUS] **Today's Trades**
            
• Executed Orders: 6
• Total Turnover: Rs.3,25,000
• Realized P&L: +Rs.2,150
• Brokerage: Rs.45
• Net P&L: +Rs.2,105""",
            
            "market_data": """[METRICS] **RELIANCE Market Data**
            
• LTP: Rs.2,650.50
• Change: +Rs.25.75 (+0.98%)
• Volume: 1,25,000
• High/Low: Rs.2,665/Rs.2,635
• VWAP: Rs.2,648.20"""
        }
        
        for query, api_type in api_queries:
            print(f"\n📝 Query: {query}")
            print(f"[CONNECT] API Call: {api_type}")
            print(f"[LIST] Response:\n{mock_responses[api_type]}")
    
    async def demo_interactive_chat(self):
        """Demonstrate interactive chat capabilities"""
        print("\n[COMM] Interactive Chat Demo")
        print("-" * 40)
        print("Type your queries below (or 'quit' to exit):")
        print("Examples:")
        print("  • 'What's the ROI of my momentum strategy?'")
        print("  • 'Generate a new scalping strategy'")
        print("  • 'Show me RELIANCE price and indicators'")
        print("  • 'Fix error in MACD calculation'")
        print()
        
        # Simulate some interactive queries
        demo_queries = [
            "What's the best performing strategy this week?",
            "Generate a VWAP-based scalping strategy",
            "Show me current market volatility",
            "Fix the RSI threshold configuration"
        ]
        
        for i, query in enumerate(demo_queries, 1):
            print(f"User {i}: {query}")
            
            # Route the query
            decision = self.router.route_query(query)
            
            # Select appropriate model
            model, confidence, metadata = self.selector.select_model(query)
            
            # Generate simulated response
            response = self._generate_demo_response(query, decision, model)
            
            print(f"[AGENT] Assistant: {response}")
            print(f"   (Agent: {decision.agent_name}, Model: {model}, Confidence: {confidence:.2f})")
            print()
    
    def _generate_demo_response(self, query: str, decision, model: str) -> str:
        """Generate demo response based on query and routing"""
        
        if "performing strategy" in query.lower():
            return """[STATUS] **Best Performing Strategy This Week**
            
The **EMA Crossover Momentum** strategy is leading with:
• ROI: +8.7%
• Win Rate: 72%
• 23 successful trades
• Best on RELIANCE (+12%) and TCS (+9%)"""
        
        elif "generate" in query.lower() and "vwap" in query.lower():
            return """[CONFIG] **VWAP Scalping Strategy Generated**
            
```python
def vwap_scalping_strategy(data):
    # Entry: Price crosses above VWAP with volume spike
    entry_long = (data['close'] > data['vwap']) & \\
                 (data['volume'] > data['volume'].rolling(20).mean() * 1.5)
    
    # Exit: Price touches VWAP or 0.5% profit
    exit_conditions = (data['close'] <= data['vwap']) | \\
                     (data['close'] >= data['entry_price'] * 1.005)
    
    return entry_long, exit_conditions
```

**Recommended Parameters:**
• Timeframe: 5-minute
• Volume threshold: 1.5x average
• Profit target: 0.5%
• Stop loss: VWAP level"""
        
        elif "volatility" in query.lower():
            return """[STATUS] **Current Market Volatility Analysis**
            
• VIX Level: 16.2 (Medium volatility)
• NIFTY ATR: 145 points
• Intraday Range: 1.2% average
• Volatility Trend: Decreasing (Good for momentum strategies)

**Volatility by Sector:**
• Banking: High (2.1%)
• IT: Low (0.8%)
• Auto: Medium (1.5%)"""
        
        elif "fix" in query.lower() and "rsi" in query.lower():
            return """[CONFIG] **RSI Configuration Fix**
            
**Issue Found:** RSI threshold too restrictive
**Current:** RSI > 70 for overbought
**Recommended:** RSI > 65 for better signal frequency

**Updated Configuration:**
```yaml
rsi_strategy:
  overbought_threshold: 65
  oversold_threshold: 35
  period: 14
```

**Expected Improvement:** +25% more signals, similar accuracy"""
        
        else:
            return f"I understand you're asking about: {query}. Let me route this to the {decision.agent_name} for detailed analysis."

async def main():
    """Main demo function"""
    demo = LLMInterfaceDemo()
    
    try:
        await demo.run_full_demo()
    except KeyboardInterrupt:
        print("\n[EXIT] Demo interrupted by user")
    except Exception as e:
        print(f"\n[ERROR] Demo error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
