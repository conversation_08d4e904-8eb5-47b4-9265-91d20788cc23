"""
Tests for Feature 9: Signal Debugging & Replay
"""
import pytest
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock, AsyncMock

from agents.enhanced_backtesting_polars import SignalAnalyzer


class TestSignalAnalyzer:
    """Test suite for SignalAnalyzer class"""
    
    @pytest.fixture
    def analyzer(self):
        """Create SignalAnalyzer instance"""
        return SignalAnalyzer()
    
    @pytest.mark.asyncio
    async def test_initialization(self, analyzer):
        """Test proper initialization of SignalAnalyzer"""
        assert analyzer.signal_history == []
        assert analyzer.replay_state == {}
        assert analyzer.debug_mode == False
    
    @pytest.mark.asyncio
    async def test_analyze_signal_quality(self, analyzer, sample_trades, sample_market_data):
        """Test signal quality analysis"""
        signal_quality = await analyzer.analyze_signal_quality(
            sample_trades, sample_market_data
        )
        
        assert 'signal_to_noise_ratio' in signal_quality
        assert 'signal_consistency' in signal_quality
        assert 'false_signal_rate' in signal_quality
        assert 'signal_timing_accuracy' in signal_quality
        assert 'overall_quality_score' in signal_quality
        
        # Check value ranges
        assert 0 <= signal_quality['signal_consistency'] <= 1
        assert 0 <= signal_quality['false_signal_rate'] <= 1
        assert 0 <= signal_quality['overall_quality_score'] <= 1
    
    @pytest.mark.asyncio
    async def test_calculate_signal_to_noise_ratio(self, analyzer, sample_trades):
        """Test signal-to-noise ratio calculation"""
        snr = await analyzer._calculate_signal_to_noise_ratio(sample_trades)
        
        assert isinstance(snr, float)
        assert snr >= 0  # SNR should be non-negative
    
    @pytest.mark.asyncio
    async def test_detect_false_signals(self, analyzer, sample_trades, sample_market_data):
        """Test false signal detection"""
        false_signals = await analyzer.detect_false_signals(
            sample_trades, sample_market_data, min_profit_threshold=0.5
        )
        
        assert 'false_signal_trades' in false_signals
        assert 'false_signal_rate' in false_signals
        assert 'common_false_signal_patterns' in false_signals
        
        assert isinstance(false_signals['false_signal_trades'], list)
        assert 0 <= false_signals['false_signal_rate'] <= 1
    
    @pytest.mark.asyncio
    async def test_analyze_signal_timing(self, analyzer, sample_trades, sample_market_data):
        """Test signal timing analysis"""
        timing_analysis = await analyzer.analyze_signal_timing(
            sample_trades, sample_market_data
        )
        
        assert 'entry_timing_accuracy' in timing_analysis
        assert 'exit_timing_accuracy' in timing_analysis
        assert 'optimal_entry_delay' in timing_analysis
        assert 'optimal_exit_delay' in timing_analysis
        assert 'timing_improvement_potential' in timing_analysis
    
    @pytest.mark.asyncio
    async def test_signal_clustering_analysis(self, analyzer, sample_trades):
        """Test signal clustering analysis"""
        clustering_result = await analyzer.analyze_signal_clustering(
            sample_trades, features=['entry_price', 'volume', 'volatility']
        )
        
        assert 'signal_clusters' in clustering_result
        assert 'cluster_performance' in clustering_result
        assert 'optimal_cluster_count' in clustering_result
        assert 'cluster_characteristics' in clustering_result
    
    @pytest.mark.asyncio
    async def test_signal_persistence_analysis(self, analyzer, sample_trades):
        """Test signal persistence analysis"""
        persistence = await analyzer.analyze_signal_persistence(sample_trades)
        
        assert 'average_signal_duration' in persistence
        assert 'signal_decay_rate' in persistence
        assert 'persistence_score' in persistence
        assert 'trend_following_effectiveness' in persistence
    
    @pytest.mark.asyncio
    async def test_replay_trade_execution(self, analyzer, sample_trades, sample_market_data):
        """Test trade execution replay"""
        trade_index = 0
        replay_result = await analyzer.replay_trade_execution(
            sample_trades, sample_market_data, trade_index
        )
        
        assert 'trade_details' in replay_result
        assert 'market_conditions' in replay_result
        assert 'signal_strength' in replay_result
        assert 'execution_steps' in replay_result
        assert 'alternative_outcomes' in replay_result
    
    @pytest.mark.asyncio
    async def test_step_by_step_debugging(self, analyzer, sample_strategy, sample_market_data):
        """Test step-by-step debugging functionality"""
        debug_session = await analyzer.start_debug_session(
            sample_strategy, sample_market_data
        )
        
        assert 'session_id' in debug_session
        assert 'current_step' in debug_session
        assert 'total_steps' in debug_session
        assert 'debug_state' in debug_session
        
        # Test stepping through
        next_step = await analyzer.debug_step_forward(debug_session['session_id'])
        assert 'step_info' in next_step
        assert 'market_data' in next_step
        assert 'signal_values' in next_step
    
    @pytest.mark.asyncio
    async def test_signal_strength_analysis(self, analyzer, sample_trades, sample_market_data):
        """Test signal strength analysis"""
        strength_analysis = await analyzer.analyze_signal_strength(
            sample_trades, sample_market_data
        )
        
        assert 'average_signal_strength' in strength_analysis
        assert 'signal_strength_distribution' in strength_analysis
        assert 'strength_vs_performance_correlation' in strength_analysis
        assert 'optimal_strength_threshold' in strength_analysis
    
    @pytest.mark.asyncio
    async def test_identify_signal_patterns(self, analyzer, sample_trades):
        """Test signal pattern identification"""
        patterns = await analyzer.identify_signal_patterns(sample_trades)
        
        assert 'common_patterns' in patterns
        assert 'pattern_success_rates' in patterns
        assert 'rare_patterns' in patterns
        assert 'pattern_recommendations' in patterns
    
    @pytest.mark.asyncio
    async def test_signal_correlation_analysis(self, analyzer, sample_trades):
        """Test signal correlation analysis"""
        correlation_analysis = await analyzer.analyze_signal_correlations(
            sample_trades, indicators=['rsi', 'sma', 'volume']
        )
        
        assert 'correlation_matrix' in correlation_analysis
        assert 'redundant_signals' in correlation_analysis
        assert 'complementary_signals' in correlation_analysis
        assert 'signal_independence_score' in correlation_analysis
    
    @pytest.mark.asyncio
    async def test_signal_optimization_suggestions(self, analyzer, sample_trades, sample_market_data):
        """Test signal optimization suggestions"""
        suggestions = await analyzer.generate_optimization_suggestions(
            sample_trades, sample_market_data
        )
        
        assert 'parameter_adjustments' in suggestions
        assert 'filter_recommendations' in suggestions
        assert 'timing_improvements' in suggestions
        assert 'risk_management_suggestions' in suggestions
        assert 'expected_improvement' in suggestions
    
    @pytest.mark.asyncio
    async def test_signal_robustness_testing(self, analyzer, sample_strategy, sample_market_data):
        """Test signal robustness testing"""
        robustness_result = await analyzer.test_signal_robustness(
            sample_strategy, sample_market_data, noise_levels=[0.01, 0.02, 0.05]
        )
        
        assert 'robustness_scores' in robustness_result
        assert 'performance_degradation' in robustness_result
        assert 'noise_sensitivity' in robustness_result
        assert 'robustness_ranking' in robustness_result
    
    @pytest.mark.asyncio
    async def test_signal_frequency_analysis(self, analyzer, sample_trades):
        """Test signal frequency analysis"""
        frequency_analysis = await analyzer.analyze_signal_frequency(sample_trades)
        
        assert 'signals_per_day' in frequency_analysis
        assert 'signal_frequency_distribution' in frequency_analysis
        assert 'optimal_frequency_range' in frequency_analysis
        assert 'overtrading_risk' in frequency_analysis
    
    @pytest.mark.asyncio
    async def test_market_regime_signal_performance(self, analyzer, sample_trades, sample_market_data):
        """Test signal performance across market regimes"""
        regime_performance = await analyzer.analyze_regime_signal_performance(
            sample_trades, sample_market_data
        )
        
        assert 'bull_market_performance' in regime_performance
        assert 'bear_market_performance' in regime_performance
        assert 'sideways_market_performance' in regime_performance
        assert 'regime_adaptability' in regime_performance
    
    @pytest.mark.asyncio
    async def test_signal_latency_analysis(self, analyzer, sample_trades, sample_market_data):
        """Test signal latency analysis"""
        latency_analysis = await analyzer.analyze_signal_latency(
            sample_trades, sample_market_data
        )
        
        assert 'average_signal_delay' in latency_analysis
        assert 'delay_impact_on_performance' in latency_analysis
        assert 'optimal_execution_timing' in latency_analysis
        assert 'latency_cost_analysis' in latency_analysis
    
    @pytest.mark.asyncio
    async def test_signal_validation_framework(self, analyzer, sample_strategy, sample_market_data):
        """Test signal validation framework"""
        validation_result = await analyzer.validate_signal_framework(
            sample_strategy, sample_market_data
        )
        
        assert 'validation_score' in validation_result
        assert 'statistical_significance' in validation_result
        assert 'consistency_check' in validation_result
        assert 'overfitting_risk' in validation_result
        assert 'validation_recommendations' in validation_result
    
    @pytest.mark.asyncio
    async def test_interactive_signal_explorer(self, analyzer, sample_trades, sample_market_data):
        """Test interactive signal explorer"""
        explorer_session = await analyzer.start_signal_explorer(
            sample_trades, sample_market_data
        )
        
        assert 'session_id' in explorer_session
        assert 'available_filters' in explorer_session
        assert 'signal_timeline' in explorer_session
        assert 'interactive_controls' in explorer_session
    
    @pytest.mark.asyncio
    async def test_signal_attribution_analysis(self, analyzer, sample_trades):
        """Test signal attribution analysis"""
        attribution = await analyzer.analyze_signal_attribution(
            sample_trades, signal_components=['trend', 'momentum', 'mean_reversion']
        )
        
        assert 'component_contributions' in attribution
        assert 'component_correlations' in attribution
        assert 'attribution_stability' in attribution
        assert 'component_importance_ranking' in attribution
    
    @pytest.mark.asyncio
    async def test_signal_decay_analysis(self, analyzer, sample_trades):
        """Test signal decay analysis"""
        decay_analysis = await analyzer.analyze_signal_decay(sample_trades)
        
        assert 'decay_rate' in decay_analysis
        assert 'half_life' in decay_analysis
        assert 'optimal_holding_period' in decay_analysis
        assert 'decay_pattern' in decay_analysis
    
    @pytest.mark.asyncio
    async def test_error_handling_empty_signals(self, analyzer):
        """Test error handling for empty signal data"""
        empty_trades = []
        
        with pytest.raises(ValueError, match="No signal data provided"):
            await analyzer.analyze_signal_quality(empty_trades, None)
    
    @pytest.mark.asyncio
    async def test_signal_memory_management(self, analyzer):
        """Test signal memory management for large datasets"""
        # Create large signal dataset
        large_trades = []
        for i in range(10000):
            large_trades.append({
                'entry_time': datetime(2023, 1, 1) + timedelta(minutes=i),
                'signal_strength': np.random.random(),
                'pnl_pct': np.random.normal(0, 1)
            })
        
        # Should handle large dataset efficiently
        quality_result = await analyzer.analyze_signal_quality(large_trades, None)
        
        assert 'signal_to_noise_ratio' in quality_result
        assert quality_result['signal_to_noise_ratio'] is not None
    
    @pytest.mark.asyncio
    async def test_concurrent_signal_analysis(self, analyzer, sample_trades, sample_market_data):
        """Test concurrent signal analysis operations"""
        import asyncio
        
        # Run multiple analysis operations concurrently
        tasks = [
            analyzer.analyze_signal_quality(sample_trades, sample_market_data),
            analyzer.analyze_signal_timing(sample_trades, sample_market_data),
            analyzer.analyze_signal_strength(sample_trades, sample_market_data)
        ]
        
        results = await asyncio.gather(*tasks)
        
        # All operations should complete successfully
        assert len(results) == 3
        assert all('signal_to_noise_ratio' in result or 'entry_timing_accuracy' in result 
                  or 'average_signal_strength' in result for result in results)
