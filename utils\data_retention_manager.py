#!/usr/bin/env python3
"""
Data Retention Manager
Intelligent data lifecycle management for trading system
"""

import os
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import polars as pl
import yaml
import json

logger = logging.getLogger(__name__)

class DataRetentionManager:
    """Manages data lifecycle and retention policies"""
    
    def __init__(self, config_path: str = "config/data_retention_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.retention_log = []
    
    def _load_config(self) -> Dict:
        """Load data retention configuration"""
        default_config = {
            'retention_policies': {
                'historical_data': {
                    'keep_days': 90,  # Keep 3 months of historical data
                    'archive_after_days': 30,  # Archive after 1 month
                    'compress_archives': True
                },
                'feature_data': {
                    'keep_days': 60,  # Keep 2 months of feature data
                    'archive_after_days': 21,  # Archive after 3 weeks
                    'compress_archives': True
                },
                'backtest_results': {
                    'keep_days': 180,  # Keep 6 months of backtest results
                    'archive_after_days': 60,  # Archive after 2 months
                    'compress_archives': True
                },
                'model_data': {
                    'keep_versions': 10,  # Keep last 10 model versions
                    'archive_old_versions': True,
                    'compress_archives': True
                },
                'logs': {
                    'keep_days': 30,  # Keep 1 month of logs
                    'archive_after_days': 7,  # Archive after 1 week
                    'compress_archives': True
                }
            },
            'directories': {
                'historical_data': 'data/historical',
                'feature_data': 'data/features',
                'backtest_results': 'data/backtest',
                'model_data': 'data/models',
                'logs': 'logs',
                'archive_base': 'data/archives'
            },
            'auto_cleanup': {
                'enabled': True,
                'schedule': 'weekly',  # daily, weekly, monthly
                'dry_run': False  # Set to True to see what would be deleted
            }
        }
        
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    config = yaml.safe_load(f)
                # Merge with defaults
                return {**default_config, **config}
            else:
                # Create default config file
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                with open(self.config_path, 'w') as f:
                    yaml.dump(default_config, f, default_flow_style=False)
                logger.info(f"📄 Created default retention config: {self.config_path}")
                return default_config
        except Exception as e:
            logger.warning(f"[WARN] Error loading retention config: {e}, using defaults")
            return default_config
    
    def analyze_data_usage(self) -> Dict[str, Dict]:
        """Analyze current data usage and provide recommendations"""
        analysis = {}
        
        for data_type, directory in self.config['directories'].items():
            if data_type == 'archive_base':
                continue
                
            dir_path = Path(directory)
            if not dir_path.exists():
                continue
            
            files_info = []
            total_size = 0
            
            for file_path in dir_path.rglob('*'):
                if file_path.is_file():
                    stat = file_path.stat()
                    age_days = (datetime.now() - datetime.fromtimestamp(stat.st_mtime)).days
                    size_mb = stat.st_size / (1024 * 1024)
                    
                    files_info.append({
                        'path': str(file_path),
                        'size_mb': size_mb,
                        'age_days': age_days,
                        'last_modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
                    total_size += size_mb
            
            # Sort by age (oldest first)
            files_info.sort(key=lambda x: x['age_days'], reverse=True)
            
            policy = self.config['retention_policies'].get(data_type, {})
            keep_days = policy.get('keep_days', 90)
            archive_days = policy.get('archive_after_days', 30)
            
            # Categorize files
            to_delete = [f for f in files_info if f['age_days'] > keep_days]
            to_archive = [f for f in files_info if archive_days < f['age_days'] <= keep_days]
            to_keep = [f for f in files_info if f['age_days'] <= archive_days]
            
            analysis[data_type] = {
                'directory': directory,
                'total_files': len(files_info),
                'total_size_mb': round(total_size, 2),
                'total_size_gb': round(total_size / 1024, 2),
                'files_to_delete': len(to_delete),
                'files_to_archive': len(to_archive),
                'files_to_keep': len(to_keep),
                'delete_size_mb': round(sum(f['size_mb'] for f in to_delete), 2),
                'archive_size_mb': round(sum(f['size_mb'] for f in to_archive), 2),
                'policy': policy,
                'oldest_file_days': max([f['age_days'] for f in files_info]) if files_info else 0,
                'recommendations': self._generate_recommendations(data_type, files_info, policy)
            }
        
        return analysis
    
    def _generate_recommendations(self, data_type: str, files_info: List[Dict], policy: Dict) -> List[str]:
        """Generate recommendations for data management"""
        recommendations = []
        
        if not files_info:
            return recommendations
        
        total_size_gb = sum(f['size_mb'] for f in files_info) / 1024
        oldest_age = max(f['age_days'] for f in files_info)
        keep_days = policy.get('keep_days', 90)
        
        # Size-based recommendations
        if total_size_gb > 10:
            recommendations.append(f"[STATUS] Large dataset ({total_size_gb:.1f}GB) - consider archiving older files")
        
        # Age-based recommendations
        if oldest_age > keep_days * 2:
            recommendations.append(f"🗓️ Very old data found ({oldest_age} days) - cleanup recommended")
        
        # Data type specific recommendations
        if data_type == 'historical_data':
            if total_size_gb > 5:
                recommendations.append("[INFO] Consider keeping only essential timeframes (5min, 15min)")
        elif data_type == 'feature_data':
            if len(files_info) > 20:
                recommendations.append("[INFO] Multiple feature files - consider consolidation")
        elif data_type == 'backtest_results':
            if len(files_info) > 50:
                recommendations.append("[INFO] Many backtest files - archive old results")
        
        return recommendations
    
    def cleanup_old_data(self, dry_run: bool = None) -> Dict[str, List[str]]:
        """Clean up old data based on retention policies"""
        if dry_run is None:
            dry_run = self.config['auto_cleanup'].get('dry_run', False)
        
        cleanup_results = {
            'deleted': [],
            'archived': [],
            'errors': [],
            'space_freed_mb': 0
        }
        
        analysis = self.analyze_data_usage()
        
        for data_type, info in analysis.items():
            policy = info['policy']
            directory = Path(info['directory'])
            
            if not directory.exists():
                continue
            
            keep_days = policy.get('keep_days', 90)
            archive_days = policy.get('archive_after_days', 30)
            
            for file_path in directory.rglob('*'):
                if not file_path.is_file():
                    continue
                
                age_days = (datetime.now() - datetime.fromtimestamp(file_path.stat().st_mtime)).days
                size_mb = file_path.stat().st_size / (1024 * 1024)
                
                try:
                    if age_days > keep_days:
                        # Delete old files
                        if dry_run:
                            cleanup_results['deleted'].append(f"[DRY RUN] Would delete: {file_path}")
                        else:
                            file_path.unlink()
                            cleanup_results['deleted'].append(str(file_path))
                            cleanup_results['space_freed_mb'] += size_mb
                            logger.info(f"🗑️ Deleted old file: {file_path}")
                    
                    elif age_days > archive_days:
                        # Archive files
                        archive_path = self._get_archive_path(file_path, data_type)
                        if dry_run:
                            cleanup_results['archived'].append(f"[DRY RUN] Would archive: {file_path} → {archive_path}")
                        else:
                            self._archive_file(file_path, archive_path, policy.get('compress_archives', True))
                            cleanup_results['archived'].append(f"{file_path} → {archive_path}")
                            logger.info(f"📦 Archived file: {file_path}")
                
                except Exception as e:
                    error_msg = f"Error processing {file_path}: {e}"
                    cleanup_results['errors'].append(error_msg)
                    logger.error(error_msg)
        
        # Log cleanup results
        self.retention_log.append({
            'timestamp': datetime.now().isoformat(),
            'action': 'cleanup',
            'dry_run': dry_run,
            'results': cleanup_results
        })
        
        return cleanup_results
    
    def _get_archive_path(self, file_path: Path, data_type: str) -> Path:
        """Generate archive path for a file"""
        archive_base = Path(self.config['directories']['archive_base'])
        
        # Create archive structure: archives/data_type/year/month/
        file_date = datetime.fromtimestamp(file_path.stat().st_mtime)
        archive_dir = archive_base / data_type / str(file_date.year) / f"{file_date.month:02d}"
        archive_dir.mkdir(parents=True, exist_ok=True)
        
        return archive_dir / file_path.name
    
    def _archive_file(self, source_path: Path, archive_path: Path, compress: bool = True):
        """Archive a file with optional compression"""
        if compress and source_path.suffix in ['.parquet', '.csv']:
            # For data files, we can compress them
            if source_path.suffix == '.parquet':
                # Re-save parquet with better compression
                df = pl.read_parquet(source_path)
                df.write_parquet(archive_path, compression='brotli')
            else:
                # For other files, just copy
                shutil.copy2(source_path, archive_path)
        else:
            # Regular copy
            shutil.copy2(source_path, archive_path)
        
        # Remove original file after successful archive
        source_path.unlink()
    
    def get_retention_summary(self) -> Dict:
        """Get summary of data retention status"""
        analysis = self.analyze_data_usage()
        
        total_size_gb = sum(info['total_size_gb'] for info in analysis.values())
        total_files = sum(info['total_files'] for info in analysis.values())
        total_to_delete = sum(info['files_to_delete'] for info in analysis.values())
        total_to_archive = sum(info['files_to_archive'] for info in analysis.values())
        
        return {
            'total_data_size_gb': round(total_size_gb, 2),
            'total_files': total_files,
            'files_to_delete': total_to_delete,
            'files_to_archive': total_to_archive,
            'potential_space_savings_gb': round(sum(info['delete_size_mb'] for info in analysis.values()) / 1024, 2),
            'data_types': list(analysis.keys()),
            'last_cleanup': self.retention_log[-1]['timestamp'] if self.retention_log else 'Never',
            'recommendations': self._get_global_recommendations(analysis)
        }
    
    def _get_global_recommendations(self, analysis: Dict) -> List[str]:
        """Get global recommendations for data management"""
        recommendations = []
        
        total_size_gb = sum(info['total_size_gb'] for info in analysis.values())
        
        if total_size_gb > 50:
            recommendations.append("🚨 Large data footprint (>50GB) - immediate cleanup recommended")
        elif total_size_gb > 20:
            recommendations.append("[WARN] Moderate data usage (>20GB) - consider regular cleanup")
        
        old_data_types = [dt for dt, info in analysis.items() if info['oldest_file_days'] > 180]
        if old_data_types:
            recommendations.append(f"📅 Very old data in: {', '.join(old_data_types)}")
        
        if not self.config['auto_cleanup']['enabled']:
            recommendations.append("[AGENT] Enable auto-cleanup for automated data management")
        
        return recommendations

def main():
    """Example usage of Data Retention Manager"""
    logging.basicConfig(level=logging.INFO)
    
    manager = DataRetentionManager()
    
    print("[STATUS] Data Retention Analysis")
    print("=" * 50)
    
    # Get analysis
    analysis = manager.analyze_data_usage()
    
    for data_type, info in analysis.items():
        print(f"\n[FOLDER] {data_type.upper()}")
        print(f"   Directory: {info['directory']}")
        print(f"   Total files: {info['total_files']}")
        print(f"   Total size: {info['total_size_gb']:.2f} GB")
        print(f"   Files to delete: {info['files_to_delete']}")
        print(f"   Files to archive: {info['files_to_archive']}")
        
        if info['recommendations']:
            print(f"   Recommendations:")
            for rec in info['recommendations']:
                print(f"     • {rec}")
    
    # Get summary
    summary = manager.get_retention_summary()
    print(f"\n[LIST] SUMMARY")
    print(f"   Total data: {summary['total_data_size_gb']} GB")
    print(f"   Potential savings: {summary['potential_space_savings_gb']} GB")
    
    if summary['recommendations']:
        print(f"   Global recommendations:")
        for rec in summary['recommendations']:
            print(f"     • {rec}")

if __name__ == "__main__":
    main()
