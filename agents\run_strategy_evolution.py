#!/usr/bin/env python3
"""
Strategy Evolution Agent Runner

This script provides a command-line interface for running the Strategy Evolution Agent
with various options for testing, monitoring, and production deployment.

Features:
[INIT] Start/stop agent operations
[STATUS] Monitor evolution progress
🧪 Run in simulation mode
[CONFIG] Configuration management
[METRICS] Performance reporting

Author: AI Assistant
Date: 2025-07-16
"""

import asyncio
import argparse
import logging
import signal
import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Optional

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from agents.strategy_evolution_agent import StrategyEvolutionAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("logs/strategy_evolution_runner.log")
    ]
)
logger = logging.getLogger(__name__)

class StrategyEvolutionRunner:
    """Runner for Strategy Evolution Agent"""
    
    def __init__(self, config_path: str = "config/strategy_evolution_config.yaml"):
        """Initialize runner"""
        self.config_path = config_path
        self.agent: Optional[StrategyEvolutionAgent] = None
        self.is_running = False
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"[STOP] Received signal {signum}, shutting down...")
        self.is_running = False
        
        if self.agent:
            asyncio.create_task(self.agent.stop())
    
    async def start_agent(self, simulation_mode: bool = False):
        """Start the Strategy Evolution Agent"""
        try:
            logger.info("[INIT] Starting Strategy Evolution Agent...")
            
            # Check dependencies
            await self._check_dependencies()
            
            # Check system resources
            await self._check_system_resources()
            
            # Create and setup agent
            self.agent = StrategyEvolutionAgent(self.config_path)
            
            if simulation_mode:
                logger.info("🧪 Running in simulation mode")
                # Disable actual trading connections in simulation mode
                self.agent.config['agents']['performance_analysis_agent']['enabled'] = False
                self.agent.config['agents']['market_monitoring_agent']['enabled'] = False
            
            await self.agent.setup()
            
            # Start monitoring
            self.is_running = True
            logger.info("[SUCCESS] Strategy Evolution Agent started successfully")
            
            # Run main loop
            await self._run_main_loop()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start Strategy Evolution Agent: {e}")
            raise
    
    async def _check_dependencies(self):
        """Check system dependencies"""
        logger.info("[DEBUG] Checking dependencies...")
        
        # Check required directories
        required_dirs = [
            "data/evolved_strategies",
            "data/evolution_performance", 
            "data/evolution_backups",
            "logs",
            "config"
        ]
        
        for directory in required_dirs:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.debug(f"[SUCCESS] Directory checked: {directory}")
        
        # Check configuration file
        if not Path(self.config_path).exists():
            logger.warning(f"[WARN] Config file not found: {self.config_path}")
            logger.info("📝 Using default configuration")
        
        logger.info("[SUCCESS] Dependencies check completed")
    
    async def _check_system_resources(self):
        """Check system resources"""
        logger.info("[SYSTEM] Checking system resources...")
        
        try:
            import psutil
            
            # Check memory
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            if memory_usage > 80:
                logger.warning(f"[WARN] High memory usage: {memory_usage:.1f}%")
            else:
                logger.info(f"[SUCCESS] Memory usage: {memory_usage:.1f}%")
            
            # Check disk space
            disk = psutil.disk_usage('.')
            disk_usage = (disk.used / disk.total) * 100
            
            if disk_usage > 90:
                logger.warning(f"[WARN] High disk usage: {disk_usage:.1f}%")
            else:
                logger.info(f"[SUCCESS] Disk usage: {disk_usage:.1f}%")
            
            # Check CPU
            cpu_usage = psutil.cpu_percent(interval=1)
            logger.info(f"[STATUS] CPU usage: {cpu_usage:.1f}%")
            
        except ImportError:
            logger.warning("[WARN] psutil not available, skipping resource check")
        
        logger.info("[SUCCESS] System resources check completed")
    
    async def _run_main_loop(self):
        """Run main monitoring loop"""
        try:
            # Start agent
            agent_task = asyncio.create_task(self.agent.start())
            
            # Start monitoring tasks
            monitoring_tasks = [
                self._status_monitoring_loop(),
                self._performance_reporting_loop()
            ]
            
            # Wait for completion
            await asyncio.gather(agent_task, *monitoring_tasks)
            
        except Exception as e:
            logger.error(f"[ERROR] Error in main loop: {e}")
            raise
    
    async def _status_monitoring_loop(self):
        """Monitor agent status"""
        while self.is_running:
            try:
                if self.agent:
                    stats = self.agent.get_evolution_statistics()
                    
                    logger.info(f"[STATUS] Evolution Status: Gen {stats['current_generation']}, "
                              f"Best Fitness: {stats['best_fitness']:.4f}, "
                              f"Avg Fitness: {stats['average_fitness']:.4f}, "
                              f"Population: {stats['population_size']}")
                
                await asyncio.sleep(300)  # Report every 5 minutes
                
            except Exception as e:
                logger.error(f"[ERROR] Error in status monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _performance_reporting_loop(self):
        """Generate periodic performance reports"""
        while self.is_running:
            try:
                if self.agent:
                    await self._generate_status_report()
                
                await asyncio.sleep(1800)  # Report every 30 minutes
                
            except Exception as e:
                logger.error(f"[ERROR] Error in performance reporting: {e}")
                await asyncio.sleep(300)
    
    async def _generate_status_report(self):
        """Generate detailed status report"""
        try:
            if not self.agent:
                return
            
            stats = self.agent.get_evolution_statistics()
            best_strategies = self.agent.get_best_strategies(count=5)
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'evolution_statistics': stats,
                'top_strategies': [
                    {
                        'name': strategy.strategy_name,
                        'fitness': strategy.fitness_score,
                        'generation': strategy.generation,
                        'age_hours': (datetime.now() - strategy.creation_timestamp).total_seconds() / 3600
                    }
                    for strategy in best_strategies
                ],
                'system_status': {
                    'agent_running': self.agent.is_running,
                    'evolution_enabled': self.agent.evolution_enabled,
                    'active_strategies': len(self.agent.active_strategies),
                    'performance_history_count': len(self.agent.strategy_performance_history)
                }
            }
            
            # Save report
            report_file = Path("data/evolution_performance") / f"status_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"[STATUS] Status report saved: {report_file}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error generating status report: {e}")
    
    async def stop_agent(self):
        """Stop the agent"""
        logger.info("[STOP] Stopping Strategy Evolution Agent...")
        self.is_running = False
        
        if self.agent:
            await self.agent.stop()
        
        logger.info("[SUCCESS] Strategy Evolution Agent stopped")

async def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description="Strategy Evolution Agent Runner")
    
    parser.add_argument(
        "--config",
        type=str,
        default="config/strategy_evolution_config.yaml",
        help="Configuration file path"
    )
    
    parser.add_argument(
        "--simulation",
        action="store_true",
        help="Run in simulation mode (no real trading connections)"
    )
    
    parser.add_argument(
        "--force-evolution",
        action="store_true",
        help="Force evolution of new generation on startup"
    )
    
    parser.add_argument(
        "--export-strategies",
        type=str,
        help="Export current strategies to file"
    )
    
    parser.add_argument(
        "--import-strategies",
        type=str,
        help="Import strategies from file"
    )
    
    parser.add_argument(
        "--status",
        action="store_true",
        help="Show current status and exit"
    )
    
    parser.add_argument(
        "--test",
        action="store_true",
        help="Run basic functionality test"
    )
    
    args = parser.parse_args()
    
    try:
        runner = StrategyEvolutionRunner(args.config)
        
        if args.test:
            await run_test(runner)
        elif args.status:
            await show_status(runner)
        elif args.export_strategies:
            await export_strategies(runner, args.export_strategies)
        elif args.import_strategies:
            await import_strategies(runner, args.import_strategies)
        else:
            # Normal operation
            await runner.start_agent(simulation_mode=args.simulation)
            
            if args.force_evolution and runner.agent:
                logger.info("[WORKFLOW] Forcing evolution...")
                await runner.agent.force_evolution()
    
    except KeyboardInterrupt:
        logger.info("[STOP] Received interrupt signal")
        if runner.agent:
            await runner.stop_agent()
    except Exception as e:
        logger.error(f"[ERROR] Error in main execution: {e}")
        sys.exit(1)

async def run_test(runner: StrategyEvolutionRunner):
    """Run basic functionality test"""
    logger.info("🧪 Running basic functionality test...")
    
    try:
        # Initialize agent
        runner.agent = StrategyEvolutionAgent(runner.config_path)
        
        # Test setup
        await runner.agent.setup()
        logger.info("[SUCCESS] Agent setup successful")
        
        # Test evolution statistics
        stats = runner.agent.get_evolution_statistics()
        logger.info(f"[STATUS] Evolution stats: {stats}")
        
        # Test strategy creation
        await runner.agent._create_initial_population()
        logger.info(f"[SUCCESS] Created initial population: {len(runner.agent.active_strategies)} strategies")
        
        # Test mutation
        if runner.agent.active_strategies:
            strategy = list(runner.agent.active_strategies.values())[0]
            mutated = runner.agent._mutate_chromosome(strategy)
            logger.info(f"[SUCCESS] Mutation test successful: {mutated.strategy_name}")
        
        logger.info("🎉 All tests passed!")
        
    except Exception as e:
        logger.error(f"[ERROR] Test failed: {e}")
        raise

async def show_status(runner: StrategyEvolutionRunner):
    """Show current status"""
    logger.info("[STATUS] Checking Strategy Evolution Agent status...")
    
    try:
        # Check if agent data exists
        strategies_dir = Path("data/evolved_strategies")
        performance_dir = Path("data/evolution_performance")
        
        if strategies_dir.exists():
            strategy_files = list(strategies_dir.glob("*.json"))
            logger.info(f"[FOLDER] Found {len(strategy_files)} strategy files")
        
        if performance_dir.exists():
            performance_files = list(performance_dir.glob("*.json"))
            logger.info(f"[FOLDER] Found {len(performance_files)} performance files")
        
        # Try to load latest status
        status_files = list(performance_dir.glob("status_report_*.json"))
        if status_files:
            latest_status = max(status_files, key=lambda x: x.stat().st_mtime)
            
            with open(latest_status, 'r') as f:
                status_data = json.load(f)
            
            logger.info(f"[STATUS] Latest status from {status_data['timestamp']}:")
            logger.info(f"  Generation: {status_data['evolution_statistics']['current_generation']}")
            logger.info(f"  Best Fitness: {status_data['evolution_statistics']['best_fitness']:.4f}")
            logger.info(f"  Population Size: {status_data['evolution_statistics']['population_size']}")
            logger.info(f"  Active Strategies: {status_data['system_status']['active_strategies']}")
        else:
            logger.info("[STATUS] No status reports found")
        
    except Exception as e:
        logger.error(f"[ERROR] Error checking status: {e}")

async def export_strategies(runner: StrategyEvolutionRunner, file_path: str):
    """Export strategies to file"""
    logger.info(f"📤 Exporting strategies to {file_path}...")
    
    try:
        runner.agent = StrategyEvolutionAgent(runner.config_path)
        await runner.agent.setup()
        
        success = await runner.agent.export_strategies(file_path)
        
        if success:
            logger.info("[SUCCESS] Strategies exported successfully")
        else:
            logger.error("[ERROR] Failed to export strategies")
    
    except Exception as e:
        logger.error(f"[ERROR] Error exporting strategies: {e}")

async def import_strategies(runner: StrategyEvolutionRunner, file_path: str):
    """Import strategies from file"""
    logger.info(f"📥 Importing strategies from {file_path}...")
    
    try:
        runner.agent = StrategyEvolutionAgent(runner.config_path)
        await runner.agent.setup()
        
        success = await runner.agent.import_strategies(file_path)
        
        if success:
            logger.info("[SUCCESS] Strategies imported successfully")
        else:
            logger.error("[ERROR] Failed to import strategies")
    
    except Exception as e:
        logger.error(f"[ERROR] Error importing strategies: {e}")

if __name__ == "__main__":
    asyncio.run(main())
