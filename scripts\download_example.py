#!/usr/bin/env python3
"""
Example script showing how to use the historical data downloader
with manual date ranges in dd-mm-yyyy format
"""

import asyncio
import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scripts.historical_data_downloader import HistoricalDataDownloader

async def download_with_manual_dates():
    """Example: Download data for specific date range"""
    
    print("[INIT] Historical Data Downloader - Manual Date Example")
    print("=" * 60)
    
    # Example date range (modify these dates as needed)
    from_date = "01-01-2024"  # dd-mm-yyyy format
    to_date = "31-01-2024"    # dd-mm-yyyy format
    
    print(f"📅 Downloading data from {from_date} to {to_date}")
    print(f"[STATUS] Will download ALL 500 symbols")
    print(f"⏱️  Sleep time: 0.1s between symbols")
    print(f"[FOLDER] Output format: Separate date and time columns")
    print()
    
    # Create downloader
    downloader = HistoricalDataDownloader(test_mode=False)  # Set to False for all 500 symbols
    
    # Download data
    success = await downloader.download_all_symbols(
        from_date_str=from_date,
        to_date_str=to_date
    )
    
    if success:
        print("[SUCCESS] Download completed successfully!")
        print("[FOLDER] Check data/historical/historical_5min.parquet for output")
    else:
        print("[ERROR] Download failed!")

async def download_last_7_days():
    """Example: Download last 7 days of data"""
    
    print("[INIT] Historical Data Downloader - Last 7 Days Example")
    print("=" * 60)
    
    # Create downloader
    downloader = HistoricalDataDownloader(test_mode=False)
    
    # Download last 7 days
    success = await downloader.download_all_symbols(days_back=7)
    
    if success:
        print("[SUCCESS] Download completed successfully!")
    else:
        print("[ERROR] Download failed!")

if __name__ == "__main__":
    print("Choose download option:")
    print("1. Download with manual dates (01-01-2024 to 31-01-2024)")
    print("2. Download last 7 days")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        asyncio.run(download_with_manual_dates())
    elif choice == "2":
        asyncio.run(download_last_7_days())
    else:
        print("Invalid choice. Please run again and select 1 or 2.")
