# 🚀 Continuous Live Trading System

An AI-driven continuous trading system that monitors the market throughout trading hours and makes intelligent trading decisions using coordinated AI agents.

## 🎯 Features

- **Continuous Market Monitoring**: Monitors selected stocks throughout trading hours (9:15 AM - 3:30 PM IST)
- **AI-Driven Decisions**: All trading decisions (trade, hold, exit) are made by AI agents
- **Risk Management**: Comprehensive risk validation before every trade
- **Position Management**: Automatic position monitoring and exit signal generation
- **Daily Trade Limits**: Configurable maximum trades per day (default: 5)
- **Auto Square-off**: Automatically closes all positions before market close
- **Real-time Reporting**: Live performance tracking and session reports

## 🤖 AI Agent Coordination

The system uses four coordinated AI agents:

1. **Signal Generation Agent**: Generates buy/sell signals using ML models
2. **Risk Management Agent**: Validates trades against risk parameters
3. **Execution Agent**: Places and manages orders on the exchange
4. **Market Monitoring Agent**: Provides real-time market data and analysis

## 🚀 Quick Start

### 1. Paper Trading (Recommended for testing)
```bash
python run_continuous_live_trading.py --mode paper --max-trades 5
```

### 2. Live Trading (Real money)
```bash
python run_continuous_live_trading.py --mode live --max-trades 5
```

### 3. Using the Launcher
```bash
python start_continuous_trading.py --mode paper --max-trades 3
```

## ⚙️ Configuration

### Command Line Options

- `--mode`: Trading mode (`live`, `paper`, `demo`)
- `--max-trades`: Maximum trades per day (default: 5)
- `--stocks`: Number of stocks to monitor (default: 20)

### Configuration File

Edit `config/continuous_trading_config.yaml` to customize:

- Trading windows and timeframes
- Risk management parameters
- Stock selection criteria
- Position management rules
- Performance tracking settings

## 📊 How It Works

### 1. Initialization Phase
- Initializes all AI agents
- Selects trading universe using AI analysis
- Sets up real-time data connections

### 2. Market Monitoring Phase
- Waits for market to open (if needed)
- Continuously monitors selected stocks
- Checks for trading signals every 30 seconds

### 3. Trading Phase
- Generates signals using AI models
- Validates trades through risk management
- Executes approved trades
- Monitors positions for exit signals

### 4. Position Management
- Tracks all active positions
- Generates exit signals based on AI analysis
- Applies stop-loss and take-profit rules
- Auto square-off before market close

### 5. Reporting Phase
- Generates comprehensive session reports
- Saves performance metrics
- Provides trading statistics

## 🛡️ Safety Features

### Risk Management
- Pre-trade risk validation
- Position size limits
- Daily loss limits
- Correlation checks

### Circuit Breakers
- Maximum consecutive losses
- Daily loss percentage limits
- Unusual volatility detection

### Emergency Stops
- Market crash detection
- API failure handling
- Manual shutdown capability

## 📈 Performance Tracking

The system tracks:
- Signals generated vs trades executed
- Win rate and profit factor
- Real-time P&L
- Position statistics
- System health metrics

## 🔧 System Requirements

- Python 3.8+
- Angel One trading account
- Stable internet connection
- Minimum 4GB RAM
- 1GB free disk space

## 📝 Logging

Logs are saved to:
- `logs/continuous_trading_YYYYMMDD.log`
- `reports/continuous_trading/`

## ⚠️ Important Notes

### For Live Trading
- **Real money will be used**
- **Real orders will be placed on NSE**
- **You can lose money**
- Ensure sufficient account balance
- Test thoroughly in paper mode first
- Monitor the system during operation

### Market Hours
- Trading: 9:15 AM - 3:30 PM IST (weekdays only)
- Active trading window: 9:20 AM - 2:30 PM IST
- Auto square-off: 3:20 PM IST

### Daily Limits
- Maximum 5 trades per day (configurable)
- System stops when limit is reached
- Resets automatically next trading day

## 🛑 Stopping the System

### Graceful Shutdown
- Press `Ctrl+C` to initiate graceful shutdown
- System will close positions and save reports
- Wait for cleanup to complete

### Emergency Stop
- Close terminal window if needed
- Check Angel One app for open positions
- Manually close positions if required

## 📞 Support

For issues or questions:
1. Check the log files for error details
2. Verify your Angel One API credentials
3. Ensure market is open and internet is stable
4. Review the configuration settings

## 🔄 Updates and Maintenance

- Update stock universe weekly
- Review and adjust risk parameters monthly
- Monitor system performance regularly
- Keep API credentials secure and updated

---

**Disclaimer**: This system is for educational and research purposes. Trading involves risk of loss. Use at your own discretion and never risk more than you can afford to lose.
