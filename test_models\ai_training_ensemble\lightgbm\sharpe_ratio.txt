tree
version=v4
num_class=1
num_tree_per_iteration=1
label_index=0
max_feature_idx=2
objective=regression
feature_names=Column_0 Column_1 Column_2
feature_infos=[-1.6828298448072123:1.559049015591971] [-1.6924922288019089:1.7554326221173657] [-1.5478636032349427:1.6051192549636748]
tree_sizes=314 326 326 325 325 326 326 326 325

Tree=0
num_leaves=2
num_cat=0
split_feature=1
split_gain=0.815866
threshold=0.12500482455343542
decision_type=2
left_child=-1
right_child=-2
leaf_value=0.92352159829570679 0.93715273764516627
leaf_weight=23 21
leaf_count=23 21
internal_value=0.930027
internal_weight=44
internal_count=44
is_linear=0
shrinkage=1


Tree=1
num_leaves=2
num_cat=0
split_feature=1
split_gain=0.736319
threshold=0.12500482455343542
decision_type=2
left_child=-1
right_child=-2
leaf_value=-0.0063252147398484143 0.0066243674384341353
leaf_weight=23 21
leaf_count=23 21
internal_value=-0.000144732
internal_weight=44
internal_count=44
is_linear=0
shrinkage=0.05


Tree=2
num_leaves=2
num_cat=0
split_feature=1
split_gain=0.664528
threshold=0.12500482455343542
decision_type=2
left_child=-1
right_child=-2
leaf_value=-0.0060089537509433605 0.0062931492924690252
leaf_weight=23 21
leaf_count=23 21
internal_value=-0.000137495
internal_weight=44
internal_count=44
is_linear=0
shrinkage=0.05


Tree=3
num_leaves=2
num_cat=0
split_feature=1
split_gain=0.599736
threshold=0.12500482455343542
decision_type=2
left_child=-1
right_child=-2
leaf_value=-0.0057085062012724253 0.005978491671738171
leaf_weight=23 21
leaf_count=23 21
internal_value=-0.000130621
internal_weight=44
internal_count=44
is_linear=0
shrinkage=0.05


Tree=4
num_leaves=2
num_cat=0
split_feature=1
split_gain=0.541262
threshold=0.12500482455343542
decision_type=2
left_child=-1
right_child=-2
leaf_value=-0.0054230805972348096 0.0056795671582221992
leaf_weight=23 21
leaf_count=23 21
internal_value=-0.00012409
internal_weight=44
internal_count=44
is_linear=0
shrinkage=0.05


Tree=5
num_leaves=2
num_cat=0
split_feature=1
split_gain=0.495556
threshold=-0.19489447046687089
decision_type=2
left_child=-1
right_child=-2
leaf_value=-0.0084581426763907074 0.0024164894426410848
leaf_weight=20 22
leaf_count=20 22
internal_value=-0.00276191
internal_weight=42
internal_count=42
is_linear=0
shrinkage=0.05


Tree=6
num_leaves=2
num_cat=0
split_feature=1
split_gain=0.447239
threshold=-0.19489447046687089
decision_type=2
left_child=-1
right_child=-2
leaf_value=-0.0080352353025227776 0.0022956648672168905
leaf_weight=20 22
leaf_count=20 22
internal_value=-0.00262381
internal_weight=42
internal_count=42
is_linear=0
shrinkage=0.05


Tree=7
num_leaves=2
num_cat=0
split_feature=1
split_gain=0.403633
threshold=-0.19489447046687089
decision_type=2
left_child=-1
right_child=-2
leaf_value=-0.0076334736961871392 0.0021808820691975681
leaf_weight=20 22
leaf_count=20 22
internal_value=-0.00249262
internal_weight=42
internal_count=42
is_linear=0
shrinkage=0.05


Tree=8
num_leaves=2
num_cat=0
split_feature=1
split_gain=0.364279
threshold=-0.19489447046687089
decision_type=2
left_child=-1
right_child=-2
leaf_value=-0.007251800000667572 0.0020718375051563434
leaf_weight=20 22
leaf_count=20 22
internal_value=-0.00236799
internal_weight=42
internal_count=42
is_linear=0
shrinkage=0.05


end of trees

feature_importances:
Column_1=9

parameters:
[boosting: gbdt]
[objective: regression]
[metric: rmse]
[tree_learner: serial]
[device_type: cpu]
[data_sample_strategy: bagging]
[data: ]
[valid: ]
[num_iterations: 10]
[learning_rate: 0.05]
[num_leaves: 31]
[num_threads: 0]
[seed: 42]
[deterministic: 0]
[force_col_wise: 0]
[force_row_wise: 0]
[histogram_pool_size: -1]
[max_depth: -1]
[min_data_in_leaf: 20]
[min_sum_hessian_in_leaf: 0.001]
[bagging_fraction: 0.8]
[pos_bagging_fraction: 1]
[neg_bagging_fraction: 1]
[bagging_freq: 5]
[bagging_seed: 400]
[bagging_by_query: 0]
[feature_fraction: 0.9]
[feature_fraction_bynode: 1]
[feature_fraction_seed: 30056]
[extra_trees: 0]
[extra_seed: 12879]
[early_stopping_round: 0]
[early_stopping_min_delta: 0]
[first_metric_only: 0]
[max_delta_step: 0]
[lambda_l1: 0]
[lambda_l2: 0]
[linear_lambda: 0]
[min_gain_to_split: 0]
[drop_rate: 0.1]
[max_drop: 50]
[skip_drop: 0.5]
[xgboost_dart_mode: 0]
[uniform_drop: 0]
[drop_seed: 17869]
[top_rate: 0.2]
[other_rate: 0.1]
[min_data_per_group: 100]
[max_cat_threshold: 32]
[cat_l2: 10]
[cat_smooth: 10]
[max_cat_to_onehot: 4]
[top_k: 20]
[monotone_constraints: ]
[monotone_constraints_method: basic]
[monotone_penalty: 0]
[feature_contri: ]
[forcedsplits_filename: ]
[refit_decay_rate: 0.9]
[cegb_tradeoff: 1]
[cegb_penalty_split: 0]
[cegb_penalty_feature_lazy: ]
[cegb_penalty_feature_coupled: ]
[path_smooth: 0]
[interaction_constraints: ]
[verbosity: -1]
[saved_feature_importance_type: 0]
[use_quantized_grad: 0]
[num_grad_quant_bins: 4]
[quant_train_renew_leaf: 0]
[stochastic_rounding: 1]
[linear_tree: 0]
[max_bin: 255]
[max_bin_by_feature: ]
[min_data_in_bin: 3]
[bin_construct_sample_cnt: 200000]
[data_random_seed: 175]
[is_enable_sparse: 1]
[enable_bundle: 1]
[use_missing: 1]
[zero_as_missing: 0]
[feature_pre_filter: 1]
[pre_partition: 0]
[two_round: 0]
[header: 0]
[label_column: ]
[weight_column: ]
[group_column: ]
[ignore_column: ]
[categorical_feature: ]
[forcedbins_filename: ]
[precise_float_parser: 0]
[parser_config_file: ]
[objective_seed: 16083]
[num_class: 1]
[is_unbalance: 0]
[scale_pos_weight: 1]
[sigmoid: 1]
[boost_from_average: 1]
[reg_sqrt: 0]
[alpha: 0.9]
[fair_c: 1]
[poisson_max_delta_step: 0.7]
[tweedie_variance_power: 1.5]
[lambdarank_truncation_level: 30]
[lambdarank_norm: 1]
[label_gain: ]
[lambdarank_position_bias_regularization: 0]
[eval_at: ]
[multi_error_top_k: 1]
[auc_mu_weights: ]
[num_machines: 1]
[local_listen_port: 12400]
[time_out: 120]
[machine_list_filename: ]
[machines: ]
[gpu_platform_id: -1]
[gpu_device_id: -1]
[gpu_use_dp: 0]
[num_gpu: 1]

end of parameters

pandas_categorical:null
