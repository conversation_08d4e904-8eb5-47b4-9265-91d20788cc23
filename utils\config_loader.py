#!/usr/bin/env python3
"""
Centralized Configuration Loader
Utility for loading and managing configuration across all trading agents

Features:
[CONFIG] 1. Environment Variable Resolution
- Load environment variables from .env file
- Resolve ${VAR_NAME} placeholders in YAML configs
- Provide default values and validation

🔗 2. Centralized Configuration Management
- Load shared environment configuration
- Merge agent-specific configs with shared settings
- Validate required configuration sections

[STATUS] 3. Configuration Validation
- Check required environment variables
- Validate configuration structure
- Provide helpful error messages

🔒 4. Security Features
- Mask sensitive values in logs
- Validate credential formats
- Support multiple environment modes
"""

import os
import sys
import yaml
import logging
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import re
from dataclasses import dataclass
from datetime import datetime

# Setup logging
logger = logging.getLogger(__name__)

@dataclass
class ConfigValidationResult:
    """Result of configuration validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    missing_vars: List[str]

class ConfigurationLoader:
    """Centralized configuration loader for all trading agents"""
    
    def __init__(self, env_file_path: str = ".env", environment_config_path: str = "config/environment_config.yaml"):
        self.env_file_path = env_file_path
        self.environment_config_path = environment_config_path
        self.env_vars = {}
        self.environment_config = {}
        
        # Load environment variables and shared config
        self._load_environment_variables()
        self._load_environment_config()
    
    def _load_environment_variables(self):
        """Load environment variables from .env file and system"""
        try:
            # Load from system environment first
            self.env_vars.update(dict(os.environ))

            # Load from .env file if it exists
            if os.path.exists(self.env_file_path):
                with open(self.env_file_path, 'r', encoding='utf-8') as file:
                    for line in file:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            # Only set if not already in system environment
                            if key not in self.env_vars:
                                self.env_vars[key] = value

                logger.info(f"[OK] Loaded environment variables from {self.env_file_path}")
            else:
                logger.warning(f"[WARN] Environment file not found: {self.env_file_path}")

        except Exception as e:
            logger.error(f"[ERROR] Error loading environment variables: {e}")
    
    def _load_environment_config(self):
        """Load shared environment configuration"""
        try:
            if os.path.exists(self.environment_config_path):
                with open(self.environment_config_path, 'r', encoding='utf-8') as file:
                    self.environment_config = yaml.safe_load(file)
                logger.info(f"[OK] Loaded environment configuration from {self.environment_config_path}")
            else:
                logger.warning(f"[WARN] Environment config not found: {self.environment_config_path}")

        except Exception as e:
            logger.error(f"[ERROR] Error loading environment configuration: {e}")
    
    def _resolve_environment_variables(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively resolve ${VAR_NAME} placeholders in configuration"""
        if isinstance(config, dict):
            resolved = {}
            for key, value in config.items():
                resolved[key] = self._resolve_environment_variables(value)
            return resolved
        elif isinstance(config, list):
            return [self._resolve_environment_variables(item) for item in config]
        elif isinstance(config, str):
            # Replace ${VAR_NAME} with environment variable value
            pattern = r'\$\{([^}]+)\}'
            
            def replace_var(match):
                var_name = match.group(1)
                return self.env_vars.get(var_name, match.group(0))  # Return original if not found
            
            return re.sub(pattern, replace_var, config)
        else:
            return config
    
    def load_agent_config(self, config_path: str, agent_name: str = None) -> Dict[str, Any]:
        """
        Load agent-specific configuration with environment variable resolution
        
        Args:
            config_path: Path to agent configuration file
            agent_name: Name of the agent (for logging)
            
        Returns:
            Resolved configuration dictionary
        """
        try:
            if not os.path.exists(config_path):
                raise FileNotFoundError(f"Configuration file not found: {config_path}")
            
            # Load agent configuration
            with open(config_path, 'r', encoding='utf-8') as file:
                agent_config = yaml.safe_load(file)
            
            # Resolve environment variables
            resolved_config = self._resolve_environment_variables(agent_config)
            
            # Merge with shared environment configuration if needed
            if 'smartapi' in self.environment_config:
                # Add shared SmartAPI config if agent doesn't have it
                if 'smartapi' not in resolved_config and 'angel_one_api' not in resolved_config:
                    resolved_config['smartapi'] = self.environment_config['smartapi']
            
            if 'notifications' in self.environment_config:
                # Merge shared notification config
                if 'notifications' in resolved_config:
                    # Merge shared settings with agent-specific settings
                    shared_notifications = self.environment_config['notifications']
                    agent_notifications = resolved_config['notifications']
                    
                    for service, config in shared_notifications.items():
                        if service not in agent_notifications:
                            agent_notifications[service] = config
                        else:
                            # Merge service-specific settings
                            for key, value in config.items():
                                if key not in agent_notifications[service]:
                                    agent_notifications[service][key] = value
            
            agent_display_name = agent_name or Path(config_path).stem
            logger.info(f"[SUCCESS] Loaded configuration for {agent_display_name}")
            
            return resolved_config
            
        except Exception as e:
            logger.error(f"[ERROR] Error loading agent configuration from {config_path}: {e}")
            raise
    
    def validate_smartapi_config(self, config: Dict[str, Any]) -> ConfigValidationResult:
        """Validate SmartAPI configuration"""
        errors = []
        warnings = []
        missing_vars = []
        
        # Check for SmartAPI configuration
        smartapi_config = config.get('smartapi') or config.get('angel_one_api', {})
        
        if not smartapi_config:
            errors.append("No SmartAPI configuration found")
            return ConfigValidationResult(False, errors, warnings, missing_vars)
        
        # Required SmartAPI fields
        required_fields = ['api_key', 'username', 'password', 'totp_token']
        
        for field in required_fields:
            value = smartapi_config.get(field, '')
            
            if not value:
                missing_vars.append(f"SMARTAPI_{field.upper()}")
                errors.append(f"Missing SmartAPI {field}")
            elif value.startswith('${') and value.endswith('}'):
                # Environment variable not resolved
                var_name = value[2:-1]
                missing_vars.append(var_name)
                errors.append(f"Environment variable not set: {var_name}")
            elif field in ['api_key', 'totp_token'] and len(value) < 10:
                warnings.append(f"SmartAPI {field} seems too short")
        
        is_valid = len(errors) == 0
        return ConfigValidationResult(is_valid, errors, warnings, missing_vars)
    
    def validate_notification_config(self, config: Dict[str, Any]) -> ConfigValidationResult:
        """Validate notification configuration"""
        errors = []
        warnings = []
        missing_vars = []
        
        notifications = config.get('notifications', {})
        
        # Check Telegram configuration if enabled
        telegram_config = notifications.get('telegram', {})
        if telegram_config.get('enabled') or telegram_config.get('enable'):
            required_telegram = ['bot_token', 'chat_id']
            for field in required_telegram:
                value = telegram_config.get(field, '')
                if not value:
                    missing_vars.append(f"TELEGRAM_{field.upper()}")
                    errors.append(f"Missing Telegram {field}")
                elif value.startswith('${') and value.endswith('}'):
                    var_name = value[2:-1]
                    missing_vars.append(var_name)
                    errors.append(f"Environment variable not set: {var_name}")
        
        # Check Email configuration if enabled
        email_config = notifications.get('email', {})
        if email_config.get('enabled') or email_config.get('enable'):
            required_email = ['smtp_server', 'username', 'password']
            for field in required_email:
                value = email_config.get(field, '')
                if not value:
                    missing_vars.append(f"EMAIL_{field.upper()}")
                    warnings.append(f"Missing Email {field}")
        
        is_valid = len(errors) == 0
        return ConfigValidationResult(is_valid, errors, warnings, missing_vars)
    
    def validate_agent_config(self, config: Dict[str, Any], agent_name: str = "Unknown") -> ConfigValidationResult:
        """Validate complete agent configuration"""
        all_errors = []
        all_warnings = []
        all_missing_vars = []
        
        # Validate SmartAPI configuration
        smartapi_result = self.validate_smartapi_config(config)
        all_errors.extend(smartapi_result.errors)
        all_warnings.extend(smartapi_result.warnings)
        all_missing_vars.extend(smartapi_result.missing_vars)
        
        # Validate notification configuration
        notification_result = self.validate_notification_config(config)
        all_errors.extend(notification_result.errors)
        all_warnings.extend(notification_result.warnings)
        all_missing_vars.extend(notification_result.missing_vars)
        
        # Remove duplicates
        all_missing_vars = list(set(all_missing_vars))
        
        is_valid = len(all_errors) == 0
        
        # Log validation results
        if is_valid:
            logger.info(f"[SUCCESS] Configuration validation passed for {agent_name}")
            if all_warnings:
                for warning in all_warnings:
                    logger.warning(f"[WARN]  {warning}")
        else:
            logger.error(f"[ERROR] Configuration validation failed for {agent_name}")
            for error in all_errors:
                logger.error(f"   - {error}")
        
        return ConfigValidationResult(is_valid, all_errors, all_warnings, all_missing_vars)
    
    def get_smartapi_credentials(self) -> Dict[str, str]:
        """Get SmartAPI credentials from environment variables"""
        return {
            'api_key': self.env_vars.get('SMARTAPI_API_KEY', ''),
            'username': self.env_vars.get('SMARTAPI_USERNAME', ''),
            'password': self.env_vars.get('SMARTAPI_PASSWORD', ''),
            'totp_token': self.env_vars.get('SMARTAPI_TOTP_TOKEN', '')
        }
    
    def get_notification_credentials(self) -> Dict[str, Dict[str, str]]:
        """Get notification service credentials"""
        return {
            'telegram': {
                'bot_token': self.env_vars.get('TELEGRAM_BOT_TOKEN', ''),
                'chat_id': self.env_vars.get('TELEGRAM_CHAT_ID', '')
            },
            'email': {
                'smtp_server': self.env_vars.get('EMAIL_SMTP_SERVER', ''),
                'smtp_port': self.env_vars.get('EMAIL_SMTP_PORT', '587'),
                'username': self.env_vars.get('EMAIL_USERNAME', ''),
                'password': self.env_vars.get('EMAIL_PASSWORD', ''),
                'from_email': self.env_vars.get('EMAIL_FROM', ''),
                'to_email': self.env_vars.get('EMAIL_TO', '')
            },
            'slack': {
                'webhook_url': self.env_vars.get('SLACK_WEBHOOK_URL', ''),
                'channel': self.env_vars.get('SLACK_CHANNEL', '')
            }
        }
    
    def create_env_file_from_template(self, template_path: str = ".env.template", output_path: str = ".env"):
        """Create .env file from template"""
        try:
            if not os.path.exists(template_path):
                logger.error(f"[ERROR] Template file not found: {template_path}")
                return False
            
            if os.path.exists(output_path):
                logger.warning(f"[WARN]  Output file already exists: {output_path}")
                return False
            
            # Copy template to .env
            with open(template_path, 'r') as template_file:
                content = template_file.read()
            
            with open(output_path, 'w') as env_file:
                env_file.write(content)
            
            logger.info(f"[SUCCESS] Created {output_path} from {template_path}")
            logger.info(f"📝 Please edit {output_path} and fill in your actual values")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Error creating env file: {e}")
            return False

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def load_config_for_agent(agent_name: str, config_path: str = None) -> Dict[str, Any]:
    """
    Convenience function to load configuration for a specific agent
    
    Args:
        agent_name: Name of the agent
        config_path: Optional custom config path
        
    Returns:
        Loaded and validated configuration
    """
    if not config_path:
        config_path = f"config/{agent_name}_config.yaml"
    
    loader = ConfigurationLoader()
    config = loader.load_agent_config(config_path, agent_name)
    
    # Validate configuration
    validation_result = loader.validate_agent_config(config, agent_name)
    
    if not validation_result.is_valid:
        raise ValueError(f"Invalid configuration for {agent_name}: {validation_result.errors}")
    
    return config

def check_environment_setup() -> bool:
    """Check if environment is properly set up"""
    loader = ConfigurationLoader()
    
    # Check if .env file exists
    if not os.path.exists(loader.env_file_path):
        logger.error(f"[ERROR] Environment file not found: {loader.env_file_path}")
        logger.info(f"[INFO] Run: cp .env.template .env and fill in your values")
        return False
    
    # Check SmartAPI credentials
    credentials = loader.get_smartapi_credentials()
    missing_creds = [key for key, value in credentials.items() if not value]
    
    if missing_creds:
        logger.error(f"[ERROR] Missing SmartAPI credentials: {missing_creds}")
        return False
    
    logger.info("[SUCCESS] Environment setup is valid")
    return True

if __name__ == "__main__":
    # Test the configuration loader
    loader = ConfigurationLoader()
    
    # Check environment setup
    if check_environment_setup():
        print("[SUCCESS] Environment setup is valid")
    else:
        print("[ERROR] Environment setup needs attention")
        
        # Offer to create .env file from template
        if not os.path.exists(".env") and os.path.exists(".env.template"):
            response = input("Create .env file from template? (y/n): ")
            if response.lower() == 'y':
                loader.create_env_file_from_template()
