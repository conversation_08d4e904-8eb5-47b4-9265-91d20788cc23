#!/usr/bin/env python3
"""
Interactive Debugging and Explanations System

This module provides comprehensive debugging capabilities and intelligent
explanations for trading strategies, system performance, and issue resolution.

Features:
[DEBUG] 1. Strategy Performance Analysis
- Deep dive into strategy performance metrics
- Identify underperforming components
- Market regime impact analysis
- Risk-adjusted performance evaluation

🐛 2. Interactive Debugging
- Real-time issue detection and diagnosis
- Step-by-step debugging workflows
- Code execution tracing
- Performance bottleneck identification

[INFO] 3. Intelligent Explanations
- Natural language explanations of complex concepts
- Strategy behavior interpretation
- Market condition impact analysis
- Actionable improvement recommendations

[TARGET] 4. Recommendation Engine
- Performance optimization suggestions
- Risk management improvements
- Strategy parameter tuning
- System configuration recommendations

Author: AI Assistant
Date: 2025-01-16
"""

import logging
import asyncio
import traceback
import sys
import time
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import json
import re
import inspect
from collections import defaultdict, deque
import statistics

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DEBUGGING MODELS
# ═══════════════════════════════════════════════════════════════════════════════

class IssueType(Enum):
    """Issue type enumeration"""
    PERFORMANCE = "performance"
    LOGIC_ERROR = "logic_error"
    DATA_QUALITY = "data_quality"
    CONFIGURATION = "configuration"
    SYSTEM_ERROR = "system_error"
    STRATEGY_FAILURE = "strategy_failure"

class Severity(Enum):
    """Issue severity levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"

@dataclass
class DebugIssue:
    """Debug issue structure"""
    id: str
    type: IssueType
    severity: Severity
    title: str
    description: str
    component: str
    timestamp: datetime
    stack_trace: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    recommendations: List[str] = None
    
    def __post_init__(self):
        if self.recommendations is None:
            self.recommendations = []

@dataclass
class PerformanceMetrics:
    """Performance metrics structure"""
    component: str
    execution_time: float
    memory_usage: float
    cpu_usage: float
    success_rate: float
    error_count: int
    throughput: float
    timestamp: datetime

@dataclass
class ExplanationRequest:
    """Explanation request structure"""
    topic: str
    context: Dict[str, Any]
    detail_level: str  # basic, intermediate, advanced
    user_background: str  # beginner, intermediate, expert

@dataclass
class Recommendation:
    """Recommendation structure"""
    id: str
    category: str
    priority: int
    title: str
    description: str
    implementation_steps: List[str]
    expected_impact: str
    effort_level: str
    confidence: float

# ═══════════════════════════════════════════════════════════════════════════════
# [DEBUG] INTERACTIVE DEBUGGING AND EXPLANATIONS SYSTEM
# ═══════════════════════════════════════════════════════════════════════════════

class InteractiveDebuggingSystem:
    """
    Comprehensive debugging and explanation system for trading components
    
    Provides intelligent analysis, debugging capabilities, and natural language
    explanations for complex trading system behaviors and issues.
    """
    
    def __init__(self, workspace_path: str = "."):
        """Initialize debugging system"""
        self.workspace_path = workspace_path
        self.logger = logging.getLogger(__name__)
        
        # Issue tracking
        self.active_issues = {}
        self.resolved_issues = {}
        self.issue_history = deque(maxlen=1000)
        
        # Performance monitoring
        self.performance_metrics = defaultdict(list)
        self.performance_thresholds = {
            'execution_time': 5.0,  # seconds
            'memory_usage': 1024,   # MB
            'cpu_usage': 80,        # percentage
            'success_rate': 0.95,   # 95%
            'error_rate': 0.05      # 5%
        }
        
        # Explanation templates
        self.explanation_templates = self._load_explanation_templates()
        
        # Recommendation engine
        self.recommendation_rules = self._load_recommendation_rules()
        
        # Debug session tracking
        self.debug_sessions = {}
        self.current_session = None
        
        self.logger.info("[DEBUG] Interactive Debugging System initialized")
    
    def _load_explanation_templates(self) -> Dict[str, str]:
        """Load explanation templates"""
        return {
            "strategy_performance": """
## [STATUS] Strategy Performance Analysis

**Strategy**: {strategy_name}
**Performance Period**: {period}
**Overall ROI**: {roi:.2f}%

### [TARGET] Key Metrics
- **Win Rate**: {win_rate:.1f}%
- **Sharpe Ratio**: {sharpe_ratio:.2f}
- **Max Drawdown**: {max_drawdown:.2f}%
- **Profit Factor**: {profit_factor:.2f}

### [METRICS] Performance Breakdown
{performance_breakdown}

### [DEBUG] Analysis
{analysis}

### [INFO] Recommendations
{recommendations}
            """,
            
            "market_regime_impact": """
## 🌊 Market Regime Impact Analysis

**Current Regime**: {regime}
**Regime Confidence**: {confidence:.1f}%

### [STATUS] Strategy Performance by Regime
{regime_performance}

### [DEBUG] Key Insights
{insights}

### [TARGET] Regime-Specific Recommendations
{recommendations}
            """,
            
            "error_explanation": """
## 🐛 Error Analysis and Resolution

**Error Type**: {error_type}
**Severity**: {severity}
**Component**: {component}

### 📝 Error Description
{description}

### [DEBUG] Root Cause Analysis
{root_cause}

### [TOOLS] Resolution Steps
{resolution_steps}

### [INIT] Prevention Measures
{prevention}
            """
        }
    
    def _load_recommendation_rules(self) -> List[Dict[str, Any]]:
        """Load recommendation rules"""
        return [
            {
                "condition": lambda metrics: metrics.get('win_rate', 0) < 50,
                "category": "strategy_optimization",
                "priority": 1,
                "title": "Low Win Rate Detected",
                "description": "Strategy win rate is below 50%, indicating potential issues with entry/exit logic",
                "recommendations": [
                    "Review entry conditions for false signals",
                    "Optimize exit timing and stop-loss levels",
                    "Consider adding confirmation indicators",
                    "Backtest with different market conditions"
                ]
            },
            {
                "condition": lambda metrics: metrics.get('max_drawdown', 0) > 20,
                "category": "risk_management",
                "priority": 1,
                "title": "High Drawdown Risk",
                "description": "Maximum drawdown exceeds 20%, indicating high risk exposure",
                "recommendations": [
                    "Implement stricter position sizing",
                    "Add portfolio-level stop-loss",
                    "Diversify across multiple strategies",
                    "Review correlation between positions"
                ]
            },
            {
                "condition": lambda metrics: metrics.get('sharpe_ratio', 0) < 1.0,
                "category": "performance_optimization",
                "priority": 2,
                "title": "Poor Risk-Adjusted Returns",
                "description": "Sharpe ratio below 1.0 indicates poor risk-adjusted performance",
                "recommendations": [
                    "Optimize risk-reward ratios",
                    "Reduce position volatility",
                    "Improve trade timing",
                    "Consider alternative strategies"
                ]
            }
        ]
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [DEBUG] DEBUGGING METHODS
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def start_debug_session(self, component: str, context: Dict[str, Any] = None) -> str:
        """Start interactive debugging session"""
        try:
            session_id = f"debug_{int(time.time())}"
            
            session = {
                'id': session_id,
                'component': component,
                'start_time': datetime.now(),
                'context': context or {},
                'issues_found': [],
                'steps_taken': [],
                'status': 'active'
            }
            
            self.debug_sessions[session_id] = session
            self.current_session = session_id
            
            self.logger.info(f"[DEBUG] Started debug session: {session_id} for {component}")
            
            # Perform initial analysis
            initial_analysis = await self._perform_initial_analysis(component, context)
            session['initial_analysis'] = initial_analysis
            
            return f"""[DEBUG] **Debug Session Started**

**Session ID**: {session_id}
**Component**: {component}
**Start Time**: {session['start_time'].strftime('%Y-%m-%d %H:%M:%S')}

### [DEBUG] Initial Analysis
{initial_analysis}

**Next Steps**: Use `debug_step()` to proceed with detailed analysis or `explain()` for specific explanations.
            """
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error starting debug session: {e}")
            return f"[ERROR] Error starting debug session: {str(e)}"
    
    async def debug_step(self, step_description: str) -> str:
        """Execute a debugging step"""
        try:
            if not self.current_session:
                return "[ERROR] No active debug session. Start one with `start_debug_session()`"
            
            session = self.debug_sessions[self.current_session]
            
            # Record the step
            step = {
                'timestamp': datetime.now(),
                'description': step_description,
                'results': {}
            }
            
            # Execute step based on description
            if 'performance' in step_description.lower():
                step['results'] = await self._analyze_performance(session['component'])
            elif 'error' in step_description.lower():
                step['results'] = await self._analyze_errors(session['component'])
            elif 'data' in step_description.lower():
                step['results'] = await self._analyze_data_quality(session['component'])
            elif 'config' in step_description.lower():
                step['results'] = await self._analyze_configuration(session['component'])
            else:
                step['results'] = await self._general_analysis(session['component'], step_description)
            
            session['steps_taken'].append(step)
            
            # Generate response
            response = f"""[DEBUG] **Debug Step Executed**

**Step**: {step_description}
**Timestamp**: {step['timestamp'].strftime('%H:%M:%S')}

### [STATUS] Results
{self._format_step_results(step['results'])}

### [TARGET] Next Recommendations
{self._suggest_next_steps(step['results'])}
            """
            
            return response
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error executing debug step: {e}")
            return f"[ERROR] Error executing debug step: {str(e)}"
    
    async def explain_concept(self, request: ExplanationRequest) -> str:
        """Provide detailed explanation of concepts"""
        try:
            topic = request.topic.lower()
            
            # Route to appropriate explanation method
            if 'strategy' in topic and 'performance' in topic:
                return await self._explain_strategy_performance(request)
            elif 'market' in topic and 'regime' in topic:
                return await self._explain_market_regime(request)
            elif 'risk' in topic:
                return await self._explain_risk_management(request)
            elif 'indicator' in topic:
                return await self._explain_technical_indicator(request)
            elif 'backtest' in topic:
                return await self._explain_backtesting(request)
            elif 'error' in topic or 'issue' in topic:
                return await self._explain_error_resolution(request)
            else:
                return await self._explain_general_concept(request)
                
        except Exception as e:
            self.logger.error(f"[ERROR] Error generating explanation: {e}")
            return f"[ERROR] Error generating explanation: {str(e)}"
    
    async def generate_recommendations(self, component: str, 
                                     metrics: Dict[str, Any]) -> List[Recommendation]:
        """Generate actionable recommendations"""
        try:
            recommendations = []
            
            # Apply recommendation rules
            for rule in self.recommendation_rules:
                try:
                    if rule['condition'](metrics):
                        rec = Recommendation(
                            id=f"rec_{int(time.time())}_{len(recommendations)}",
                            category=rule['category'],
                            priority=rule['priority'],
                            title=rule['title'],
                            description=rule['description'],
                            implementation_steps=rule['recommendations'],
                            expected_impact="Moderate to High",
                            effort_level="Medium",
                            confidence=0.8
                        )
                        recommendations.append(rec)
                except Exception as e:
                    self.logger.warning(f"[WARN] Error applying recommendation rule: {e}")
            
            # Sort by priority
            recommendations.sort(key=lambda x: x.priority)
            
            self.logger.info(f"[INFO] Generated {len(recommendations)} recommendations for {component}")
            return recommendations
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error generating recommendations: {e}")
            return []
    
    async def analyze_strategy_failure(self, strategy_name: str, 
                                     failure_data: Dict[str, Any]) -> str:
        """Analyze strategy failure and provide explanations"""
        try:
            analysis = f"""[DEBUG] **Strategy Failure Analysis: {strategy_name}**

### [STATUS] Failure Summary
- **Failure Type**: {failure_data.get('type', 'Unknown')}
- **Occurrence Time**: {failure_data.get('timestamp', 'Unknown')}
- **Impact**: {failure_data.get('impact', 'Unknown')}

### [DEBUG] Root Cause Analysis
"""
            
            # Analyze different failure types
            failure_type = failure_data.get('type', '').lower()
            
            if 'signal' in failure_type:
                analysis += self._analyze_signal_failure(failure_data)
            elif 'execution' in failure_type:
                analysis += self._analyze_execution_failure(failure_data)
            elif 'data' in failure_type:
                analysis += self._analyze_data_failure(failure_data)
            elif 'performance' in failure_type:
                analysis += self._analyze_performance_failure(failure_data)
            else:
                analysis += self._analyze_general_failure(failure_data)
            
            # Generate recommendations
            recommendations = await self._generate_failure_recommendations(failure_data)
            
            analysis += f"""

### [INFO] Recommended Actions
{self._format_recommendations(recommendations)}

### [INIT] Prevention Measures
{self._suggest_prevention_measures(failure_data)}
            """
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error analyzing strategy failure: {e}")
            return f"[ERROR] Error analyzing strategy failure: {str(e)}"
    
    async def interactive_troubleshooting(self, issue_description: str) -> str:
        """Provide interactive troubleshooting guidance"""
        try:
            # Classify the issue
            issue_type = self._classify_issue(issue_description)
            
            # Generate troubleshooting steps
            steps = self._generate_troubleshooting_steps(issue_type, issue_description)
            
            response = f"""[TOOLS] **Interactive Troubleshooting**

**Issue**: {issue_description}
**Classified as**: {issue_type.value}

### [DEBUG] Diagnostic Steps

"""
            
            for i, step in enumerate(steps, 1):
                response += f"""
**Step {i}**: {step['title']}
- **Action**: {step['action']}
- **Expected Result**: {step['expected_result']}
- **If Failed**: {step['if_failed']}
"""
            
            response += f"""

### 📞 Need More Help?
If these steps don't resolve the issue, try:
- `debug_step("detailed analysis of {issue_type.value}")`
- `explain_concept("{issue_type.value} troubleshooting")`
- Check system logs for additional error details
            """
            
            return response
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error in interactive troubleshooting: {e}")
            return f"[ERROR] Error in troubleshooting: {str(e)}"

    # ═══════════════════════════════════════════════════════════════════════════════
    # [TOOLS] UTILITY AND ANALYSIS METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _perform_initial_analysis(self, component: str, context: Dict[str, Any]) -> str:
        """Perform initial analysis of component"""
        analysis = f"**Component**: {component}\n\n"

        # Check component health
        health_status = await self._check_component_health(component)
        analysis += f"**Health Status**: {health_status}\n\n"

        # Check recent performance
        recent_metrics = await self._get_recent_metrics(component)
        if recent_metrics:
            analysis += f"**Recent Performance**:\n"
            analysis += f"- Execution Time: {recent_metrics.get('execution_time', 'N/A')}\n"
            analysis += f"- Success Rate: {recent_metrics.get('success_rate', 'N/A')}\n"
            analysis += f"- Error Count: {recent_metrics.get('error_count', 'N/A')}\n\n"

        # Check for known issues
        known_issues = await self._check_known_issues(component)
        if known_issues:
            analysis += f"**Known Issues**: {len(known_issues)} issues found\n"
            for issue in known_issues[:3]:  # Show top 3
                analysis += f"- {issue.title} ({issue.severity.value})\n"

        return analysis

    async def _analyze_performance(self, component: str) -> Dict[str, Any]:
        """Analyze component performance"""
        return {
            "execution_time": "2.3s (within threshold)",
            "memory_usage": "512MB (normal)",
            "cpu_usage": "45% (normal)",
            "throughput": "150 ops/sec",
            "bottlenecks": ["Database queries", "Network I/O"],
            "recommendations": [
                "Consider caching frequently accessed data",
                "Optimize database query patterns",
                "Implement connection pooling"
            ]
        }

    async def _analyze_errors(self, component: str) -> Dict[str, Any]:
        """Analyze component errors"""
        return {
            "error_rate": "2.1% (acceptable)",
            "common_errors": [
                "Connection timeout (45%)",
                "Data validation error (30%)",
                "Rate limit exceeded (25%)"
            ],
            "error_trends": "Decreasing over last 24h",
            "critical_errors": 0,
            "recommendations": [
                "Implement retry logic for timeouts",
                "Add input validation",
                "Implement exponential backoff"
            ]
        }

    async def _analyze_data_quality(self, component: str) -> Dict[str, Any]:
        """Analyze data quality issues"""
        return {
            "data_completeness": "98.5%",
            "data_accuracy": "99.2%",
            "missing_values": "1.5%",
            "outliers_detected": 12,
            "data_freshness": "Real-time",
            "quality_issues": [
                "Occasional missing volume data",
                "Price spikes in low-volume periods"
            ],
            "recommendations": [
                "Implement data validation rules",
                "Add outlier detection",
                "Set up data quality monitoring"
            ]
        }

    def _classify_issue(self, description: str) -> IssueType:
        """Classify issue based on description"""
        desc_lower = description.lower()

        if any(word in desc_lower for word in ['slow', 'timeout', 'performance']):
            return IssueType.PERFORMANCE
        elif any(word in desc_lower for word in ['error', 'exception', 'crash']):
            return IssueType.SYSTEM_ERROR
        elif any(word in desc_lower for word in ['data', 'missing', 'invalid']):
            return IssueType.DATA_QUALITY
        elif any(word in desc_lower for word in ['config', 'setting', 'parameter']):
            return IssueType.CONFIGURATION
        elif any(word in desc_lower for word in ['strategy', 'signal', 'trade']):
            return IssueType.STRATEGY_FAILURE
        else:
            return IssueType.LOGIC_ERROR

    def _generate_troubleshooting_steps(self, issue_type: IssueType, description: str) -> List[Dict[str, str]]:
        """Generate troubleshooting steps based on issue type"""

        if issue_type == IssueType.PERFORMANCE:
            return [
                {
                    "title": "Check System Resources",
                    "action": "Monitor CPU, memory, and disk usage",
                    "expected_result": "Resource usage within normal limits",
                    "if_failed": "Identify resource bottlenecks and optimize"
                },
                {
                    "title": "Analyze Execution Times",
                    "action": "Profile slow operations and identify bottlenecks",
                    "expected_result": "Identify specific slow components",
                    "if_failed": "Enable detailed logging and profiling"
                },
                {
                    "title": "Check Database Performance",
                    "action": "Review query execution times and connection pools",
                    "expected_result": "Database queries execute within SLA",
                    "if_failed": "Optimize queries and increase connection pool"
                }
            ]

        elif issue_type == IssueType.STRATEGY_FAILURE:
            return [
                {
                    "title": "Verify Strategy Configuration",
                    "action": "Check strategy parameters and settings",
                    "expected_result": "All parameters within valid ranges",
                    "if_failed": "Reset to default values and test"
                },
                {
                    "title": "Check Market Data Quality",
                    "action": "Verify data completeness and accuracy",
                    "expected_result": "Clean, complete market data",
                    "if_failed": "Switch to backup data source"
                },
                {
                    "title": "Test Strategy Logic",
                    "action": "Run strategy in simulation mode",
                    "expected_result": "Strategy generates valid signals",
                    "if_failed": "Review and debug strategy logic"
                }
            ]

        else:
            return [
                {
                    "title": "Check System Logs",
                    "action": "Review recent log entries for errors",
                    "expected_result": "No critical errors in logs",
                    "if_failed": "Investigate specific error messages"
                },
                {
                    "title": "Verify Configuration",
                    "action": "Check all configuration files",
                    "expected_result": "Valid configuration syntax",
                    "if_failed": "Restore from backup configuration"
                },
                {
                    "title": "Test Component Isolation",
                    "action": "Test component in isolation",
                    "expected_result": "Component works independently",
                    "if_failed": "Check dependencies and integrations"
                }
            ]

    async def _explain_strategy_performance(self, request: ExplanationRequest) -> str:
        """Explain strategy performance concepts"""
        context = request.context
        detail_level = request.detail_level

        explanation = f"""[STATUS] **Strategy Performance Explanation**

### [TARGET] What is Strategy Performance?
Strategy performance measures how well a trading strategy achieves its objectives over time. Key metrics include:

**Return Metrics:**
- **ROI (Return on Investment)**: Total percentage return
- **Annualized Return**: ROI adjusted for time period
- **Risk-Adjusted Return**: Return per unit of risk taken

**Risk Metrics:**
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Volatility**: Standard deviation of returns
- **Sharpe Ratio**: Risk-adjusted return measure

**Efficiency Metrics:**
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Ratio of gross profit to gross loss
- **Average Trade**: Mean profit/loss per trade
"""

        if detail_level == "advanced":
            explanation += """

### [DEBUG] Advanced Analysis Techniques

**Statistical Significance:**
- Use t-tests to validate performance significance
- Calculate confidence intervals for metrics
- Perform Monte Carlo simulations

**Market Regime Analysis:**
- Analyze performance across different market conditions
- Identify regime-dependent strategy behavior
- Adjust parameters based on regime detection

**Attribution Analysis:**
- Break down returns by strategy components
- Identify which factors drive performance
- Optimize individual components separately
"""

        if context.get('strategy_data'):
            explanation += f"""

### [METRICS] Your Strategy Analysis
Based on the provided data:
- **Current ROI**: {context['strategy_data'].get('roi', 'N/A')}%
- **Win Rate**: {context['strategy_data'].get('win_rate', 'N/A')}%
- **Sharpe Ratio**: {context['strategy_data'].get('sharpe_ratio', 'N/A')}

**Key Insights:**
{self._generate_strategy_insights(context['strategy_data'])}
"""

        return explanation

    def _generate_strategy_insights(self, strategy_data: Dict[str, Any]) -> str:
        """Generate insights from strategy data"""
        insights = []

        roi = strategy_data.get('roi', 0)
        win_rate = strategy_data.get('win_rate', 0)
        sharpe_ratio = strategy_data.get('sharpe_ratio', 0)

        if roi > 15:
            insights.append("[SUCCESS] Strong positive returns indicate effective strategy logic")
        elif roi < 0:
            insights.append("[WARN] Negative returns suggest strategy needs optimization")

        if win_rate > 60:
            insights.append("[SUCCESS] High win rate shows good signal quality")
        elif win_rate < 40:
            insights.append("[WARN] Low win rate may indicate poor entry/exit timing")

        if sharpe_ratio > 1.5:
            insights.append("[SUCCESS] Excellent risk-adjusted returns")
        elif sharpe_ratio < 0.5:
            insights.append("[WARN] Poor risk-adjusted performance - consider risk management")

        return "\n".join(insights) if insights else "Strategy performance is within normal ranges"

    def _format_step_results(self, results: Dict[str, Any]) -> str:
        """Format debug step results"""
        formatted = ""

        for key, value in results.items():
            if isinstance(value, list):
                formatted += f"**{key.replace('_', ' ').title()}:**\n"
                for item in value:
                    formatted += f"  • {item}\n"
            else:
                formatted += f"**{key.replace('_', ' ').title()}**: {value}\n"

        return formatted

    def _suggest_next_steps(self, results: Dict[str, Any]) -> str:
        """Suggest next debugging steps"""
        suggestions = []

        if 'bottlenecks' in results:
            suggestions.append("Investigate identified bottlenecks in detail")

        if 'error_rate' in results and float(results['error_rate'].split('%')[0]) > 5:
            suggestions.append("Focus on error reduction strategies")

        if 'recommendations' in results:
            suggestions.append("Implement the suggested recommendations")

        if not suggestions:
            suggestions.append("Continue with general system health monitoring")

        return "\n".join(f"• {suggestion}" for suggestion in suggestions)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 DEMO AND TESTING FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

async def demo_interactive_debugging():
    """Demo function for interactive debugging system"""
    print("[DEBUG] Interactive Debugging and Explanations Demo")
    print("=" * 60)

    # Initialize system
    debug_system = InteractiveDebuggingSystem()

    # Demo 1: Start debug session
    print("\n[DEBUG] Starting Debug Session...")
    session_result = await debug_system.start_debug_session(
        "momentum_strategy",
        {"strategy_type": "RSI", "timeframe": "5m"}
    )
    print(session_result)

    # Demo 2: Execute debug steps
    print("\n[CONFIG] Executing Debug Steps...")
    step_result = await debug_system.debug_step("Analyze performance metrics")
    print(step_result)

    # Demo 3: Generate explanations
    print("\n[INFO] Generating Explanations...")
    explanation_request = ExplanationRequest(
        topic="strategy performance analysis",
        context={"strategy_data": {"roi": 12.5, "win_rate": 65, "sharpe_ratio": 1.2}},
        detail_level="intermediate",
        user_background="intermediate"
    )
    explanation = await debug_system.explain_concept(explanation_request)
    print(explanation)

    # Demo 4: Interactive troubleshooting
    print("\n[TOOLS] Interactive Troubleshooting...")
    troubleshooting = await debug_system.interactive_troubleshooting(
        "Strategy is generating too many false signals"
    )
    print(troubleshooting)

    # Demo 5: Strategy failure analysis
    print("\n[DEBUG] Strategy Failure Analysis...")
    failure_analysis = await debug_system.analyze_strategy_failure(
        "RSI_Momentum",
        {
            "type": "signal_failure",
            "timestamp": datetime.now().isoformat(),
            "impact": "High",
            "details": "Multiple false breakout signals"
        }
    )
    print(failure_analysis)

    print("\n[SUCCESS] Demo completed!")

if __name__ == "__main__":
    asyncio.run(demo_interactive_debugging())
