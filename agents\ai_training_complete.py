#!/usr/bin/env python3
"""
AI Training Agent - Complete Integration
Master script that integrates all AI Training Agent components
"""

import os
import sys
import asyncio
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import all AI Training Agent components
from ai_training_agent import AI<PERSON>rainingAgent, AITrainingConfig
from ai_training_monitor import AITrainingMonitor, MonitoringConfig
from ai_training_scheduler import AITrainingScheduler, SchedulerConfig
from ai_training_benchmark import AITrainingBenchmark, BenchmarkConfig
from ai_training_utils import setup_logging, get_system_info, check_dependencies

logger = logging.getLogger(__name__)

class AITrainingComplete:
    """
    Complete AI Training Agent System
    
    Integrates all components:
    - Training Agent (LightGBM + TabNet ensemble)
    - Monitoring (drift detection, performance tracking)
    - Scheduling (automated retraining)
    - Benchmarking (performance analysis)
    - API Serving (FastAPI endpoints)
    """
    
    def __init__(self):
        """Initialize complete system"""
        self.agent = None
        self.monitor = None
        self.scheduler = None
        self.benchmark = None
        
        logger.info("[AGENT] AI Training Complete System initialized")
    
    async def setup_system(self, config_path: str = "config/ai_training_config.yaml") -> None:
        """Setup all system components"""
        logger.info("[CONFIG] Setting up AI Training system...")
        
        # Check system requirements
        system_info = get_system_info()
        dependencies = check_dependencies()
        
        logger.info(f"[SYSTEM] System: {system_info['cpu_count']} cores, {system_info['memory_total_gb']:.1f}GB RAM")
        logger.info(f"🎮 GPU: {'Available' if system_info['gpu_available'] else 'Not Available'}")
        
        # Check dependencies
        missing_deps = [dep for dep, available in dependencies.items() if not available]
        if missing_deps:
            logger.warning(f"[WARN]  Missing dependencies: {missing_deps}")
        
        # Initialize components
        self.agent = AITrainingAgent()
        self.monitor = AITrainingMonitor()
        self.scheduler = AITrainingScheduler()
        self.benchmark = AITrainingBenchmark()
        
        logger.info("[SUCCESS] System setup completed")
    
    async def train_models(self, data_file: Optional[str] = None,
                          optimize_hyperparams: bool = True) -> Dict[str, Any]:
        """Train AI models"""
        logger.info("[INIT] Starting model training...")
        
        if not self.agent:
            raise ValueError("System not initialized. Call setup_system() first.")
        
        # Train models
        results = await self.agent.train_async(data_file, optimize_hyperparams)
        
        logger.info("[SUCCESS] Model training completed")
        return results
    
    async def monitor_performance(self, data_file: str) -> Dict[str, Any]:
        """Monitor model performance and detect drift"""
        logger.info("[DEBUG] Monitoring model performance...")
        
        if not self.monitor or not self.agent:
            raise ValueError("System not initialized. Call setup_system() first.")
        
        # Load data for monitoring
        import polars as pl
        df = pl.read_parquet(data_file)
        
        # Split data for baseline vs current comparison
        split_point = len(df) // 2
        baseline_data = df[:split_point]
        current_data = df[split_point:]
        
        # Generate monitoring report
        report = self.monitor.generate_monitoring_report(self.agent, current_data, baseline_data)
        
        logger.info("[SUCCESS] Performance monitoring completed")
        return report
    
    async def run_benchmark(self) -> Dict[str, Any]:
        """Run performance benchmark"""
        logger.info("[RUNNING] Running performance benchmark...")
        
        if not self.benchmark:
            raise ValueError("System not initialized. Call setup_system() first.")
        
        # Run comprehensive benchmark
        results = await self.benchmark.run_comprehensive_benchmark()
        
        logger.info("[SUCCESS] Benchmark completed")
        return results
    
    async def start_scheduler(self) -> None:
        """Start automated retraining scheduler"""
        logger.info("📅 Starting automated scheduler...")
        
        if not self.scheduler:
            raise ValueError("System not initialized. Call setup_system() first.")
        
        # Run scheduler (this will block)
        await self.scheduler.run_scheduler()
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform system health check"""
        logger.info("[HEALTH] Performing health check...")
        
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'system_info': get_system_info(),
            'dependencies': check_dependencies(),
            'components': {
                'agent_initialized': self.agent is not None,
                'agent_trained': self.agent.is_trained if self.agent else False,
                'monitor_initialized': self.monitor is not None,
                'scheduler_initialized': self.scheduler is not None,
                'benchmark_initialized': self.benchmark is not None
            },
            'status': 'healthy'
        }
        
        # Check for issues
        missing_deps = [dep for dep, available in health_status['dependencies'].items() if not available]
        if missing_deps:
            health_status['status'] = 'warning'
            health_status['issues'] = [f"Missing dependencies: {missing_deps}"]
        
        if not health_status['components']['agent_trained']:
            health_status['status'] = 'warning'
            health_status.setdefault('issues', []).append("Models not trained")
        
        logger.info(f"[SUCCESS] Health check completed: {health_status['status']}")
        return health_status
    
    async def full_workflow_demo(self, data_file: str) -> None:
        """Demonstrate complete workflow"""
        print("\n[AGENT] AI TRAINING AGENT - COMPLETE WORKFLOW DEMO")
        print("="*60)
        
        # 1. Setup system
        print("\n1️⃣  Setting up system...")
        await self.setup_system()
        
        # 2. Health check
        print("\n2️⃣  Performing health check...")
        health = await self.health_check()
        print(f"   Status: {health['status']}")
        
        # 3. Train models
        print("\n3️⃣  Training models...")
        train_results = await self.train_models(data_file, optimize_hyperparams=False)  # Fast training for demo
        print(f"   Overall R²: {train_results['evaluation_metrics']['overall']['r2']:.4f}")
        
        # 4. Monitor performance
        print("\n4️⃣  Monitoring performance...")
        monitor_report = await self.monitor_performance(data_file)
        print(f"   Alerts: {len(monitor_report['alerts'])}")
        print(f"   Recommendations: {len(monitor_report['recommendations'])}")
        
        # 5. Run benchmark (small scale for demo)
        print("\n5️⃣  Running benchmark...")
        # Configure smaller benchmark for demo
        self.benchmark.config.test_data_sizes = [100, 500]
        self.benchmark.config.benchmark_runs = 1
        benchmark_results = await self.run_benchmark()
        print(f"   Configurations tested: {benchmark_results['configurations_tested']}")
        
        # 6. Make sample predictions
        print("\n6️⃣  Making sample predictions...")
        import numpy as np
        sample_features = np.random.randn(3, len(self.agent.config.feature_columns))
        predictions, confidence = self.agent.predict(sample_features)
        rankings = self.agent.rank_strategies(predictions, ['Strategy_A', 'Strategy_B', 'Strategy_C'])
        
        print("   Top strategy rankings:")
        for i, ranking in enumerate(rankings):
            print(f"   {i+1}. {ranking['strategy_name']}: {ranking['composite_score']:.4f}")
        
        print("\n[SUCCESS] Complete workflow demo finished!")

async def main():
    """Main execution with command-line interface"""
    parser = argparse.ArgumentParser(
        description='AI Training Agent - Complete System',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Full workflow demo
  python ai_training_complete.py --demo --data-file data/backtest/enhanced_strategy_results.parquet
  
  # Train models only
  python ai_training_complete.py --train --data-file data/backtest/enhanced_strategy_results.parquet
  
  # Monitor performance
  python ai_training_complete.py --monitor --data-file data/backtest/enhanced_strategy_results.parquet
  
  # Run benchmark
  python ai_training_complete.py --benchmark
  
  # Start scheduler
  python ai_training_complete.py --scheduler
  
  # Health check
  python ai_training_complete.py --health
        """
    )
    
    # Action arguments
    parser.add_argument('--demo', action='store_true', help='Run complete workflow demo')
    parser.add_argument('--train', action='store_true', help='Train models')
    parser.add_argument('--monitor', action='store_true', help='Monitor performance')
    parser.add_argument('--benchmark', action='store_true', help='Run benchmark')
    parser.add_argument('--scheduler', action='store_true', help='Start scheduler')
    parser.add_argument('--health', action='store_true', help='Perform health check')
    
    # Data arguments
    parser.add_argument('--data-file', type=str, help='Path to backtesting data file')
    parser.add_argument('--config', type=str, default='config/ai_training_config.yaml', help='Configuration file')
    
    # Training arguments
    parser.add_argument('--no-optimize', action='store_true', help='Skip hyperparameter optimization')
    
    # Logging arguments
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='Logging level')
    parser.add_argument('--log-file', type=str, help='Log file path')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level, args.log_file)
    
    # Initialize complete system
    system = AITrainingComplete()
    
    try:
        if args.demo:
            if not args.data_file:
                print("[ERROR] --data-file required for demo")
                return
            await system.full_workflow_demo(args.data_file)
        
        elif args.train:
            if not args.data_file:
                print("[ERROR] --data-file required for training")
                return
            await system.setup_system(args.config)
            results = await system.train_models(args.data_file, not args.no_optimize)
            print(f"[SUCCESS] Training completed. Overall R²: {results['evaluation_metrics']['overall']['r2']:.4f}")
        
        elif args.monitor:
            if not args.data_file:
                print("[ERROR] --data-file required for monitoring")
                return
            await system.setup_system(args.config)
            report = await system.monitor_performance(args.data_file)
            print(f"[SUCCESS] Monitoring completed. Alerts: {len(report['alerts'])}")
        
        elif args.benchmark:
            await system.setup_system(args.config)
            results = await system.run_benchmark()
            print(f"[SUCCESS] Benchmark completed. Configurations tested: {results['configurations_tested']}")
        
        elif args.scheduler:
            await system.setup_system(args.config)
            print("📅 Starting scheduler (Ctrl+C to stop)...")
            await system.start_scheduler()
        
        elif args.health:
            await system.setup_system(args.config)
            health = await system.health_check()
            print(f"[SUCCESS] Health check: {health['status']}")
            if 'issues' in health:
                print(f"[WARN]  Issues: {health['issues']}")
        
        else:
            parser.print_help()
    
    except KeyboardInterrupt:
        logger.info("⏹️  Stopped by user")
    except Exception as e:
        logger.error(f"[ERROR] Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
