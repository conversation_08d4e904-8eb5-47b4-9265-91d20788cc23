# 🧠 Enhanced AI Training Agent Documentation

## Overview

The Enhanced AI Training Agent is a comprehensive multi-task learning system designed for options trading strategy optimization. It implements advanced machine learning techniques with real-time prediction capabilities, model explainability, and continual learning.

## 🎯 Multi-Task Learning Objectives

### 1. Trade Direction Prediction
- **Type**: Classification
- **Classes**: Buy Call, Buy Put, No Trade
- **Purpose**: Determines optimal trade direction based on market conditions
- **Weight**: 25%

### 2. Profitability Prediction
- **Type**: Binary Classification
- **Classes**: Profitable, Unprofitable
- **Purpose**: Predicts whether a trade will be profitable
- **Weight**: 20%

### 3. Signal Confidence Estimation
- **Type**: Regression
- **Range**: 0-1
- **Purpose**: Provides confidence score for trading signals
- **Weight**: 15%

### 4. Expected ROI Estimation
- **Type**: Regression
- **Purpose**: Predicts expected return on investment
- **Weight**: 20%

### 5. Strategy Selection
- **Type**: Multi-class Classification
- **Purpose**: Selects optimal strategy from 25+ available strategies
- **Weight**: 10%

### 6. Regime Classification
- **Type**: Classification
- **Classes**: Trending Up, Trending Down, Sideways, Volatile
- **Purpose**: Classifies current market regime
- **Weight**: 10%

## 🔧 Enhanced Features

### Model Architectures
- **LightGBM**: Primary gradient boosting model
- **XGBoost**: Secondary gradient boosting with different optimization
- **CatBoost**: Categorical feature handling specialist
- **TabNet**: Deep learning for tabular data
- **Random Forest**: Ensemble baseline
- **MLP**: Multi-layer perceptron for non-linear patterns
- **LSTM**: Sequential pattern recognition (future enhancement)

### Feature Categories

#### Technical Indicators
- Moving averages (SMA, EMA)
- Momentum indicators (RSI, MACD, Stochastic)
- Volatility indicators (Bollinger Bands, ATR)
- Volume indicators (MFI, VWAP)

#### Option-Specific Features
- Greeks (Delta, Gamma, Theta, Vega, Rho)
- Implied volatility metrics
- Time decay factors
- Moneyness calculations
- Open interest and volume

#### Time-Based Features
- Hour of day, day of week
- Time to expiry
- Market session indicators
- Expiry proximity flags

#### Market Regime Features
- VIX levels and changes
- Trend strength indicators
- Volatility regime classification
- Correlation regime analysis

#### Strategy Meta-Features
- Strategy performance history
- Complexity scores
- Risk category classifications
- Regime suitability ratings

## 🚀 Usage

### Basic Training

```python
from agents.ai_training_agent import AITrainingAgent, AITrainingConfig

# Initialize with default configuration
agent = AITrainingAgent()

# Train all models
results = await agent.train_enhanced_models("data/backtest/training_data.parquet")
```

### Custom Configuration

```python
# Create custom configuration
config = AITrainingConfig()
config.enabled_models = ["lightgbm", "xgboost", "tabnet"]
config.cv_folds = 5
config.optuna_trials = 50

# Initialize agent
agent = AITrainingAgent(config)
```

### Live Prediction

```python
# Prepare features
features = {
    'sma_20': 150.0,
    'rsi_14': 65.0,
    'delta': 0.5,
    'gamma': 0.05,
    'implied_volatility': 0.25,
    'hour_of_day': 10,
    'days_to_expiry': 7
}

# Get predictions for all tasks
predictions = await agent.predict_live(features)

# Get prediction for specific task
trade_prediction = await agent.predict_live(features, "trade_direction")
```

### Using the Runner Script

```bash
# Basic training
python agents/run_enhanced_ai_training.py

# With custom configuration
python agents/run_enhanced_ai_training.py --config config/custom_config.yaml

# With custom data file
python agents/run_enhanced_ai_training.py --data data/my_training_data.parquet

# Run with prediction demo
python agents/run_enhanced_ai_training.py --demo

# Don't save results
python agents/run_enhanced_ai_training.py --no-save
```

## 📊 Configuration

### YAML Configuration Structure

```yaml
# Multi-task objectives
multi_task_objectives:
  trade_direction:
    type: "classification"
    classes: ["buy_call", "buy_put", "no_trade"]
    target_column: "trade_direction"
    weight: 0.25

# Model configuration
models:
  enabled_models:
    - "lightgbm"
    - "xgboost"
    - "tabnet"
  
  ensemble_weights:
    lightgbm: 0.30
    xgboost: 0.25
    tabnet: 0.25

# Training configuration
training:
  cv_folds: 5
  cv_strategy: "time_series"
  use_gpu: true
```

## 🔍 Model Explainability

### SHAP Integration
- Global feature importance
- Local prediction explanations
- Feature interaction analysis

### LIME Integration
- Instance-level explanations
- Feature contribution analysis
- Model-agnostic interpretability

### Feature Importance
- Tree-based model importance
- Permutation importance
- Correlation analysis

## 📚 Model Registry & Versioning

### Automatic Versioning
- Timestamp-based version IDs
- Metadata tracking
- Performance metrics storage

### Model Storage
- Individual model serialization
- Ensemble model packaging
- Explanation artifacts

### Registry Management
- Version cleanup
- Performance comparison
- Model rollback capabilities

## 🔄 Feedback Integration & Continual Learning

### Drift Detection
- Statistical drift tests (KS test)
- Performance monitoring
- Automatic alerts

### Retraining Triggers
- Performance degradation
- Concept drift detection
- Time-based schedules

### Feedback Sources
- Execution Agent
- Performance Analysis Agent
- Market Monitoring Agent

## 🧠 Meta-Learning Features

### Strategy Selection
- Optimal strategy recommendation
- Regime-based selection
- Performance-driven choices

### Holding Period Optimization
- Optimal timing decisions
- Risk-adjusted returns
- Market condition adaptation

### Risk-Reward Optimization
- Dynamic RR ratio adjustment
- Expectancy maximization
- Drawdown minimization

## 🤖 LLM Integration

### Automated Insights
- Model performance summaries
- Feature importance explanations
- Trading recommendations

### Natural Language Outputs
- Human-readable reports
- Strategy explanations
- Alert descriptions

## 🚨 Monitoring & Alerts

### Performance Monitoring
- Real-time metrics tracking
- Degradation detection
- Automated notifications

### System Health
- Resource utilization
- Error rate monitoring
- Latency tracking

## 🧪 Testing

### Unit Tests
```bash
# Run all tests
pytest test/test_enhanced_ai_training_agent.py -v

# Run specific test
pytest test/test_enhanced_ai_training_agent.py::TestEnhancedAITrainingAgent::test_config_initialization -v
```

### Integration Tests
```bash
# Test with sample data
pytest test/test_enhanced_ai_training_agent.py::TestEnhancedAITrainingAgent::test_prepare_multi_task_data -v
```

## 📈 Performance Optimization

### GPU Acceleration
- CUDA support for compatible models
- Memory optimization
- Batch processing

### Parallel Processing
- Multi-core training
- Concurrent model training
- Async prediction serving

### Caching
- Feature caching
- Model caching
- Prediction caching

## 🔧 Troubleshooting

### Common Issues

1. **GPU Not Detected**
   - Check CUDA installation
   - Verify PyTorch CUDA support
   - Set `use_gpu: false` in config

2. **Memory Issues**
   - Reduce batch sizes
   - Enable data chunking
   - Limit concurrent models

3. **Poor Performance**
   - Check feature quality
   - Increase training data
   - Tune hyperparameters

### Debug Mode
```python
import logging
logging.getLogger('agents.ai_training_agent').setLevel(logging.DEBUG)
```

## 📝 API Reference

### AITrainingAgent Class

#### Methods
- `train_enhanced_models(data_file)`: Main training method
- `predict_live(features, task_name)`: Live prediction
- `prepare_multi_task_data(data)`: Data preparation
- `generate_model_explanations(task_data)`: Explainability
- `save_models_to_registry(results, explanations)`: Model storage

#### Properties
- `config`: Configuration object
- `task_models`: Trained models by task
- `ensemble_models`: Ensemble models
- `meta_models`: Meta-learning models

### AITrainingConfig Class

#### Key Parameters
- `multi_task_objectives`: Task definitions
- `enabled_models`: Model list
- `cv_strategy`: Cross-validation method
- `shap_enabled`: Explainability toggle
- `model_versioning`: Registry toggle

## 🔗 Integration

### Agent Communication
- HTTP-based communication
- Async message passing
- Event-driven updates

### Data Pipeline
- Feature Engineering Agent integration
- Backtesting Agent data flow
- Market Monitoring Agent feeds

### External Services
- Telegram notifications
- Email alerts
- Webhook endpoints
