#!/usr/bin/env python3
"""
AI Training Agent Example
Demonstrates complete usage of the AI Training Agent
"""

import os
import sys
import asyncio
import numpy as np
import polars as pl
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.ai_training_agent import AITrainingAgent, AITrainingConfig
from agents.ai_training_utils import setup_logging, get_system_info

async def main():
    """Complete example of AI Training Agent usage"""
    
    # Setup logging
    setup_logging("INFO", "logs/ai_training_example.log")
    
    print("🤖 AI Training Agent Example")
    print("="*50)
    
    # Check system info
    system_info = get_system_info()
    print(f"💻 System: {system_info['cpu_count']} cores, {system_info['memory_total_gb']:.1f}GB RAM")
    print(f"🎮 GPU: {'Available' if system_info['gpu_available'] else 'Not Available'}")
    
    # Create sample data if it doesn't exist
    data_file = "data/backtest/enhanced_strategy_results.parquet"
    if not os.path.exists(data_file):
        print("\n📊 Creating sample backtesting data...")
        create_sample_data(data_file)
    
    # Example 1: Basic Training
    print("\n🎯 Example 1: Basic Training")
    print("-" * 30)
    
    # Create configuration
    config = AITrainingConfig(
        optuna_trials=10,  # Reduced for example
        use_gpu=system_info['gpu_available']
    )
    
    # Initialize agent
    agent = AITrainingAgent(config)
    
    # Train models
    print("🚀 Training models...")
    results = await agent.train_async(
        file_path=data_file,
        optimize_hyperparams=True
    )
    
    print(f"✅ Training completed!")
    print(f"   Overall R²: {results['evaluation_metrics']['overall']['r2']:.4f}")
    print(f"   Overall RMSE: {results['evaluation_metrics']['overall']['rmse']:.4f}")
    
    # Example 2: Making Predictions
    print("\n🔮 Example 2: Making Predictions")
    print("-" * 30)
    
    # Create sample strategy features
    sample_strategies = [
        {
            'name': 'Conservative_Strategy',
            'features': [25, 1.5, 0.02, 0.9, 0.15, 0, 0.2, 3.0, 15, 10, 120.0, -80.0, 800.0, 0.015]
        },
        {
            'name': 'Aggressive_Strategy', 
            'features': [80, 3.0, 0.08, 0.7, 0.35, 1, 0.5, 8.0, 45, 35, 200.0, -150.0, 2500.0, 0.05]
        },
        {
            'name': 'Balanced_Strategy',
            'features': [50, 2.0, 0.05, 0.8, 0.25, 2, 0.3, 5.0, 30, 20, 150.0, -100.0, 1500.0, 0.03]
        }
    ]
    
    # Prepare feature matrix
    feature_matrix = np.array([strategy['features'] for strategy in sample_strategies])
    strategy_names = [strategy['name'] for strategy in sample_strategies]
    
    # Make predictions
    predictions, confidence = agent.predict(feature_matrix)
    
    print("📊 Individual Predictions:")
    for i, (name, pred, conf) in enumerate(zip(strategy_names, predictions, confidence)):
        print(f"\n{i+1}. {name}")
        print(f"   Confidence: {conf:.3f}")
        for j, target in enumerate(config.target_columns):
            print(f"   {target}: {pred[j]:.4f}")
    
    # Example 3: Strategy Ranking
    print("\n🏆 Example 3: Strategy Ranking")
    print("-" * 30)
    
    # Test different market regimes
    market_regimes = ['bull', 'bear', 'sideways']
    
    for regime in market_regimes:
        print(f"\n📈 Market Regime: {regime.upper()}")
        rankings = agent.rank_strategies(predictions, strategy_names, regime)
        
        for i, ranking in enumerate(rankings):
            print(f"   {i+1}. {ranking['strategy_name']}")
            print(f"      Composite Score: {ranking['composite_score']:.4f}")
            print(f"      Predicted ROI: {ranking['predicted_metrics']['ROI']:.4f}")
            print(f"      Predicted Sharpe: {ranking['predicted_metrics']['sharpe_ratio']:.4f}")
    
    # Example 4: Feature Importance Analysis
    print("\n🔍 Example 4: Feature Importance Analysis")
    print("-" * 30)
    
    if hasattr(agent, 'feature_importance') and agent.feature_importance:
        from agents.ai_training_utils import calculate_feature_importance_summary
        
        avg_importance = calculate_feature_importance_summary(agent.feature_importance)
        
        print("📊 Top 10 Most Important Features:")
        for i, (feature, importance) in enumerate(list(avg_importance.items())[:10]):
            print(f"   {i+1:2d}. {feature}: {importance:.4f}")
    
    # Example 5: Model Persistence
    print("\n💾 Example 5: Model Persistence")
    print("-" * 30)
    
    # Save models
    model_name = "example_ensemble"
    agent.save_models(model_name)
    print(f"✅ Models saved as: {model_name}")
    
    # Create new agent and load models
    new_agent = AITrainingAgent()
    new_agent.load_models(model_name)
    print(f"✅ Models loaded successfully")
    
    # Verify loaded models work
    test_predictions, test_confidence = new_agent.predict(feature_matrix[:1])
    print(f"✅ Loaded model prediction: ROI={test_predictions[0][0]:.4f}")
    
    # Example 6: Batch Processing
    print("\n⚡ Example 6: Batch Processing")
    print("-" * 30)
    
    # Generate larger batch of strategies
    np.random.seed(42)
    n_strategies = 20
    batch_features = np.random.randn(n_strategies, len(config.feature_columns))
    batch_names = [f'Batch_Strategy_{i}' for i in range(n_strategies)]
    
    # Process batch
    batch_predictions, batch_confidence = agent.predict(batch_features)
    batch_rankings = agent.rank_strategies(batch_predictions, batch_names, 'normal')
    
    print(f"📊 Processed {n_strategies} strategies")
    print("🏆 Top 5 Strategies:")
    for i, ranking in enumerate(batch_rankings[:5]):
        print(f"   {i+1}. {ranking['strategy_name']}: {ranking['composite_score']:.4f}")
    
    print("\n🎉 Example completed successfully!")
    print("="*50)

def create_sample_data(file_path: str):
    """Create sample backtesting data for demonstration"""
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    np.random.seed(42)
    n_samples = 1000
    
    # Generate realistic backtesting data
    data = {
        'strategy_name': [f'Strategy_{i}' for i in range(n_samples)],
        'symbol': np.random.choice(['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN'], n_samples),
        'timeframe': np.random.choice(['5min', '15min', '1h'], n_samples),
        'n_trades': np.random.randint(10, 200, n_samples),
        'ROI': np.random.normal(0.05, 0.2, n_samples),
        'accuracy': np.random.uniform(0.3, 0.9, n_samples),
        'expectancy': np.random.normal(0.02, 0.15, n_samples),
        'sharpe_ratio': np.random.normal(1.0, 0.8, n_samples),
        'max_drawdown': np.random.uniform(0.02, 0.4, n_samples),
        'profit_factor': np.random.uniform(0.5, 3.0, n_samples),
        'avg_holding_period': np.random.uniform(0.5, 15, n_samples),
        'risk_reward_ratio': np.random.uniform(0.3, 4.0, n_samples),
        'capital_at_risk': np.random.uniform(0.005, 0.15, n_samples),
        'liquidity': np.random.uniform(0.3, 1.0, n_samples),
        'volatility': np.random.uniform(0.05, 0.8, n_samples),
        'market_regime': np.random.choice(['bull', 'bear', 'sideways'], n_samples),
        'correlation_index': np.random.uniform(-0.8, 0.9, n_samples),
        'drawdown_duration': np.random.uniform(0.5, 30, n_samples),
        'winning_trades': np.random.randint(3, 120, n_samples),
        'losing_trades': np.random.randint(2, 80, n_samples),
        'avg_win': np.random.uniform(50, 2000, n_samples),
        'avg_loss': np.random.uniform(-2000, -50, n_samples),
        'total_pnl': np.random.normal(500, 8000, n_samples),
        'position_size_pct': np.random.uniform(0.005, 0.12, n_samples),
        'is_profitable': np.random.choice([True, False], n_samples, p=[0.6, 0.4])
    }
    
    # Create correlations to make data more realistic
    for i in range(n_samples):
        # ROI correlations
        if data['ROI'][i] > 0.1:  # High ROI strategies
            data['sharpe_ratio'][i] = max(data['sharpe_ratio'][i], 0.8)
            data['profit_factor'][i] = max(data['profit_factor'][i], 1.2)
            data['accuracy'][i] = max(data['accuracy'][i], 0.5)
        
        # Risk correlations
        if data['max_drawdown'][i] > 0.2:  # High risk strategies
            data['volatility'][i] = max(data['volatility'][i], 0.3)
            data['risk_reward_ratio'][i] = max(data['risk_reward_ratio'][i], 1.5)
        
        # Trade count correlations
        if data['n_trades'][i] > 100:  # High frequency strategies
            data['avg_holding_period'][i] = min(data['avg_holding_period'][i], 3.0)
    
    # Create DataFrame and save
    df = pl.DataFrame(data)
    df.write_parquet(file_path, compression='brotli')
    
    print(f"✅ Sample data created: {file_path}")
    print(f"   Rows: {len(df):,}")
    print(f"   Columns: {len(df.columns)}")

if __name__ == "__main__":
    asyncio.run(main())
