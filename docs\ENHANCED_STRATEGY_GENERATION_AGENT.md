# 🧠 Enhanced Strategy Generation Agent

## Overview

The Enhanced Strategy Generation Agent is an advanced trading strategy creation and management system that incorporates regime-aware filtering, event-driven strategies, time-based routing, ensemble scoring, and comprehensive risk management.

## 🌟 Key Features

### 1. 🧠 Regime-Aware Strategy Tagging
- **Market Regime Detection**: Automatically detects market conditions (bullish, bearish, sideways, volatile, calm)
- **Volatility Regime Classification**: Identifies volatility environments (low IV, high IV, IV expansion, IV crush)
- **Conditional Strategy Activation**: Strategies are only activated when market conditions match their requirements

### 2. 📅 Event-Driven Strategies
- **Weekly/Monthly Expiry Strategies**: Special strategies for options expiry days
- **Economic Event Strategies**: RBI policy days, budget announcements
- **Market Event Detection**: Gap-up/gap-down detection and routing
- **Dynamic Event Activation**: Strategies automatically activate based on detected events

### 3. ⏰ Time-of-Day Based Strategy Routing
- **Pre-Market (9:00-9:15)**: Gap detection and pre-market momentum strategies
- **Opening Range (9:15-10:15)**: Breakout and opening range strategies
- **Morning Session (10:15-11:30)**: Momentum and trend-following strategies
- **Midday Lull (11:30-13:30)**: Mean reversion and range trading strategies
- **Afternoon Session (13:30-14:30)**: Trend continuation strategies
- **Power Hour (14:30-15:15)**: High-momentum and closing strategies

### 4. 🎯 Inter-Strategy Ensemble Scoring
- **Voting Mechanisms**: Multiple strategies vote on signal direction
- **Confidence Aggregation**: Combines confidence scores from multiple strategies
- **Conflict Resolution**: Resolves conflicting signals using confidence thresholds
- **Performance Weighting**: Weights strategies based on historical performance

### 5. ⚠️ Risk-Aware Strategy Filtering
- **Capital at Risk Calculation**: Monitors total capital exposure per strategy
- **Expected Drawdown Estimation**: Predicts potential losses before signal generation
- **Dynamic Strategy Suppression**: Automatically disables poor-performing strategies
- **Risk-Reward Ratio Filtering**: Only allows signals with favorable risk-reward ratios

### 6. 📊 Strategy Meta-data Versioning
- **30-Day Performance Tracking**: Monitors recent strategy performance
- **Hit Ratio by Timeframe**: Tracks success rates across different timeframes
- **Ideal Holding Period Analysis**: Determines optimal holding periods for each strategy
- **Volatility Range Tracking**: Identifies optimal volatility conditions for each strategy

### 7. 🔄 Synthetic Strategy Creation Module
- **Auto-Generation**: Creates new strategies by combining existing ones
- **Backtesting Integration**: Tests new strategies before deployment
- **Automatic Naming and Versioning**: Manages strategy lifecycle automatically
- **Performance-Based Evolution**: Evolves strategies based on market feedback

### 8. 🏷️ Strategy Intent Tags
- **Objective-Based Tagging**: Categorizes strategies by purpose (breakout, mean reversion, etc.)
- **Confidence Type Classification**: Identifies strategy validation method (backtested, AI-scored, etc.)
- **Asset-Specific Tagging**: Specifies which assets each strategy is designed for

### 9. 🔍 Multi-Layer Filtering Pipeline
- **Strategy Trigger Validation**: Ensures all conditions are met
- **Confidence + Risk Scoring**: Applies comprehensive scoring
- **Time + Regime Compatibility**: Checks time and market regime alignment
- **Final Signal Generation**: Produces validated, high-quality signals

## 📁 File Structure

```
agents/
├── strategy_generation_agent.py          # Main agent implementation
├── run_enhanced_strategy_generation.py   # Test runner and examples
config/
├── enhanced_strategies.yaml              # Agent configuration
├── enhanced_strategies_metadata.yaml     # Strategy definitions
docs/
├── ENHANCED_STRATEGY_GENERATION_AGENT.md # This documentation
```

## 🚀 Quick Start

### 1. Installation

```python
# Import the agent
from agents.strategy_generation_agent import EnhancedStrategyGenerationAgent

# Initialize the agent
agent = EnhancedStrategyGenerationAgent()
await agent.initialize()
```

### 2. Basic Usage

```python
# Generate signals
market_data = {
    'open': [100, 101, 102],
    'high': [102, 103, 104],
    'low': [99, 100, 101],
    'close': [101, 102, 103],
    'volume': [1000, 1100, 1200]
}

indicators = {
    'rsi_14': [45, 50, 55],
    'ema_20': [100, 101, 102],
    'vwap': [100.5, 101.5, 102.5],
    'atr_14': [1.0, 1.1, 1.2]
}

signals = await agent.generate_signals(
    market_data, 
    indicators, 
    'BANKNIFTY', 
    datetime.now()
)
```

### 3. Running Tests

```bash
cd agents
python run_enhanced_strategy_generation.py
```

## ⚙️ Configuration

### Main Configuration (`config/enhanced_strategies.yaml`)

Key configuration sections:

- **General Settings**: Basic agent parameters
- **Regime Detection**: Market and volatility regime parameters
- **Event Detection**: Event-driven strategy settings
- **Time Filtering**: Time-based routing configuration
- **Risk Management**: Risk filtering and position sizing
- **Ensemble Scoring**: Multi-strategy aggregation settings

### Strategy Metadata (`config/enhanced_strategies_metadata.yaml`)

Each strategy includes:

- **Basic Information**: ID, name, description
- **Asset Configuration**: Applicable assets and timeframes
- **Time/Event Configuration**: Trigger windows and event types
- **Regime Configuration**: Market and volatility regime requirements
- **Signal Logic**: Entry and exit conditions
- **Risk Management**: Stop loss, take profit, and risk limits
- **Performance Tracking**: Backtest results and live performance

## 📊 Strategy Examples

### Morning Breakout Strategy

```yaml
- id: "strat_001"
  name: "Morning Breakout IV Expansion"
  applicable_assets: ["BANKNIFTY"]
  timeframes: ["5min", "15min"]
  trigger_window: "09:15-10:15"
  market_regime: ["bullish", "volatile"]
  volatility_regime: ["low_IV"]
  signal_logic:
    long_conditions:
      - "close > high.shift(1)"
      - "volume > volume.rolling(20).mean() * 2"
      - "rsi_14 > 50"
```

### VWAP Mean Reversion Strategy

```yaml
- id: "strat_002"
  name: "VWAP Mean Reversion"
  applicable_assets: ["NIFTY", "BANKNIFTY"]
  trigger_window: "10:15-14:30"
  market_regime: ["sideways", "range_bound"]
  signal_logic:
    long_conditions:
      - "close < vwap * 0.998"
      - "rsi_14 < 35"
      - "volume > volume.rolling(20).mean() * 1.2"
```

## 🔧 Advanced Features

### Custom Strategy Creation

```python
# Create a new strategy
strategy = StrategyMetadata(
    id="custom_001",
    name="Custom Strategy",
    description="My custom trading strategy",
    applicable_assets=["NIFTY"],
    timeframes=["15min"],
    market_regime=[MarketRegimeType.BULLISH],
    signal_logic={
        'long_conditions': [
            'rsi_14 < 30',
            'close > ema_20'
        ]
    }
)

# Add to agent
agent.strategies[strategy.id] = strategy
```

### Performance Monitoring

```python
# Get strategy performance
stats = agent.strategy_stats['strat_001']
print(f"Win Rate: {stats['win_rate']:.2%}")
print(f"Sharpe Ratio: {stats['sharpe_ratio']:.2f}")
print(f"Max Drawdown: {stats['max_drawdown']:.2%}")
```

### Regime Detection

```python
# Check current market regime
regime = agent.current_market_regime
volatility_regime = agent.current_volatility_regime
confidence = agent.regime_confidence

print(f"Market Regime: {regime.value}")
print(f"Volatility Regime: {volatility_regime.value}")
print(f"Confidence: {confidence:.2%}")
```

## 📈 Performance Metrics

The agent tracks comprehensive performance metrics:

- **Win Rate**: Percentage of profitable signals
- **Profit Factor**: Ratio of gross profit to gross loss
- **Sharpe Ratio**: Risk-adjusted return measure
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Average Return**: Mean return per signal
- **Hit Ratio by Timeframe**: Success rate across different timeframes

## 🚨 Risk Management

### Automatic Risk Controls

- **Position Sizing**: Automatic position sizing based on volatility and capital
- **Stop Loss Calculation**: Dynamic stop losses based on ATR and strategy parameters
- **Risk-Reward Filtering**: Only signals with favorable risk-reward ratios are generated
- **Drawdown Monitoring**: Strategies are disabled if drawdown exceeds thresholds

### Performance-Based Filtering

- **Poor Performer Suppression**: Strategies with low win rates are automatically disabled
- **Confidence Thresholds**: Only high-confidence signals are generated
- **Regime Compatibility**: Strategies only activate in compatible market regimes

## 🔮 Future Enhancements

- **Machine Learning Integration**: AI-powered strategy optimization
- **Real-time Regime Prediction**: Predictive regime detection
- **Options Chain Integration**: Options-specific strategies and Greeks
- **Multi-Asset Correlation**: Cross-asset strategy coordination
- **Adaptive Thresholds**: Self-adjusting strategy parameters

## 📞 Support

For questions or issues:
1. Check the logs in `logs/enhanced_strategy_generation.log`
2. Review the configuration files for proper setup
3. Run the test suite to verify functionality
4. Consult the strategy metadata for strategy-specific issues
