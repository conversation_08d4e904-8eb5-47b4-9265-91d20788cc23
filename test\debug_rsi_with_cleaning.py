#!/usr/bin/env python3
"""
Debug script to test RSI_Reversal with the actual data cleaning process
"""

import polars as pl
import yaml
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_data_like_backtesting(df):
    """Apply the same data cleaning logic as the backtesting module"""
    
    # Identify all numerical columns that might contain NaNs
    numerical_cols = [
        'open', 'high', 'low', 'close', 'volume',
        'rsi_14', 'rsi_5', 'ema_5', 'ema_10', 'ema_13', 'ema_20', 'ema_21', 'ema_50',
        'vwap', 'supertrend', 'cpr_top', 'cpr_bottom', 'macd', 'macd_signal',
        'bb_upper', 'bb_lower', 'bb_middle', 'atr', 'adx', 'cci', 'mfi',
        'stoch_k', 'stoch_d', 'pivot', 'resistance', 'support',
        'donchian_high', 'donchian_low',
        'vcp_pattern', 'upward_candle', 'downward_candle'
    ]

    # Filter to only include columns actually present in the DataFrame
    cols_to_process = [col for col in numerical_cols if col in df.columns]

    # Apply a chain of operations: replace inf, replace non-positive (except for binary indicators), then fill nulls
    expressions = []
    # Binary indicator columns that can legitimately have 0 values
    binary_indicators = ['vcp_pattern', 'upward_candle', 'downward_candle']
    
    # Technical indicators that can have values between 0-100 (RSI, Stochastic, etc.)
    percentage_indicators = ['rsi_14', 'rsi_5', 'stoch_k', 'stoch_d', 'mfi']
    
    # Price-based columns that should be positive
    price_columns = ['open', 'high', 'low', 'close', 'volume', 'atr']
    
    for col in cols_to_process:
        if col in binary_indicators:
            # For binary indicators, replace infinite and NaN values, but not zeros
            expressions.append(
                pl.when(pl.col(col).is_infinite() | pl.col(col).is_nan())
                .then(pl.lit(None, dtype=pl.Float64))
                .otherwise(pl.col(col))
                .fill_null(strategy='forward')
                .fill_null(strategy='backward')
                .alias(col)
            )
        elif col in percentage_indicators:
            # For percentage indicators (RSI, Stochastic, etc.), replace infinite, NaN, and values outside valid range
            expressions.append(
                pl.when(pl.col(col).is_infinite() | pl.col(col).is_nan() | (pl.col(col) < 0) | (pl.col(col) > 100))
                .then(pl.lit(None, dtype=pl.Float64))
                .otherwise(pl.col(col))
                .fill_null(strategy='forward')
                .fill_null(strategy='backward')
                .alias(col)
            )
        elif col in price_columns:
            # For price/volume columns, replace inf and non-positive values
            expressions.append(
                pl.when(pl.col(col).is_infinite() | (pl.col(col) <= 0))
                .then(pl.lit(None, dtype=pl.Float64))
                .otherwise(pl.col(col))
                .fill_null(strategy='forward')
                .fill_null(strategy='backward')
                .alias(col)
            )
        else:
            # For other technical indicators (EMA, MACD, etc.), replace infinite and NaN values
            expressions.append(
                pl.when(pl.col(col).is_infinite() | pl.col(col).is_nan())
                .then(pl.lit(None, dtype=pl.Float64))
                .otherwise(pl.col(col))
                .fill_null(strategy='forward')
                .fill_null(strategy='backward')
                .alias(col)
            )
    df = df.with_columns(expressions)
    
    # Drop any remaining nulls in critical columns that couldn't be filled (e.g., leading/trailing NaNs if all values were bad)
    df = df.drop_nulls(subset=['close', 'high', 'low', 'open', 'volume', 'datetime'])
    logger.info(f"DataFrame rows after critical nulls dropped: {len(df)}")
    
    return df

def test_with_data_cleaning():
    """Test RSI_Reversal with proper data cleaning"""
    
    # Load the 360ONE 15min data that was used in the test
    data_file = "c:/Users/<USER>/Documents/Equity/data/features/features_360ONE_15min.parquet"
    
    try:
        df = pl.read_parquet(data_file)
        logger.info(f"Original data: {len(df)} rows")
        
        # Sort by datetime (important for forward/backward fill)
        df = df.sort("datetime")
        
        # Apply data cleaning
        logger.info("Applying data cleaning...")
        df_cleaned = clean_data_like_backtesting(df)
        logger.info(f"Cleaned data: {len(df_cleaned)} rows")
        
        # Check for required columns for RSI_Reversal strategy
        required_cols = ['rsi_14', 'close', 'ema_10']
        missing_cols = [col for col in required_cols if col not in df_cleaned.columns]
        
        if missing_cols:
            logger.error(f"Missing required columns: {missing_cols}")
            return
        
        # Show basic statistics for required columns AFTER cleaning
        for col in required_cols:
            logger.info(f"\n{col} statistics (AFTER cleaning):")
            logger.info(f"  Min: {df_cleaned[col].min()}")
            logger.info(f"  Max: {df_cleaned[col].max()}")
            logger.info(f"  Mean: {df_cleaned[col].mean()}")
            logger.info(f"  Null count: {df_cleaned[col].is_null().sum()}")
            logger.info(f"  Sample values: {df_cleaned[col].head(10).to_list()}")
        
        # Test RSI_Reversal long condition: rsi_14 < 30 and close > ema_10
        long_condition = (df_cleaned['rsi_14'] < 30) & (df_cleaned['close'] > df_cleaned['ema_10'])
        long_signals = long_condition.sum()
        logger.info(f"\nRSI_Reversal LONG signals (AFTER cleaning): {long_signals}")
        
        if long_signals > 0:
            # Show some examples where condition is met
            long_examples = df_cleaned.filter(long_condition).head(5)
            logger.info("Long signal examples:")
            for i, row in enumerate(long_examples.iter_rows(named=True)):
                logger.info(f"  {i+1}. RSI: {row['rsi_14']:.2f}, Close: {row['close']:.2f}, EMA10: {row['ema_10']:.2f}, DateTime: {row['datetime']}")
        
        # Test RSI_Reversal short condition: rsi_14 > 70 and close < ema_10
        short_condition = (df_cleaned['rsi_14'] > 70) & (df_cleaned['close'] < df_cleaned['ema_10'])
        short_signals = short_condition.sum()
        logger.info(f"\nRSI_Reversal SHORT signals (AFTER cleaning): {short_signals}")
        
        if short_signals > 0:
            # Show some examples where condition is met
            short_examples = df_cleaned.filter(short_condition).head(5)
            logger.info("Short signal examples:")
            for i, row in enumerate(short_examples.iter_rows(named=True)):
                logger.info(f"  {i+1}. RSI: {row['rsi_14']:.2f}, Close: {row['close']:.2f}, EMA10: {row['ema_10']:.2f}, DateTime: {row['datetime']}")
        
        # Check RSI distribution AFTER cleaning
        rsi_below_30 = (df_cleaned['rsi_14'] < 30).sum()
        rsi_above_70 = (df_cleaned['rsi_14'] > 70).sum()
        logger.info(f"\nRSI distribution (AFTER cleaning):")
        logger.info(f"  RSI < 30: {rsi_below_30} ({rsi_below_30/len(df_cleaned)*100:.2f}%)")
        logger.info(f"  RSI > 70: {rsi_above_70} ({rsi_above_70/len(df_cleaned)*100:.2f}%)")
        
        # Check close vs ema_10 relationship AFTER cleaning
        close_above_ema = (df_cleaned['close'] > df_cleaned['ema_10']).sum()
        close_below_ema = (df_cleaned['close'] < df_cleaned['ema_10']).sum()
        logger.info(f"\nClose vs EMA10 (AFTER cleaning):")
        logger.info(f"  Close > EMA10: {close_above_ema} ({close_above_ema/len(df_cleaned)*100:.2f}%)")
        logger.info(f"  Close < EMA10: {close_below_ema} ({close_below_ema/len(df_cleaned)*100:.2f}%)")
        
        # Total signals
        total_signals = long_signals + short_signals
        logger.info(f"\nTotal signals (AFTER cleaning): {total_signals}")
        
        if total_signals > 0:
            logger.info("✅ SUCCESS: Signals are being generated after data cleaning!")
        else:
            logger.warning("❌ PROBLEM: Still no signals after data cleaning")
            
            # Try even more relaxed conditions
            logger.info("\nTrying more relaxed conditions...")
            relaxed_long = (df_cleaned['rsi_14'] < 35) & (df_cleaned['close'] > df_cleaned['ema_10'])
            relaxed_short = (df_cleaned['rsi_14'] > 65) & (df_cleaned['close'] < df_cleaned['ema_10'])
            logger.info(f"Relaxed long signals (RSI < 35): {relaxed_long.sum()}")
            logger.info(f"Relaxed short signals (RSI > 65): {relaxed_short.sum()}")
        
        return df_cleaned
        
    except Exception as e:
        logger.error(f"Error in test: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

if __name__ == "__main__":
    logger.info("Starting RSI_Reversal debug with data cleaning...")
    test_with_data_cleaning()
    logger.info("Debug analysis completed.")