strategies:
  - name: RSI_Reversal
    long: rsi_14 < 30 and close > ema_10
    short: rsi_14 > 70 and close < ema_10

  - name: MACD_Crossover
    long: macd > macd_signal and close > ema_20
    short: macd < macd_signal and close < ema_20

  - name: EMA_Stacking
    long: ema_5 > ema_13 and ema_13 > ema_21 and close > ema_5
    short: ema_5 < ema_13 and ema_13 < ema_21 and close < ema_5

  - name: <PERSON><PERSON><PERSON><PERSON>Bo<PERSON><PERSON>
    long: close < bb_lower and rsi_5 < 40
    short: close > bb_upper and rsi_5 > 60

  - name: VWAP_Bounce
    long: close < vwap and rsi_14 < 40 and close > ema_10
    short: close > vwap and rsi_14 > 60 and close < ema_10

  - name: Donchian_Break
    long: close >= donchian_high and volume > 1000
    short: close <= donchian_low and volume > 1000

  - name: Supertrend_Trend
    long: close > supertrend and rsi_5 > 50
    short: close < supertrend and rsi_5 < 50

  - name: CPR_Bounce
    long: close > cpr_bottom and close < cpr_top and rsi_14 < 50
    short: close < cpr_top and close > cpr_bottom and rsi_14 > 50

  - name: ADX_Momentum
    long: adx > 25 and close > ema_20
    short: adx > 25 and close < ema_20

  - name: VCP_Breakout
    long: vcp_pattern == 1 and upward_candle == 1 and volume > 1000 and rsi_14 > 50
    short: vcp_pattern == 1 and downward_candle == 1 and volume > 1000 and rsi_14 < 50
